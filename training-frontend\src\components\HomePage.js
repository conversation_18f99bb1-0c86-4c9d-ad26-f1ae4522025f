import React from 'react';
import styled from 'styled-components';
import {
  Brain,
  Camera,
  Users,
  Shield,
  Smartphone,
  Target,
  Play,
  BookOpen,
  Zap,
  Cpu,
  Eye,
  Award,
  TrendingUp,
  Globe
} from 'lucide-react';

const HomeContainer = styled.div`
  min-height: 100vh;
  background: var(--bg-primary);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.03) 0%, transparent 50%),
      radial-gradient(circle at 40% 60%, rgba(16, 185, 129, 0.02) 0%, transparent 50%);
    pointer-events: none;
    z-index: 0;
  }
`;

const Navigation = styled.nav`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-neural);
  padding: var(--space-4) 0;
  transition: var(--transition-normal);

  @media (max-width: 768px) {
    padding: var(--space-3) 0;
  }
`;

const NavContainer = styled.div`
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--space-6);
  display: flex;
  align-items: center;
  justify-content: space-between;

  @media (max-width: 768px) {
    padding: 0 var(--space-4);
  }
`;

const Logo = styled.div`
  font-family: var(--font-primary);
  font-size: 1.5rem;
  font-weight: 700;
  background: var(--text-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: flex;
  align-items: center;
  gap: var(--space-3);

  @media (max-width: 768px) {
    font-size: 1.25rem;
    gap: var(--space-2);
  }
`;

const LogoIcon = styled.div`
  width: 40px;
  height: 40px;
  background: var(--bg-neural);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: var(--shadow-neural);

  @media (max-width: 768px) {
    width: 36px;
    height: 36px;
  }
`;

const NavLinks = styled.div`
  display: flex;
  align-items: center;
  gap: var(--space-8);

  @media (max-width: 768px) {
    gap: var(--space-4);
  }
`;

const NavLink = styled.a`
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: 500;
  font-size: 0.9rem;
  transition: var(--transition-fast);
  position: relative;

  &:hover {
    color: var(--text-accent);
  }

  &::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--bg-neural);
    transition: var(--transition-normal);
  }

  &:hover::after {
    width: 100%;
  }

  @media (max-width: 768px) {
    font-size: 0.85rem;
  }
`;

const HeroSection = styled.section`
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-24) var(--space-6) var(--space-20);
  text-align: center;
  position: relative;
  z-index: 1;

  @media (max-width: 768px) {
    padding: var(--space-20) var(--space-4) var(--space-16);
    min-height: calc(100vh - 80px);
  }
`;

const HeroContent = styled.div`
  max-width: 900px;
  width: 100%;
  position: relative;
  z-index: 2;
`;

const HeroBadge = styled.div`
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  background: var(--bg-glass);
  border: 1px solid var(--border-neural);
  border-radius: var(--radius-full);
  padding: var(--space-2) var(--space-4);
  margin-bottom: var(--space-6);
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-accent);
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-glow);

  @media (max-width: 768px) {
    font-size: 0.8rem;
    padding: var(--space-2) var(--space-3);
  }
`;

const HeroTitle = styled.h1`
  font-family: var(--font-primary);
  font-size: 3.5rem;
  font-weight: 800;
  background: var(--text-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: var(--space-6);
  line-height: 1.1;
  letter-spacing: -0.02em;

  @media (max-width: 768px) {
    font-size: 2.5rem;
    margin-bottom: var(--space-4);
  }

  @media (max-width: 480px) {
    font-size: 2rem;
  }
`;

const HeroSubtitle = styled.p`
  font-size: 1.25rem;
  font-weight: 400;
  color: var(--text-secondary);
  margin-bottom: var(--space-8);
  line-height: 1.6;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;

  @media (max-width: 768px) {
    font-size: 1.125rem;
    margin-bottom: var(--space-6);
  }

  @media (max-width: 480px) {
    font-size: 1rem;
  }
`;

const HeroDescription = styled.p`
  font-size: 1rem;
  line-height: 1.7;
  color: var(--text-tertiary);
  margin-bottom: var(--space-10);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;

  @media (max-width: 768px) {
    font-size: 0.95rem;
    margin-bottom: var(--space-8);
    line-height: 1.6;
  }
`;

const HeroActions = styled.div`
  display: flex;
  gap: var(--space-5);
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: var(--space-16);

  @media (max-width: 768px) {
    gap: var(--space-4);
    margin-bottom: var(--space-12);
    flex-direction: column;
  }
`;

const PrimaryButton = styled.button`
  background: var(--bg-neural);
  color: white;
  border: none;
  padding: var(--space-4) var(--space-10);
  border-radius: var(--radius-xl);
  font-size: 1.125rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-normal);
  display: flex;
  align-items: center;
  gap: var(--space-3);
  box-shadow: var(--shadow-neural);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-slow);
  }

  &:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl), var(--shadow-glow);

    &::before {
      left: 100%;
    }
  }

  &:active {
    transform: translateY(-1px);
  }

  @media (max-width: 768px) {
    padding: var(--space-4) var(--space-8);
    font-size: 1rem;
    width: 100%;
    max-width: 300px;
  }
`;

const SecondaryButton = styled.button`
  background: var(--bg-glass);
  color: var(--text-primary);
  border: 2px solid var(--border-neural);
  padding: var(--space-4) var(--space-8);
  border-radius: var(--radius-xl);
  font-size: 1.125rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-normal);
  display: flex;
  align-items: center;
  gap: var(--space-3);
  backdrop-filter: blur(10px);

  &:hover {
    border-color: var(--primary-600);
    color: var(--primary-600);
    background: var(--primary-50);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
  }

  @media (max-width: 768px) {
    padding: var(--space-4) var(--space-6);
    font-size: 1rem;
    width: 100%;
    max-width: 300px;
  }
`;

const FeaturesSection = styled.section`
  padding: var(--space-24) var(--space-6);
  background: var(--bg-tertiary);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 30% 30%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
      radial-gradient(circle at 70% 70%, rgba(147, 51, 234, 0.05) 0%, transparent 50%);
    pointer-events: none;
  }

  @media (max-width: 768px) {
    padding: var(--space-20) var(--space-4);
  }
`;

const FeaturesContainer = styled.div`
  max-width: 1400px;
  margin: 0 auto;
  text-align: center;
  position: relative;
  z-index: 1;
`;

const SectionTitle = styled.h2`
  font-family: var(--font-primary);
  font-size: 2.75rem;
  font-weight: 700;
  background: var(--text-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: var(--space-6);
  letter-spacing: -0.02em;

  @media (max-width: 768px) {
    font-size: 2rem;
  }
`;

const SectionSubtitle = styled.p`
  font-size: 1.125rem;
  color: var(--text-secondary);
  margin-bottom: var(--space-16);
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;

  @media (max-width: 768px) {
    font-size: 1rem;
    margin-bottom: var(--space-12);
  }
`;

const FeatureGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: var(--space-8);
  margin-bottom: var(--space-20);

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }
`;

const FeatureCard = styled.div`
  background: var(--bg-glass);
  border-radius: var(--radius-2xl);
  padding: var(--space-10);
  border: 1px solid var(--border-neural);
  transition: var(--transition-normal);
  text-align: left;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-lg);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--bg-neural);
    transform: scaleX(0);
    transition: var(--transition-normal);
  }

  &:hover {
    transform: translateY(-6px);
    box-shadow: var(--shadow-xl), var(--shadow-glow);
    border-color: var(--primary-300);

    &::before {
      transform: scaleX(1);
    }
  }

  @media (max-width: 768px) {
    padding: var(--space-8);
    text-align: center;
  }
`;

const FeatureIconWrapper = styled.div`
  width: 64px;
  height: 64px;
  background: var(--bg-neural);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--space-6);
  box-shadow: var(--shadow-neural);

  @media (max-width: 768px) {
    margin: 0 auto var(--space-6);
  }
`;

const FeatureTitle = styled.h3`
  font-size: 1.5rem;
  margin-bottom: var(--space-4);
  color: var(--text-primary);
  font-weight: 700;
  font-family: var(--font-primary);

  @media (max-width: 768px) {
    text-align: center;
  }
`;

const FeatureDescription = styled.p`
  font-size: 1rem;
  color: var(--text-secondary);
  line-height: 1.7;
  font-weight: 400;

  @media (max-width: 768px) {
    text-align: center;
  }
`;

const HeroStats = styled.div`
  display: flex;
  justify-content: center;
  gap: var(--space-8);
  margin-top: var(--space-8);

  @media (max-width: 768px) {
    gap: var(--space-6);
    flex-wrap: wrap;
  }
`;

const StatItem = styled.div`
  text-align: center;
`;

const StatNumber = styled.div`
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-600);
  font-family: var(--font-primary);

  @media (max-width: 768px) {
    font-size: 1.5rem;
  }
`;

const StatLabel = styled.div`
  font-size: 0.875rem;
  color: var(--text-tertiary);
  font-weight: 500;
  margin-top: var(--space-1);
`;

const HomePage = ({ onStartTraining, onNavigateToAbout, onNavigateToContact }) => {
  return (
    <HomeContainer>
      <Navigation>
        <NavContainer>
          <Logo>
            <LogoIcon>
              <Brain size={24} />
            </LogoIcon>
            ASL Neural
          </Logo>
          <NavLinks>
            <NavLink href="#features">Features</NavLink>
            <NavLink onClick={onNavigateToAbout} style={{ cursor: 'pointer' }}>About</NavLink>
            <NavLink onClick={onNavigateToContact} style={{ cursor: 'pointer' }}>Contact</NavLink>
          </NavLinks>
        </NavContainer>
      </Navigation>

      <HeroSection>
        <HeroContent>
          <HeroBadge>
            <Zap size={16} />
            AI-Powered Learning Platform
          </HeroBadge>

          <HeroTitle>Master Sign Language with Neural Intelligence</HeroTitle>
          <HeroSubtitle>
            Revolutionary AI platform that transforms sign language learning through
            real-time computer vision and adaptive neural networks
          </HeroSubtitle>
          <HeroDescription>
            Experience the future of accessibility education. Our advanced machine learning
            algorithms provide instant feedback while contributing to breakthrough AI research
            for the deaf and hard-of-hearing community.
          </HeroDescription>

          <HeroActions>
            <PrimaryButton onClick={onStartTraining}>
              <Play size={20} />
              Start Neural Training
            </PrimaryButton>
            <SecondaryButton onClick={onNavigateToAbout}>
              <BookOpen size={20} />
              Explore Technology
            </SecondaryButton>
          </HeroActions>

          <HeroStats>
            <StatItem>
              <StatNumber>100K+</StatNumber>
              <StatLabel>Neural Sessions</StatLabel>
            </StatItem>
            <StatItem>
              <StatNumber>250+</StatNumber>
              <StatLabel>Sign Patterns</StatLabel>
            </StatItem>
            <StatItem>
              <StatNumber>+88.7%</StatNumber>
              <StatLabel>AI Accuracy</StatLabel>
            </StatItem>
          </HeroStats>
        </HeroContent>
      </HeroSection>

      <FeaturesSection id="features">
        <FeaturesContainer>
          <SectionTitle>Neural Network Capabilities</SectionTitle>
          <SectionSubtitle>
            Discover how our advanced AI technology revolutionizes sign language learning
            through cutting-edge computer vision and machine learning
          </SectionSubtitle>

          <FeatureGrid>
            <FeatureCard>
              <FeatureIconWrapper>
                <Camera size={28} color="white" />
              </FeatureIconWrapper>
              <FeatureTitle>Real-time Computer Vision</FeatureTitle>
              <FeatureDescription>
                Advanced neural networks analyze your hand movements in real-time, providing
                instant feedback with 98.7% accuracy using state-of-the-art pose estimation algorithms
              </FeatureDescription>
            </FeatureCard>

            <FeatureCard>
              <FeatureIconWrapper>
                <Cpu size={28} color="white" />
              </FeatureIconWrapper>
              <FeatureTitle>Adaptive AI Learning</FeatureTitle>
              <FeatureDescription>
                Our deep learning models continuously adapt to your learning style, creating
                personalized training paths that optimize skill acquisition and retention rates
              </FeatureDescription>
            </FeatureCard>

            <FeatureCard>
              <FeatureIconWrapper>
                <Globe size={28} color="white" />
              </FeatureIconWrapper>
              <FeatureTitle>Global Impact Network</FeatureTitle>
              <FeatureDescription>
                Join a worldwide community contributing to breakthrough AI research that advances
                accessibility technology for millions in the deaf and hard-of-hearing community
              </FeatureDescription>
            </FeatureCard>

            <FeatureCard>
              <FeatureIconWrapper>
                <Smartphone size={28} color="white" />
              </FeatureIconWrapper>
              <FeatureTitle>Cross-Platform Intelligence</FeatureTitle>
              <FeatureDescription>
                Seamless AI-powered experience across all devices with cloud-synchronized progress
                and edge computing for lightning-fast response times
              </FeatureDescription>
            </FeatureCard>

            <FeatureCard>
              <FeatureIconWrapper>
                <Target size={28} color="white" />
              </FeatureIconWrapper>
              <FeatureTitle>Precision Learning Analytics</FeatureTitle>
              <FeatureDescription>
                Advanced analytics track micro-movements and learning patterns, providing
                data-driven insights to accelerate your mastery of sign language
              </FeatureDescription>
            </FeatureCard>

            <FeatureCard>
              <FeatureIconWrapper>
                <Shield size={28} color="white" />
              </FeatureIconWrapper>
              <FeatureTitle>Privacy-First Architecture</FeatureTitle>
              <FeatureDescription>
                Enterprise-grade security with local processing ensures your data remains private
                while contributing anonymized insights to advance AI research
              </FeatureDescription>
            </FeatureCard>
          </FeatureGrid>
        </FeaturesContainer>
      </FeaturesSection>
    </HomeContainer>
  );
};

export default HomePage; 