{"cells": [{"cell_type": "markdown", "id": "6b9f338a", "metadata": {"papermill": {"duration": 0.005832, "end_time": "2023-03-16T15:16:25.962431", "exception": false, "start_time": "2023-03-16T15:16:25.956599", "status": "completed"}, "tags": []}, "source": ["# Introduction\n", "\n", "This notebook is created for [<PERSON><PERSON>'s Sign Langueage Classifier Competition](https://www.kaggle.com/competitions/asl-signs/code)\n", "\n", "In this notebook, I used notebooks from various sources and modify them to get this notebook. Please give upvotes to them if you also find it useful:\n", "\n", "- I used some of the data visualization of the landmark from [Sign Language EDA & Visualization](https://www.kaggle.com/code/mayukh18/sign-language-eda-visualization).\n", "\n", "- I used the preprocessed tensorflow Dataset from [tfdataset-of-google-isl-recognition-data](https://www.kaggle.com/datasets/aapokossi/saved-tfdataset-of-google-isl-recognition-data).\n", "\n", "- I train my model following the notebook. [Submission for variable length time-series model](https://www.kaggle.com/code/aapokossi/submission-for-variable-length-time-series-model). I then tweak the layers and the epoch to increase the accuracy.\n", "\n", "I also included comments and links to help study the model further."]}, {"cell_type": "markdown", "id": "771040e9", "metadata": {"papermill": {"duration": 0.004402, "end_time": "2023-03-16T15:16:25.971725", "exception": false, "start_time": "2023-03-16T15:16:25.967323", "status": "completed"}, "tags": []}, "source": ["<a id=\"contents\"></a>\n", "# Contents\n", "1. [Import Libraries and Set File Directories](#section-one)\n", "2. [Visualize data](#section-two)\n", "3. [Load Data](#section-three)\n", "4. [Train Model](#section-four)\n", "5. [Submit Model](#section-five)"]}, {"cell_type": "markdown", "id": "970cb79a", "metadata": {"papermill": {"duration": 0.004375, "end_time": "2023-03-16T15:16:25.981000", "exception": false, "start_time": "2023-03-16T15:16:25.976625", "status": "completed"}, "tags": []}, "source": ["<a id=\"section-one\"></a>\n", "# Import Libraries and Set File Directories"]}, {"cell_type": "code", "execution_count": 1, "id": "59224a4b", "metadata": {"_cell_guid": "b1076dfc-b9ad-4769-8c92-a6c4dae69d19", "_uuid": "8f2839f25d086af736a60e9eeb907d3b93b6e0e5", "execution": {"iopub.execute_input": "2023-03-16T15:16:25.993421Z", "iopub.status.busy": "2023-03-16T15:16:25.992193Z", "iopub.status.idle": "2023-03-16T15:16:34.212338Z", "shell.execute_reply": "2023-03-16T15:16:34.211165Z"}, "papermill": {"duration": 8.229713, "end_time": "2023-03-16T15:16:34.215287", "exception": false, "start_time": "2023-03-16T15:16:25.985574", "status": "completed"}, "tags": []}, "outputs": [], "source": ["# import libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import tensorflow as tf\n", "from tensorflow.keras import layers, optimizers"]}, {"cell_type": "code", "execution_count": 2, "id": "b086608b", "metadata": {"execution": {"iopub.execute_input": "2023-03-16T15:16:34.226676Z", "iopub.status.busy": "2023-03-16T15:16:34.226029Z", "iopub.status.idle": "2023-03-16T15:16:34.230853Z", "shell.execute_reply": "2023-03-16T15:16:34.229739Z"}, "papermill": {"duration": 0.013293, "end_time": "2023-03-16T15:16:34.233516", "exception": false, "start_time": "2023-03-16T15:16:34.220223", "status": "completed"}, "tags": []}, "outputs": [], "source": ["# set files directories\n", "LANDMARK_FILES_DIR = \"/kaggle/input/asl-signs/train_landmark_files\"\n", "TRAIN_FILE = \"/kaggle/input/asl-signs/train.csv\""]}, {"cell_type": "markdown", "id": "e166c38c", "metadata": {"papermill": {"duration": 0.004459, "end_time": "2023-03-16T15:16:34.242790", "exception": false, "start_time": "2023-03-16T15:16:34.238331", "status": "completed"}, "tags": []}, "source": ["<a id=\"section-two\"></a>\n", "# Visualize data"]}, {"cell_type": "markdown", "id": "89459148", "metadata": {"papermill": {"duration": 0.004441, "end_time": "2023-03-16T15:16:34.251795", "exception": false, "start_time": "2023-03-16T15:16:34.247354", "status": "completed"}, "tags": []}, "source": ["Slightly different from a dataframe, in this competition we will be using a [parquet](https://towardsdatascience.com/demystifying-the-parquet-file-format-13adb0206705). We will read it using the command `read_parquet`."]}, {"cell_type": "code", "execution_count": 3, "id": "930f2404", "metadata": {"execution": {"iopub.execute_input": "2023-03-16T15:16:34.262478Z", "iopub.status.busy": "2023-03-16T15:16:34.261899Z", "iopub.status.idle": "2023-03-16T15:16:34.426234Z", "shell.execute_reply": "2023-03-16T15:16:34.425290Z"}, "papermill": {"duration": 0.172362, "end_time": "2023-03-16T15:16:34.428754", "exception": false, "start_time": "2023-03-16T15:16:34.256392", "status": "completed"}, "tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>frame</th>\n", "      <th>row_id</th>\n", "      <th>type</th>\n", "      <th>landmark_index</th>\n", "      <th>x</th>\n", "      <th>y</th>\n", "      <th>z</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>103</td>\n", "      <td>103-face-0</td>\n", "      <td>face</td>\n", "      <td>0</td>\n", "      <td>0.437886</td>\n", "      <td>0.437599</td>\n", "      <td>-0.051134</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>103</td>\n", "      <td>103-face-1</td>\n", "      <td>face</td>\n", "      <td>1</td>\n", "      <td>0.443258</td>\n", "      <td>0.392901</td>\n", "      <td>-0.067054</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>103</td>\n", "      <td>103-face-2</td>\n", "      <td>face</td>\n", "      <td>2</td>\n", "      <td>0.443997</td>\n", "      <td>0.409998</td>\n", "      <td>-0.042990</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>103</td>\n", "      <td>103-face-3</td>\n", "      <td>face</td>\n", "      <td>3</td>\n", "      <td>0.435256</td>\n", "      <td>0.362771</td>\n", "      <td>-0.039492</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>103</td>\n", "      <td>103-face-4</td>\n", "      <td>face</td>\n", "      <td>4</td>\n", "      <td>0.443780</td>\n", "      <td>0.381762</td>\n", "      <td>-0.068013</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   frame      row_id  type  landmark_index         x         y         z\n", "0    103  103-face-0  face               0  0.437886  0.437599 -0.051134\n", "1    103  103-face-1  face               1  0.443258  0.392901 -0.067054\n", "2    103  103-face-2  face               2  0.443997  0.409998 -0.042990\n", "3    103  103-face-3  face               3  0.435256  0.362771 -0.039492\n", "4    103  103-face-4  face               4  0.443780  0.381762 -0.068013"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# read the data, the type of the data\n", "sample = pd.read_parquet(\"/kaggle/input/asl-signs/train_landmark_files/16069/100015657.parquet\")\n", "sample.head()"]}, {"cell_type": "markdown", "id": "abc75bfd", "metadata": {"papermill": {"duration": 0.00472, "end_time": "2023-03-16T15:16:34.438918", "exception": false, "start_time": "2023-03-16T15:16:34.434198", "status": "completed"}, "tags": []}, "source": ["Everyone likes to visualize things right? Since the data is about sequences of hand movement, we might not be able to see it from tabular data only. Let us instead plot the movement into matplotlib."]}, {"cell_type": "code", "execution_count": 4, "id": "a92d28cd", "metadata": {"execution": {"iopub.execute_input": "2023-03-16T15:16:34.449875Z", "iopub.status.busy": "2023-03-16T15:16:34.449559Z", "iopub.status.idle": "2023-03-16T15:16:36.072195Z", "shell.execute_reply": "2023-03-16T15:16:36.071324Z"}, "papermill": {"duration": 1.633439, "end_time": "2023-03-16T15:16:36.077144", "exception": false, "start_time": "2023-03-16T15:16:34.443705", "status": "completed"}, "tags": []}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 500x2500 with 10 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# pick the left hand and right hand points\n", "sample_left_hand = sample[sample.type == \"left_hand\"]\n", "sample_right_hand = sample[sample.type == \"right_hand\"]\n", "\n", "# edges that represents the hand edges\n", "edges = [(0,1),(1,2),(2,3),(3,4),(0,5),(0,17),(5,6),(6,7),(7,8),(5,9),(9,10),(10,11),(11,12),\n", "         (9,13),(13,14),(14,15),(15,16),(13,17),(17,18),(18,19),(19,20)]\n", "\n", "# plotting a single frame into matplotlib\n", "def plot_frame(df, frame_id, ax):\n", "    df = df[df.frame == frame_id].sort_values(['landmark_index'])\n", "    x = list(df.x)\n", "    y = list(df.y)\n", "    \n", "    # plotting the points\n", "    ax.scatter(df.x, df.y, color='dodgerblue')\n", "    for i in range(len(x)):\n", "        ax.text(x[i], y[i], str(i))\n", "    \n", "    # plotting the edges that represents the hand\n", "    for edge in edges:\n", "        ax.plot([x[edge[0]], x[edge[1]]], [y[edge[0]], y[edge[1]]], color='salmon')\n", "        ax.set_xlabel(f\"Frame no. {frame_id}\")\n", "        ax.set_xticks([])\n", "        ax.set_yticks([])\n", "        ax.set_xticklabels([])\n", "        ax.set_yticklabels([])\n", "\n", "# plotting the multiple frames\n", "def plot_frame_seq(df, frame_range, n_frames):\n", "    frames = np.linspace(frame_range[0],frame_range[1],n_frames, dtype = int, endpoint=True)\n", "    fig, ax = plt.subplots(n_frames, 1, figsize=(5,25))\n", "    for i in range(n_frames):\n", "        plot_frame(df, frames[i], ax[i])\n", "        \n", "    plt.show()\n", "\n", "plot_frame_seq(sample_left_hand, (178,186), 10)"]}, {"cell_type": "markdown", "id": "861d42ac", "metadata": {"papermill": {"duration": 0.007262, "end_time": "2023-03-16T15:16:36.091981", "exception": false, "start_time": "2023-03-16T15:16:36.084719", "status": "completed"}, "tags": []}, "source": ["<a id=\"section-three\"></a>\n", "# Load Data"]}, {"cell_type": "code", "execution_count": 5, "id": "ec6ad730", "metadata": {"execution": {"iopub.execute_input": "2023-03-16T15:16:36.108253Z", "iopub.status.busy": "2023-03-16T15:16:36.107950Z", "iopub.status.idle": "2023-03-16T15:16:36.113431Z", "shell.execute_reply": "2023-03-16T15:16:36.112335Z"}, "papermill": {"duration": 0.016179, "end_time": "2023-03-16T15:16:36.115431", "exception": false, "start_time": "2023-03-16T15:16:36.099252", "status": "completed"}, "tags": []}, "outputs": [], "source": ["# Set constants and pick important landmarks\n", "LANDMARK_IDX = [0,9,11,13,14,17,117,118,119,199,346,347,348] + list(range(468,543))\n", "DATA_PATH = \"/kaggle/input/saved-tfdataset-of-google-isl-recognition-data/GoogleISLDatasetBatched\"\n", "DS_CARDINALITY = 185\n", "VAL_SIZE  = 18\n", "N_SIGNS = 250\n", "ROWS_PER_FRAME = 543"]}, {"cell_type": "markdown", "id": "f16a8da5", "metadata": {"papermill": {"duration": 0.007182, "end_time": "2023-03-16T15:16:36.129772", "exception": false, "start_time": "2023-03-16T15:16:36.122590", "status": "completed"}, "tags": []}, "source": ["To keep it simple, we will use the preprocessed [tf.Dataset](https://www.tensorflow.org/api_docs/python/tf/data/Dataset) from [tfdataset-of-google-isl-recognition-data](https://www.kaggle.com/datasets/aapokossi/saved-tfdataset-of-google-isl-recognition-data)."]}, {"cell_type": "code", "execution_count": 6, "id": "c04908c1", "metadata": {"execution": {"iopub.execute_input": "2023-03-16T15:16:36.145833Z", "iopub.status.busy": "2023-03-16T15:16:36.145309Z", "iopub.status.idle": "2023-03-16T15:16:40.251300Z", "shell.execute_reply": "2023-03-16T15:16:40.250239Z"}, "papermill": {"duration": 4.116994, "end_time": "2023-03-16T15:16:40.254058", "exception": false, "start_time": "2023-03-16T15:16:36.137064", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def preprocess(ragged_batch, labels):\n", "    ragged_batch = tf.gather(ragged_batch, LANDMARK_IDX, axis=2)\n", "    ragged_batch = tf.where(tf.math.is_nan(ragged_batch), tf.zeros_like(ragged_batch), ragged_batch)\n", "    return tf.concat([ragged_batch[...,i] for i in range(3)],-1), labels\n", "\n", "dataset = tf.data.Dataset.load(DATA_PATH)\n", "dataset = dataset.map(preprocess)\n", "val_ds = dataset.take(VAL_SIZE).cache().prefetch(tf.data.AUTOTUNE)\n", "train_ds = dataset.skip(VAL_SIZE).cache().shuffle(20).prefetch(tf.data.AUTOTUNE)"]}, {"cell_type": "markdown", "id": "ccda5919", "metadata": {"papermill": {"duration": 0.007258, "end_time": "2023-03-16T15:16:40.269383", "exception": false, "start_time": "2023-03-16T15:16:40.262125", "status": "completed"}, "tags": []}, "source": ["<a id=\"section-four\"></a>\n", "# Train Model"]}, {"cell_type": "markdown", "id": "36e76597", "metadata": {"papermill": {"duration": 0.007136, "end_time": "2023-03-16T15:16:40.283747", "exception": false, "start_time": "2023-03-16T15:16:40.276611", "status": "completed"}, "tags": []}, "source": ["Now let us get to the fun part, training the model!"]}, {"cell_type": "code", "execution_count": 7, "id": "4311e26a", "metadata": {"execution": {"iopub.execute_input": "2023-03-16T15:16:40.302047Z", "iopub.status.busy": "2023-03-16T15:16:40.300255Z", "iopub.status.idle": "2023-03-16T15:16:40.308611Z", "shell.execute_reply": "2023-03-16T15:16:40.307696Z"}, "papermill": {"duration": 0.019327, "end_time": "2023-03-16T15:16:40.310728", "exception": false, "start_time": "2023-03-16T15:16:40.291401", "status": "completed"}, "tags": []}, "outputs": [], "source": ["# include early stopping and reducelr\n", "def get_callbacks():\n", "    return [\n", "            tf.keras.callbacks.EarlyStopping(\n", "            monitor=\"val_accuracy\",\n", "            patience = 10,\n", "            restore_best_weights=True\n", "        ),\n", "        tf.keras.callbacks.ReduceLROnPlateau(\n", "            monitor = \"val_accuracy\",\n", "            factor = 0.5,\n", "            patience = 3\n", "        ),\n", "    ]\n", "\n", "# a single dense block followed by a normalization block and relu activation\n", "def dense_block(units, name):\n", "    fc = layers.Dense(units)\n", "    norm = layers.LayerNormalization()\n", "    act = layers.Activation(\"relu\")\n", "    drop = layers.Dropout(0.1)\n", "    return lambda x: drop(act(norm(fc(x))))\n", "\n", "# the lstm block with the final dense block for the classification\n", "def classifier(lstm_units):\n", "    lstm = layers.LSTM(lstm_units)\n", "    out = layers.Dense(N_SIGNS, activation=\"softmax\")\n", "    return lambda x: out(lstm(x))"]}, {"cell_type": "code", "execution_count": 8, "id": "9cf30ba8", "metadata": {"execution": {"iopub.execute_input": "2023-03-16T15:16:40.327057Z", "iopub.status.busy": "2023-03-16T15:16:40.326182Z", "iopub.status.idle": "2023-03-16T15:16:40.873345Z", "shell.execute_reply": "2023-03-16T15:16:40.872500Z"}, "papermill": {"duration": 0.574679, "end_time": "2023-03-16T15:16:40.892649", "exception": false, "start_time": "2023-03-16T15:16:40.317970", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model: \"model\"\n", "_________________________________________________________________\n", " Layer (type)                Output Shape              Param #   \n", "=================================================================\n", " input_1 (InputLayer)        [(None, None, 264)]       0         \n", "                                                                 \n", " dense (Dense)               (None, None, 512)         135680    \n", "                                                                 \n", " layer_normalization (LayerN  (None, None, 512)        1024      \n", " ormalization)                                                   \n", "                                                                 \n", " activation (Activation)     (None, None, 512)         0         \n", "                                                                 \n", " dropout (Dropout)           (None, None, 512)         0         \n", "                                                                 \n", " dense_1 (<PERSON><PERSON>)             (None, None, 256)         131328    \n", "                                                                 \n", " layer_normalization_1 (<PERSON><PERSON>  (None, None, 256)        512       \n", " rNormalization)                                                 \n", "                                                                 \n", " activation_1 (Activation)   (None, None, 256)         0         \n", "                                                                 \n", " dropout_1 (Dropout)         (None, None, 256)         0         \n", "                                                                 \n", " lstm (LSTM)                 (None, 250)               507000    \n", "                                                                 \n", " dense_2 (<PERSON><PERSON>)             (None, 250)               62750     \n", "                                                                 \n", "=================================================================\n", "Total params: 838,294\n", "Trainable params: 838,294\n", "Non-trainable params: 0\n", "_________________________________________________________________\n"]}], "source": ["# choose the number of nodes per layer\n", "encoder_units = [512, 256] # tune this\n", "lstm_units = 250 # tune this\n", "\n", "#define the inputs (ragged batches of time series of landmark coordinates)\n", "inputs = tf.keras.Input(shape=(None,3*len(LANDMARK_IDX)), ragged=True)\n", "\n", "# dense encoder model\n", "x = inputs\n", "for i, n in enumerate(encoder_units):\n", "    x = dense_block(n, f\"encoder_{i}\")(x)\n", "\n", "# classifier model\n", "out = classifier(lstm_units)(x)\n", "\n", "model = tf.keras.Model(inputs=inputs, outputs=out)\n", "model.summary()"]}, {"cell_type": "code", "execution_count": 9, "id": "afb9e80a", "metadata": {"execution": {"iopub.execute_input": "2023-03-16T15:16:40.912632Z", "iopub.status.busy": "2023-03-16T15:16:40.912079Z", "iopub.status.idle": "2023-03-16T15:16:40.967770Z", "shell.execute_reply": "2023-03-16T15:16:40.966714Z"}, "papermill": {"duration": 0.068351, "end_time": "2023-03-16T15:16:40.970358", "exception": false, "start_time": "2023-03-16T15:16:40.902007", "status": "completed"}, "tags": []}, "outputs": [], "source": ["# add a decreasing learning rate scheduler to help convergence\n", "steps_per_epoch = DS_CARDINALITY - VAL_SIZE\n", "boundaries = [steps_per_epoch * n for n in [30,50,70]]\n", "values = [1e-3,1e-4,1e-5,1e-6]\n", "lr_sched = optimizers.schedules.PiecewiseConstantDecay(boundaries, values)\n", "optimizer = optimizers.<PERSON>(lr_sched)\n", "\n", "model.compile(optimizer=optimizer,\n", "              loss=\"sparse_categorical_crossentropy\",\n", "              metrics=[\"accuracy\",\"sparse_top_k_categorical_accuracy\"])"]}, {"cell_type": "code", "execution_count": 10, "id": "b4391ab1", "metadata": {"execution": {"iopub.execute_input": "2023-03-16T15:16:40.990082Z", "iopub.status.busy": "2023-03-16T15:16:40.989795Z", "iopub.status.idle": "2023-03-16T16:11:03.937681Z", "shell.execute_reply": "2023-03-16T16:11:03.936556Z"}, "papermill": {"duration": 3262.960073, "end_time": "2023-03-16T16:11:03.939870", "exception": false, "start_time": "2023-03-16T15:16:40.979797", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch 1/100\n", "167/167 [==============================] - 169s 780ms/step - loss: 5.3075 - accuracy: 0.0135 - sparse_top_k_categorical_accuracy: 0.0558 - val_loss: 4.7720 - val_accuracy: 0.0433 - val_sparse_top_k_categorical_accuracy: 0.1496 - lr: 0.0010\n", "Epoch 2/100\n", "167/167 [==============================] - 44s 265ms/step - loss: 4.4345 - accuracy: 0.0773 - sparse_top_k_categorical_accuracy: 0.2384 - val_loss: 4.0023 - val_accuracy: 0.1184 - val_sparse_top_k_categorical_accuracy: 0.3444 - lr: 0.0010\n", "Epoch 3/100\n", "167/167 [==============================] - 44s 267ms/step - loss: 3.6600 - accuracy: 0.1796 - sparse_top_k_categorical_accuracy: 0.4387 - val_loss: 3.3636 - val_accuracy: 0.2248 - val_sparse_top_k_categorical_accuracy: 0.5128 - lr: 0.0010\n", "Epoch 4/100\n", "167/167 [==============================] - 44s 265ms/step - loss: 3.1387 - accuracy: 0.2702 - sparse_top_k_categorical_accuracy: 0.5643 - val_loss: 2.9601 - val_accuracy: 0.2975 - val_sparse_top_k_categorical_accuracy: 0.6013 - lr: 0.0010\n", "Epoch 5/100\n", "167/167 [==============================] - 44s 267ms/step - loss: 2.7821 - accuracy: 0.3383 - sparse_top_k_categorical_accuracy: 0.6408 - val_loss: 2.6510 - val_accuracy: 0.3628 - val_sparse_top_k_categorical_accuracy: 0.6709 - lr: 0.0010\n", "Epoch 6/100\n", "167/167 [==============================] - 44s 265ms/step - loss: 2.4900 - accuracy: 0.3978 - sparse_top_k_categorical_accuracy: 0.7003 - val_loss: 2.4431 - val_accuracy: 0.4070 - val_sparse_top_k_categorical_accuracy: 0.7025 - lr: 0.0010\n", "Epoch 7/100\n", "167/167 [==============================] - 44s 265ms/step - loss: 2.3174 - accuracy: 0.4354 - sparse_top_k_categorical_accuracy: 0.7304 - val_loss: 2.2931 - val_accuracy: 0.4379 - val_sparse_top_k_categorical_accuracy: 0.7310 - lr: 0.0010\n", "Epoch 8/100\n", "167/167 [==============================] - 44s 263ms/step - loss: 2.1433 - accuracy: 0.4744 - sparse_top_k_categorical_accuracy: 0.7598 - val_loss: 2.1448 - val_accuracy: 0.4683 - val_sparse_top_k_categorical_accuracy: 0.7587 - lr: 0.0010\n", "Epoch 9/100\n", "167/167 [==============================] - 44s 265ms/step - loss: 1.9845 - accuracy: 0.5080 - sparse_top_k_categorical_accuracy: 0.7863 - val_loss: 2.0793 - val_accuracy: 0.4868 - val_sparse_top_k_categorical_accuracy: 0.7722 - lr: 0.0010\n", "Epoch 10/100\n", "167/167 [==============================] - 44s 263ms/step - loss: 1.9009 - accuracy: 0.5240 - sparse_top_k_categorical_accuracy: 0.7976 - val_loss: 1.9733 - val_accuracy: 0.5089 - val_sparse_top_k_categorical_accuracy: 0.7867 - lr: 0.0010\n", "Epoch 11/100\n", "167/167 [==============================] - 44s 264ms/step - loss: 1.7707 - accuracy: 0.5561 - sparse_top_k_categorical_accuracy: 0.8162 - val_loss: 1.8920 - val_accuracy: 0.5291 - val_sparse_top_k_categorical_accuracy: 0.8009 - lr: 0.0010\n", "Epoch 12/100\n", "167/167 [==============================] - 44s 264ms/step - loss: 1.6736 - accuracy: 0.5789 - sparse_top_k_categorical_accuracy: 0.8305 - val_loss: 1.8179 - val_accuracy: 0.5501 - val_sparse_top_k_categorical_accuracy: 0.8105 - lr: 0.0010\n", "Epoch 13/100\n", "167/167 [==============================] - 44s 264ms/step - loss: 1.5996 - accuracy: 0.5948 - sparse_top_k_categorical_accuracy: 0.8423 - val_loss: 1.7643 - val_accuracy: 0.5627 - val_sparse_top_k_categorical_accuracy: 0.8160 - lr: 0.0010\n", "Epoch 14/100\n", "167/167 [==============================] - 44s 265ms/step - loss: 1.5224 - accuracy: 0.6133 - sparse_top_k_categorical_accuracy: 0.8533 - val_loss: 1.7397 - val_accuracy: 0.5615 - val_sparse_top_k_categorical_accuracy: 0.8169 - lr: 0.0010\n", "Epoch 15/100\n", "167/167 [==============================] - 44s 264ms/step - loss: 1.4644 - accuracy: 0.6272 - sparse_top_k_categorical_accuracy: 0.8603 - val_loss: 1.8037 - val_accuracy: 0.5509 - val_sparse_top_k_categorical_accuracy: 0.8083 - lr: 0.0010\n", "Epoch 16/100\n", "167/167 [==============================] - 44s 263ms/step - loss: 1.3962 - accuracy: 0.6431 - sparse_top_k_categorical_accuracy: 0.8692 - val_loss: 1.6295 - val_accuracy: 0.5927 - val_sparse_top_k_categorical_accuracy: 0.8355 - lr: 0.0010\n", "Epoch 17/100\n", "167/167 [==============================] - 44s 264ms/step - loss: 1.3641 - accuracy: 0.6491 - sparse_top_k_categorical_accuracy: 0.8738 - val_loss: 1.6758 - val_accuracy: 0.5811 - val_sparse_top_k_categorical_accuracy: 0.8254 - lr: 0.0010\n", "Epoch 18/100\n", "167/167 [==============================] - 44s 263ms/step - loss: 1.2944 - accuracy: 0.6681 - sparse_top_k_categorical_accuracy: 0.8819 - val_loss: 1.5703 - val_accuracy: 0.6089 - val_sparse_top_k_categorical_accuracy: 0.8406 - lr: 0.0010\n", "Epoch 19/100\n", "167/167 [==============================] - 44s 265ms/step - loss: 1.2291 - accuracy: 0.6839 - sparse_top_k_categorical_accuracy: 0.8901 - val_loss: 1.5482 - val_accuracy: 0.6077 - val_sparse_top_k_categorical_accuracy: 0.8464 - lr: 0.0010\n", "Epoch 20/100\n", "167/167 [==============================] - 44s 264ms/step - loss: 1.1910 - accuracy: 0.6933 - sparse_top_k_categorical_accuracy: 0.8942 - val_loss: 1.5307 - val_accuracy: 0.6132 - val_sparse_top_k_categorical_accuracy: 0.8478 - lr: 0.0010\n", "Epoch 21/100\n", "167/167 [==============================] - 44s 266ms/step - loss: 1.1510 - accuracy: 0.7029 - sparse_top_k_categorical_accuracy: 0.8993 - val_loss: 1.5252 - val_accuracy: 0.6222 - val_sparse_top_k_categorical_accuracy: 0.8494 - lr: 0.0010\n", "Epoch 22/100\n", "167/167 [==============================] - 44s 263ms/step - loss: 1.1385 - accuracy: 0.7059 - sparse_top_k_categorical_accuracy: 0.9000 - val_loss: 1.5930 - val_accuracy: 0.6007 - val_sparse_top_k_categorical_accuracy: 0.8392 - lr: 0.0010\n", "Epoch 23/100\n", "167/167 [==============================] - 44s 265ms/step - loss: 1.0772 - accuracy: 0.7200 - sparse_top_k_categorical_accuracy: 0.9075 - val_loss: 1.5577 - val_accuracy: 0.6087 - val_sparse_top_k_categorical_accuracy: 0.8442 - lr: 0.0010\n", "Epoch 24/100\n", "167/167 [==============================] - 44s 264ms/step - loss: 1.0521 - accuracy: 0.7268 - sparse_top_k_categorical_accuracy: 0.9098 - val_loss: 1.5725 - val_accuracy: 0.6066 - val_sparse_top_k_categorical_accuracy: 0.8423 - lr: 0.0010\n", "Epoch 25/100\n", "167/167 [==============================] - 44s 263ms/step - loss: 1.0195 - accuracy: 0.7357 - sparse_top_k_categorical_accuracy: 0.9139 - val_loss: 1.4692 - val_accuracy: 0.6365 - val_sparse_top_k_categorical_accuracy: 0.8551 - lr: 0.0010\n", "Epoch 26/100\n", "167/167 [==============================] - 44s 265ms/step - loss: 0.9766 - accuracy: 0.7473 - sparse_top_k_categorical_accuracy: 0.9184 - val_loss: 1.4487 - val_accuracy: 0.6373 - val_sparse_top_k_categorical_accuracy: 0.8574 - lr: 0.0010\n", "Epoch 27/100\n", "167/167 [==============================] - 44s 263ms/step - loss: 0.9503 - accuracy: 0.7546 - sparse_top_k_categorical_accuracy: 0.9222 - val_loss: 1.4671 - val_accuracy: 0.6363 - val_sparse_top_k_categorical_accuracy: 0.8545 - lr: 0.0010\n", "Epoch 28/100\n", "167/167 [==============================] - 44s 265ms/step - loss: 0.9216 - accuracy: 0.7589 - sparse_top_k_categorical_accuracy: 0.9251 - val_loss: 1.4117 - val_accuracy: 0.6515 - val_sparse_top_k_categorical_accuracy: 0.8604 - lr: 0.0010\n", "Epoch 29/100\n", "167/167 [==============================] - 44s 263ms/step - loss: 0.9154 - accuracy: 0.7618 - sparse_top_k_categorical_accuracy: 0.9263 - val_loss: 1.4654 - val_accuracy: 0.6349 - val_sparse_top_k_categorical_accuracy: 0.8563 - lr: 0.0010\n", "Epoch 30/100\n", "167/167 [==============================] - 44s 263ms/step - loss: 0.8990 - accuracy: 0.7643 - sparse_top_k_categorical_accuracy: 0.9286 - val_loss: 1.4055 - val_accuracy: 0.6494 - val_sparse_top_k_categorical_accuracy: 0.8637 - lr: 0.0010\n", "Epoch 31/100\n", "167/167 [==============================] - 44s 264ms/step - loss: 0.7177 - accuracy: 0.8235 - sparse_top_k_categorical_accuracy: 0.9432 - val_loss: 1.3104 - val_accuracy: 0.6753 - val_sparse_top_k_categorical_accuracy: 0.8735 - lr: 1.0000e-04\n", "Epoch 32/100\n", "167/167 [==============================] - 44s 264ms/step - loss: 0.6741 - accuracy: 0.8377 - sparse_top_k_categorical_accuracy: 0.9459 - val_loss: 1.3073 - val_accuracy: 0.6757 - val_sparse_top_k_categorical_accuracy: 0.8733 - lr: 1.0000e-04\n", "Epoch 33/100\n", "167/167 [==============================] - 44s 265ms/step - loss: 0.6607 - accuracy: 0.8418 - sparse_top_k_categorical_accuracy: 0.9473 - val_loss: 1.3026 - val_accuracy: 0.6784 - val_sparse_top_k_categorical_accuracy: 0.8732 - lr: 1.0000e-04\n", "Epoch 34/100\n", "167/167 [==============================] - 44s 264ms/step - loss: 0.6504 - accuracy: 0.8449 - sparse_top_k_categorical_accuracy: 0.9479 - val_loss: 1.2983 - val_accuracy: 0.6786 - val_sparse_top_k_categorical_accuracy: 0.8753 - lr: 1.0000e-04\n", "Epoch 35/100\n", "167/167 [==============================] - 44s 264ms/step - loss: 0.6448 - accuracy: 0.8471 - sparse_top_k_categorical_accuracy: 0.9485 - val_loss: 1.2982 - val_accuracy: 0.6782 - val_sparse_top_k_categorical_accuracy: 0.8752 - lr: 1.0000e-04\n", "Epoch 36/100\n", "167/167 [==============================] - 44s 265ms/step - loss: 0.6354 - accuracy: 0.8506 - sparse_top_k_categorical_accuracy: 0.9497 - val_loss: 1.2970 - val_accuracy: 0.6787 - val_sparse_top_k_categorical_accuracy: 0.8751 - lr: 1.0000e-04\n", "Epoch 37/100\n", "167/167 [==============================] - 44s 263ms/step - loss: 0.6307 - accuracy: 0.8504 - sparse_top_k_categorical_accuracy: 0.9505 - val_loss: 1.2990 - val_accuracy: 0.6789 - val_sparse_top_k_categorical_accuracy: 0.8734 - lr: 1.0000e-04\n", "Epoch 38/100\n", "167/167 [==============================] - 44s 265ms/step - loss: 0.6244 - accuracy: 0.8524 - sparse_top_k_categorical_accuracy: 0.9505 - val_loss: 1.2961 - val_accuracy: 0.6828 - val_sparse_top_k_categorical_accuracy: 0.8755 - lr: 1.0000e-04\n", "Epoch 39/100\n", "167/167 [==============================] - 44s 264ms/step - loss: 0.6169 - accuracy: 0.8555 - sparse_top_k_categorical_accuracy: 0.9513 - val_loss: 1.2955 - val_accuracy: 0.6814 - val_sparse_top_k_categorical_accuracy: 0.8754 - lr: 1.0000e-04\n", "Epoch 40/100\n", "167/167 [==============================] - 44s 264ms/step - loss: 0.6142 - accuracy: 0.8564 - sparse_top_k_categorical_accuracy: 0.9517 - val_loss: 1.2953 - val_accuracy: 0.6801 - val_sparse_top_k_categorical_accuracy: 0.8758 - lr: 1.0000e-04\n", "Epoch 41/100\n", "167/167 [==============================] - 45s 267ms/step - loss: 0.6083 - accuracy: 0.8584 - sparse_top_k_categorical_accuracy: 0.9518 - val_loss: 1.2932 - val_accuracy: 0.6828 - val_sparse_top_k_categorical_accuracy: 0.8757 - lr: 1.0000e-04\n", "Epoch 42/100\n", "167/167 [==============================] - 44s 264ms/step - loss: 0.6038 - accuracy: 0.8592 - sparse_top_k_categorical_accuracy: 0.9529 - val_loss: 1.2963 - val_accuracy: 0.6795 - val_sparse_top_k_categorical_accuracy: 0.8743 - lr: 1.0000e-04\n", "Epoch 43/100\n", "167/167 [==============================] - 44s 265ms/step - loss: 0.5986 - accuracy: 0.8603 - sparse_top_k_categorical_accuracy: 0.9530 - val_loss: 1.2934 - val_accuracy: 0.6815 - val_sparse_top_k_categorical_accuracy: 0.8750 - lr: 1.0000e-04\n", "Epoch 44/100\n", "167/167 [==============================] - 44s 264ms/step - loss: 0.5947 - accuracy: 0.8611 - sparse_top_k_categorical_accuracy: 0.9536 - val_loss: 1.2949 - val_accuracy: 0.6815 - val_sparse_top_k_categorical_accuracy: 0.8742 - lr: 1.0000e-04\n", "Epoch 45/100\n", "167/167 [==============================] - 44s 263ms/step - loss: 0.5864 - accuracy: 0.8645 - sparse_top_k_categorical_accuracy: 0.9544 - val_loss: 1.2933 - val_accuracy: 0.6820 - val_sparse_top_k_categorical_accuracy: 0.8749 - lr: 1.0000e-04\n", "Epoch 46/100\n", "167/167 [==============================] - 44s 266ms/step - loss: 0.5832 - accuracy: 0.8650 - sparse_top_k_categorical_accuracy: 0.9544 - val_loss: 1.2950 - val_accuracy: 0.6820 - val_sparse_top_k_categorical_accuracy: 0.8762 - lr: 1.0000e-04\n", "Epoch 47/100\n", "167/167 [==============================] - 44s 263ms/step - loss: 0.5793 - accuracy: 0.8672 - sparse_top_k_categorical_accuracy: 0.9547 - val_loss: 1.2988 - val_accuracy: 0.6831 - val_sparse_top_k_categorical_accuracy: 0.8749 - lr: 1.0000e-04\n", "Epoch 48/100\n", "167/167 [==============================] - 45s 267ms/step - loss: 0.5728 - accuracy: 0.8681 - sparse_top_k_categorical_accuracy: 0.9551 - val_loss: 1.2979 - val_accuracy: 0.6810 - val_sparse_top_k_categorical_accuracy: 0.8740 - lr: 1.0000e-04\n", "Epoch 49/100\n", "167/167 [==============================] - 44s 264ms/step - loss: 0.5693 - accuracy: 0.8682 - sparse_top_k_categorical_accuracy: 0.9555 - val_loss: 1.3030 - val_accuracy: 0.6834 - val_sparse_top_k_categorical_accuracy: 0.8762 - lr: 1.0000e-04\n", "Epoch 50/100\n", "167/167 [==============================] - 44s 264ms/step - loss: 0.5663 - accuracy: 0.8693 - sparse_top_k_categorical_accuracy: 0.9557 - val_loss: 1.2955 - val_accuracy: 0.6837 - val_sparse_top_k_categorical_accuracy: 0.8750 - lr: 1.0000e-04\n", "Epoch 51/100\n", "167/167 [==============================] - 44s 266ms/step - loss: 0.5466 - accuracy: 0.8766 - sparse_top_k_categorical_accuracy: 0.9577 - val_loss: 1.2884 - val_accuracy: 0.6852 - val_sparse_top_k_categorical_accuracy: 0.8761 - lr: 1.0000e-05\n", "Epoch 52/100\n", "167/167 [==============================] - 44s 264ms/step - loss: 0.5445 - accuracy: 0.8764 - sparse_top_k_categorical_accuracy: 0.9576 - val_loss: 1.2869 - val_accuracy: 0.6848 - val_sparse_top_k_categorical_accuracy: 0.8764 - lr: 1.0000e-05\n", "Epoch 53/100\n", "167/167 [==============================] - 44s 266ms/step - loss: 0.5409 - accuracy: 0.8784 - sparse_top_k_categorical_accuracy: 0.9577 - val_loss: 1.2861 - val_accuracy: 0.6855 - val_sparse_top_k_categorical_accuracy: 0.8766 - lr: 1.0000e-05\n", "Epoch 54/100\n", "167/167 [==============================] - 44s 263ms/step - loss: 0.5437 - accuracy: 0.8775 - sparse_top_k_categorical_accuracy: 0.9576 - val_loss: 1.2861 - val_accuracy: 0.6854 - val_sparse_top_k_categorical_accuracy: 0.8765 - lr: 1.0000e-05\n", "Epoch 55/100\n", "167/167 [==============================] - 44s 265ms/step - loss: 0.5399 - accuracy: 0.8795 - sparse_top_k_categorical_accuracy: 0.9582 - val_loss: 1.2891 - val_accuracy: 0.6871 - val_sparse_top_k_categorical_accuracy: 0.8759 - lr: 1.0000e-05\n", "Epoch 56/100\n", "167/167 [==============================] - 45s 268ms/step - loss: 0.5395 - accuracy: 0.8789 - sparse_top_k_categorical_accuracy: 0.9580 - val_loss: 1.2858 - val_accuracy: 0.6860 - val_sparse_top_k_categorical_accuracy: 0.8766 - lr: 1.0000e-05\n", "Epoch 57/100\n", "167/167 [==============================] - 45s 268ms/step - loss: 0.5401 - accuracy: 0.8778 - sparse_top_k_categorical_accuracy: 0.9582 - val_loss: 1.2869 - val_accuracy: 0.6852 - val_sparse_top_k_categorical_accuracy: 0.8771 - lr: 1.0000e-05\n", "Epoch 58/100\n", "167/167 [==============================] - 45s 268ms/step - loss: 0.5379 - accuracy: 0.8789 - sparse_top_k_categorical_accuracy: 0.9584 - val_loss: 1.2872 - val_accuracy: 0.6850 - val_sparse_top_k_categorical_accuracy: 0.8771 - lr: 1.0000e-05\n", "Epoch 59/100\n", "167/167 [==============================] - 45s 268ms/step - loss: 0.5389 - accuracy: 0.8789 - sparse_top_k_categorical_accuracy: 0.9579 - val_loss: 1.2868 - val_accuracy: 0.6847 - val_sparse_top_k_categorical_accuracy: 0.8767 - lr: 1.0000e-05\n", "Epoch 60/100\n", "167/167 [==============================] - 45s 270ms/step - loss: 0.5379 - accuracy: 0.8798 - sparse_top_k_categorical_accuracy: 0.9581 - val_loss: 1.2875 - val_accuracy: 0.6835 - val_sparse_top_k_categorical_accuracy: 0.8768 - lr: 1.0000e-05\n", "Epoch 61/100\n", "167/167 [==============================] - 45s 268ms/step - loss: 0.5362 - accuracy: 0.8801 - sparse_top_k_categorical_accuracy: 0.9582 - val_loss: 1.2897 - val_accuracy: 0.6844 - val_sparse_top_k_categorical_accuracy: 0.8754 - lr: 1.0000e-05\n", "Epoch 62/100\n", "167/167 [==============================] - 45s 268ms/step - loss: 0.5344 - accuracy: 0.8807 - sparse_top_k_categorical_accuracy: 0.9586 - val_loss: 1.2868 - val_accuracy: 0.6853 - val_sparse_top_k_categorical_accuracy: 0.8778 - lr: 1.0000e-05\n", "Epoch 63/100\n", "167/167 [==============================] - 44s 264ms/step - loss: 0.5357 - accuracy: 0.8793 - sparse_top_k_categorical_accuracy: 0.9585 - val_loss: 1.2879 - val_accuracy: 0.6849 - val_sparse_top_k_categorical_accuracy: 0.8771 - lr: 1.0000e-05\n", "Epoch 64/100\n", "167/167 [==============================] - 44s 264ms/step - loss: 0.5333 - accuracy: 0.8796 - sparse_top_k_categorical_accuracy: 0.9585 - val_loss: 1.2872 - val_accuracy: 0.6849 - val_sparse_top_k_categorical_accuracy: 0.8779 - lr: 1.0000e-05\n", "Epoch 65/100\n", "167/167 [==============================] - 44s 265ms/step - loss: 0.5334 - accuracy: 0.8815 - sparse_top_k_categorical_accuracy: 0.9583 - val_loss: 1.2879 - val_accuracy: 0.6841 - val_sparse_top_k_categorical_accuracy: 0.8775 - lr: 1.0000e-05\n"]}, {"data": {"text/plain": ["<keras.callbacks.History at 0x7f1d9c4f8110>"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["# fit the model with 100 epochs iteration\n", "model.fit(train_ds,\n", "          validation_data = val_ds,\n", "          callbacks = get_callbacks(),\n", "          epochs = 100)"]}, {"cell_type": "markdown", "id": "7f19b1a2", "metadata": {"papermill": {"duration": 0.600259, "end_time": "2023-03-16T16:11:05.487927", "exception": false, "start_time": "2023-03-16T16:11:04.887668", "status": "completed"}, "tags": []}, "source": ["<a id=\"section-five\"></a>\n", "# Submit Model"]}, {"cell_type": "markdown", "id": "712cbfb3", "metadata": {"papermill": {"duration": 0.599043, "end_time": "2023-03-16T16:11:06.731892", "exception": false, "start_time": "2023-03-16T16:11:06.132849", "status": "completed"}, "tags": []}, "source": ["Now it is time to submit. In this competition, we should submit the model itself."]}, {"cell_type": "code", "execution_count": 11, "id": "1427e3e4", "metadata": {"execution": {"iopub.execute_input": "2023-03-16T16:11:07.979087Z", "iopub.status.busy": "2023-03-16T16:11:07.978470Z", "iopub.status.idle": "2023-03-16T16:11:08.014480Z", "shell.execute_reply": "2023-03-16T16:11:08.013708Z"}, "papermill": {"duration": 0.700688, "end_time": "2023-03-16T16:11:08.029341", "exception": false, "start_time": "2023-03-16T16:11:07.328653", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model: \"model\"\n", "_________________________________________________________________\n", " Layer (type)                Output Shape              Param #   \n", "=================================================================\n", " input_1 (InputLayer)        [(None, None, 264)]       0         \n", "                                                                 \n", " dense (Dense)               (None, None, 512)         135680    \n", "                                                                 \n", " layer_normalization (LayerN  (None, None, 512)        1024      \n", " ormalization)                                                   \n", "                                                                 \n", " activation (Activation)     (None, None, 512)         0         \n", "                                                                 \n", " dropout (Dropout)           (None, None, 512)         0         \n", "                                                                 \n", " dense_1 (<PERSON><PERSON>)             (None, None, 256)         131328    \n", "                                                                 \n", " layer_normalization_1 (<PERSON><PERSON>  (None, None, 256)        512       \n", " rNormalization)                                                 \n", "                                                                 \n", " activation_1 (Activation)   (None, None, 256)         0         \n", "                                                                 \n", " dropout_1 (Dropout)         (None, None, 256)         0         \n", "                                                                 \n", " lstm (LSTM)                 (None, 250)               507000    \n", "                                                                 \n", " dense_2 (<PERSON><PERSON>)             (None, 250)               62750     \n", "                                                                 \n", "=================================================================\n", "Total params: 838,294\n", "Trainable params: 838,294\n", "Non-trainable params: 0\n", "_________________________________________________________________\n"]}], "source": ["model.summary(expand_nested=True)"]}, {"cell_type": "code", "execution_count": 12, "id": "26a8ba98", "metadata": {"execution": {"iopub.execute_input": "2023-03-16T16:11:09.275141Z", "iopub.status.busy": "2023-03-16T16:11:09.274777Z", "iopub.status.idle": "2023-03-16T16:11:09.282051Z", "shell.execute_reply": "2023-03-16T16:11:09.280965Z"}, "papermill": {"duration": 0.659013, "end_time": "2023-03-16T16:11:09.284211", "exception": false, "start_time": "2023-03-16T16:11:08.625198", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def get_inference_model(model):\n", "    inputs = tf.keras.Input(shape=(ROWS_PER_FRAME,3), name=\"inputs\")\n", "    \n", "    # drop most of the face mesh\n", "    x = tf.gather(inputs, LANDMARK_IDX, axis=1)\n", "\n", "    # fill nan\n", "    x = tf.where(tf.math.is_nan(x), tf.zeros_like(x), x)\n", "\n", "    # flatten landmark xyz coordinates ()\n", "    x = tf.concat([x[...,i] for i in range(3)], -1)\n", "\n", "    x = tf.expand_dims(x,0)\n", "    \n", "    # call trained model\n", "    out = model(x)\n", "    \n", "    # explicitly name the final (identity) layer for the submission format\n", "    out = layers.Activation(\"linear\", name=\"outputs\")(out)\n", "    \n", "    inference_model = tf.keras.Model(inputs=inputs, outputs=out)\n", "    inference_model.compile(loss=\"sparse_categorical_crossentropy\",\n", "                            metrics=\"accuracy\")\n", "    return inference_model"]}, {"cell_type": "code", "execution_count": 13, "id": "5d5a7e7e", "metadata": {"execution": {"iopub.execute_input": "2023-03-16T16:11:10.476775Z", "iopub.status.busy": "2023-03-16T16:11:10.476403Z", "iopub.status.idle": "2023-03-16T16:11:10.864096Z", "shell.execute_reply": "2023-03-16T16:11:10.863170Z"}, "papermill": {"duration": 1.029893, "end_time": "2023-03-16T16:11:10.905979", "exception": false, "start_time": "2023-03-16T16:11:09.876086", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model: \"model_1\"\n", "__________________________________________________________________________________________________\n", " Layer (type)                   Output Shape         Param #     Connected to                     \n", "==================================================================================================\n", " inputs (InputLayer)            [(None, 543, 3)]     0           []                               \n", "                                                                                                  \n", " tf.compat.v1.gather (TFOpLambd  (None, 88, 3)       0           ['inputs[0][0]']                 \n", " a)                                                                                               \n", "                                                                                                  \n", " tf.math.is_nan (TFOpLambda)    (None, 88, 3)        0           ['tf.compat.v1.gather[0][0]']    \n", "                                                                                                  \n", " tf.zeros_like (TFOpLambda)     (None, 88, 3)        0           ['tf.compat.v1.gather[0][0]']    \n", "                                                                                                  \n", " tf.where (TFOpLambda)          (None, 88, 3)        0           ['tf.math.is_nan[0][0]',         \n", "                                                                  'tf.zeros_like[0][0]',          \n", "                                                                  'tf.compat.v1.gather[0][0]']    \n", "                                                                                                  \n", " tf.__operators__.getitem (Slic  (None, 88)          0           ['tf.where[0][0]']               \n", " ingOpLambda)                                                                                     \n", "                                                                                                  \n", " tf.__operators__.getitem_1 (Sl  (None, 88)          0           ['tf.where[0][0]']               \n", " icingOpLambda)                                                                                   \n", "                                                                                                  \n", " tf.__operators__.getitem_2 (Sl  (None, 88)          0           ['tf.where[0][0]']               \n", " icingOpLambda)                                                                                   \n", "                                                                                                  \n", " tf.concat (TFOpLambda)         (None, 264)          0           ['tf.__operators__.getitem[0][0]'\n", "                                                                 , 'tf.__operators__.getitem_1[0][\n", "                                                                 0]',                             \n", "                                                                  'tf.__operators__.getitem_2[0][0\n", "                                                                 ]']                              \n", "                                                                                                  \n", " tf.expand_dims (TFOpLambda)    (1, None, 264)       0           ['tf.concat[0][0]']              \n", "                                                                                                  \n", " model (Functional)             (None, 250)          838294      ['tf.expand_dims[0][0]']         \n", "|¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯|\n", "| input_1 (InputLayer)         [(None, None, 264)]  0           []                               |\n", "|                                                                                                |\n", "| dense (Dense)                (None, None, 512)    135680      []                               |\n", "|                                                                                                |\n", "| layer_normalization (LayerNorm  (None, None, 512)  1024       []                               |\n", "| alization)                                                                                     |\n", "|                                                                                                |\n", "| activation (Activation)      (None, None, 512)    0           []                               |\n", "|                                                                                                |\n", "| dropout (Dropout)            (None, None, 512)    0           []                               |\n", "|                                                                                                |\n", "| dense_1 (<PERSON><PERSON>)              (None, None, 256)    131328      []                               |\n", "|                                                                                                |\n", "| layer_normalization_1 (<PERSON><PERSON><PERSON><PERSON>  (None, None, 256)  512        []                               |\n", "| rmalization)                                                                                   |\n", "|                                                                                                |\n", "| activation_1 (Activation)    (None, None, 256)    0           []                               |\n", "|                                                                                                |\n", "| dropout_1 (Dropout)          (None, None, 256)    0           []                               |\n", "|                                                                                                |\n", "| lstm (LSTM)                  (None, 250)          507000      []                               |\n", "|                                                                                                |\n", "| dense_2 (<PERSON><PERSON>)              (None, 250)          62750       []                               |\n", "¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯¯\n", " outputs (Activation)           (1, 250)             0           ['model[0][0]']                  \n", "                                                                                                  \n", "==================================================================================================\n", "Total params: 838,294\n", "Trainable params: 838,294\n", "Non-trainable params: 0\n", "__________________________________________________________________________________________________\n"]}], "source": ["inference_model = get_inference_model(model)\n", "inference_model.summary(expand_nested=True)"]}, {"cell_type": "code", "execution_count": 14, "id": "5fede5e8", "metadata": {"execution": {"iopub.execute_input": "2023-03-16T16:11:12.163840Z", "iopub.status.busy": "2023-03-16T16:11:12.163308Z", "iopub.status.idle": "2023-03-16T16:11:25.442723Z", "shell.execute_reply": "2023-03-16T16:11:25.441505Z"}, "papermill": {"duration": 13.886115, "end_time": "2023-03-16T16:11:25.445238", "exception": false, "start_time": "2023-03-16T16:11:11.559123", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  adding: model.tflite (deflated 8%)\r\n"]}], "source": ["# save the model\n", "converter = tf.lite.TFLiteConverter.from_keras_model(inference_model)\n", "tflite_model = converter.convert()\n", "model_path = \"model.tflite\"\n", "\n", "# submit the model\n", "with open(model_path, 'wb') as f:\n", "    f.write(tflite_model)\n", "!zip submission.zip $model_path"]}, {"cell_type": "code", "execution_count": null, "id": "cddcfaf8", "metadata": {"papermill": {"duration": 0.822271, "end_time": "2023-03-16T16:11:26.950484", "exception": false, "start_time": "2023-03-16T16:11:26.128213", "status": "completed"}, "tags": []}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.12"}, "papermill": {"default_parameters": {}, "duration": 3315.159476, "end_time": "2023-03-16T16:11:31.554557", "environment_variables": {}, "exception": null, "input_path": "__notebook__.ipynb", "output_path": "__notebook__.ipynb", "parameters": {}, "start_time": "2023-03-16T15:16:16.395081", "version": "2.4.0"}}, "nbformat": 4, "nbformat_minor": 5}