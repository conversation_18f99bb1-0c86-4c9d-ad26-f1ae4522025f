# Real-Time Sign Language Detection & Training System

A complete system for real-time sign language detection with automatic recording when signs match the selected target.

## 🏗️ Architecture

```
┌─────────────────┐    WebSocket    ┌─────────────────┐
│   React Frontend│ ◄──────────────► │ Python Backend  │
│                 │                 │                 │
│ • Video Capture │                 │ • MediaPipe     │
│ • Sign Selection│                 │ • TensorFlow    │
│ • UI Display    │                 │ • Recording     │
└─────────────────┘                 └─────────────────┘
```

## 🚀 Features

### ✅ Real-Time Sign Detection
- **MediaPipe Integration**: Extracts 543 3D landmarks (face, hands, pose)
- **TensorFlow Lite Model**: Fast inference for 80+ sign language signs
- **Confidence Scoring**: Only shows predictions above 70% confidence
- **Live Video Processing**: 10 FPS frame analysis with landmark visualization

### ✅ Smart Recording System
- **Automatic Recording**: Records only when detected sign matches selected target
- **Sign Matching**: Compares AI predictions with user-selected signs
- **Local Storage**: Saves recordings locally for AI training datasets
- **Visual Feedback**: Real-time indication when signs match

### ✅ Professional UI
- **Sign Selector**: Dropdown with 80+ signs and animated GIFs
- **Live Predictions**: Real-time display of detected signs with confidence
- **Connection Status**: Visual indicator of AI backend connectivity
- **Responsive Design**: Works on desktop and mobile devices

## 📁 Project Structure

```
├── python-backend/          # FastAPI backend for AI processing
│   ├── main.py             # Main FastAPI application
│   ├── requirements.txt    # Python dependencies
│   ├── start.py           # Startup script
│   └── recordings/        # Saved video recordings
├── training-frontend/      # React frontend application
│   ├── src/
│   │   ├── components/
│   │   │   └── TrainingPage.js
│   │   └── hooks/
│   │       └── useSignDetection.js
│   └── package.json
└── sign-language-recognition/  # ML models and data
    ├── streamlit/
    │   ├── model.tflite   # TensorFlow Lite model
    │   └── train.csv      # Sign labels
    └── weights/
        └── model.tflite   # Alternative model location
```

## 🛠️ Setup Instructions

### 1. Python Backend Setup

```bash
cd python-backend

# Install dependencies
pip install -r requirements.txt

# Start the backend server
python start.py
```

The backend will be available at:
- **API**: http://localhost:8000
- **WebSocket**: ws://localhost:8000/ws/detect
- **Health Check**: http://localhost:8000/health

### 2. React Frontend Setup

```bash
cd training-frontend

# Install dependencies
npm install

# Start the development server
npm start
```

The frontend will be available at: http://localhost:3000

### 3. Model Files

Ensure you have the required model files:
- `model.tflite` - TensorFlow Lite model for sign detection
- `train.csv` - Sign labels and mappings

Expected locations:
- `sign-language-recognition/streamlit/model.tflite`
- `sign-language-recognition/streamlit/train.csv`

## 🎯 How to Use

### 1. Start Both Servers
1. Run the Python backend: `python python-backend/start.py`
2. Run the React frontend: `npm start` in `training-frontend/`

### 2. Training Workflow
1. **Select a Sign**: Choose from 80+ available signs in the dropdown
2. **View Reference**: See the animated GIF showing how to perform the sign
3. **Start Recording**: Click "Start Neural Recording"
4. **Perform Sign**: Do the selected sign in front of the camera
5. **Automatic Recording**: System records only when your sign matches the target
6. **Visual Feedback**: Green highlight indicates successful sign matching

### 3. AI Detection Features
- **Real-time Predictions**: See detected signs with confidence percentages
- **Landmark Visualization**: MediaPipe draws hand, face, and pose landmarks
- **Connection Status**: Monitor AI backend connectivity
- **Recording Status**: Track when recordings are being saved

## 🤖 Technical Details

### AI Pipeline
1. **Video Capture**: WebRTC captures live video at 10 FPS
2. **MediaPipe Processing**: Extracts 1,629 features per frame (543 landmarks × 3D)
3. **Temporal Analysis**: Analyzes 30-frame sequences (~3 seconds)
4. **TensorFlow Inference**: Predicts sign with confidence score
5. **Smart Recording**: Records only when prediction matches target sign

### Performance Optimizations
- **TensorFlow Lite**: Optimized model for real-time inference
- **Frame Buffering**: Processes every 10th frame for efficiency
- **WebSocket Streaming**: Low-latency communication
- **Confidence Filtering**: Reduces false positives

## 📊 Available Signs

The system can detect 80+ signs including:
- **Basic**: hello, thank you, yes, no, please, sorry
- **Animals**: dog, cat, bird, bear, elephant, horse
- **Colors**: red, blue, green, yellow, black, white
- **Actions**: eat, sleep, run, jump, dance, look
- **Family**: mom, dad, brother, aunt
- **Objects**: car, book, chair, apple

## 🎥 Recording Output

Recordings are saved in `python-backend/recordings/` with format:
- **Filename**: `{sign_name}_{timestamp}.mp4`
- **Content**: Only frames where detected sign matches target
- **Quality**: Original camera resolution at 20 FPS
- **Use Case**: Perfect for creating AI training datasets

## 🔧 Configuration

### Backend Configuration
- **Confidence Threshold**: 0.7 (70%)
- **Frame Buffer Size**: 30 frames
- **Recording FPS**: 20
- **WebSocket Port**: 8000

### Frontend Configuration
- **Frame Capture Rate**: 100ms (10 FPS)
- **WebSocket Reconnection**: 3 seconds
- **UI Update Rate**: Real-time

## 🚨 Troubleshooting

### Backend Issues
- **Model not found**: Ensure `model.tflite` is in the correct location
- **Dependencies**: Run `pip install -r requirements.txt`
- **Port conflicts**: Change port in `main.py` if 8000 is occupied

### Frontend Issues
- **WebSocket connection**: Ensure backend is running on port 8000
- **Camera access**: Allow camera permissions in browser
- **Dependencies**: Run `npm install` to install packages

### Performance Issues
- **Slow detection**: Reduce frame capture rate in `useSignDetection.js`
- **High CPU usage**: Increase frame interval (default: 100ms)
- **Memory issues**: Restart backend periodically for long sessions

## 🎯 Next Steps

This system provides a complete foundation for:
1. **AI Training Data Collection**: Automatically recorded sign videos
2. **Real-time Sign Recognition**: Production-ready detection system
3. **Educational Applications**: Interactive sign language learning
4. **Research Projects**: Sign language analysis and improvement

The recorded videos can be used to train improved models, creating a continuous learning loop for better sign language recognition accuracy.
