import asyncio
import json
from fastapi import FastAP<PERSON>, WebSocket
from fastapi.middleware.cors import CORSMiddleware

app = FastAPI(title="Test Sign Detection API")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "Test server is running"}

@app.get("/health")
async def health():
    return {"status": "healthy", "test": True}

@app.websocket("/ws/detect")
async def websocket_endpoint(websocket: WebSocket):
    await websocket.accept()
    print("WebSocket client connected")
    
    try:
        while True:
            data = await websocket.receive_text()
            message = json.loads(data)
            print(f"Received: {message.get('type', 'unknown')}")
            
            if message.get("type") == "frame":
                # Send back a test prediction
                response = {
                    "type": "frame_processed",
                    "prediction": {
                        "sign": "hello",
                        "confidence": 0.85
                    },
                    "is_recording": False,
                    "sign_matched": False
                }
                await websocket.send_text(json.dumps(response))
                
    except Exception as e:
        print(f"WebSocket error: {e}")

if __name__ == "__main__":
    import uvicorn
    print("Starting test server on port 8000...")
    uvicorn.run(app, host="0.0.0.0", port=8000)
