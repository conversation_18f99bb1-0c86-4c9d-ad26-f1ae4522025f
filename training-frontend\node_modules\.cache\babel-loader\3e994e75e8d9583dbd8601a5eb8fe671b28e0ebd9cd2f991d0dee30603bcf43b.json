{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState, useRef, useCallback, useEffect } from 'react';\nconst BACKEND_URL = 'ws://localhost:8001/ws/detect';\nexport const useSignDetection = () => {\n  _s();\n  const [isConnected, setIsConnected] = useState(false);\n  const [prediction, setPrediction] = useState(null);\n  const [isRecording, setIsRecording] = useState(false);\n  const [recordingStatus, setRecordingStatus] = useState('');\n  const [processedFrame, setProcessedFrame] = useState(null);\n  const [signMatched, setSignMatched] = useState(false);\n  const [targetSign, setTargetSign] = useState('');\n  const wsRef = useRef(null);\n  const reconnectTimeoutRef = useRef(null);\n  const frameIntervalRef = useRef(null);\n  const connect = useCallback(() => {\n    try {\n      wsRef.current = new WebSocket(BACKEND_URL);\n      wsRef.current.onopen = () => {\n        console.log('Connected to sign detection backend');\n        setIsConnected(true);\n        setRecordingStatus('Connected to AI backend');\n      };\n      wsRef.current.onmessage = event => {\n        try {\n          const data = JSON.parse(event.data);\n          console.log('Received from backend:', data.type, data.prediction);\n          switch (data.type) {\n            case 'frame_processed':\n              setPrediction(data.prediction);\n              setProcessedFrame(data.processed_frame);\n              setSignMatched(data.sign_matched || false);\n              setTargetSign(data.target_sign || '');\n              if (data.prediction) {\n                console.log(`Detected: ${data.prediction.sign} (${Math.round(data.prediction.confidence * 100)}%)`);\n              }\n              break;\n            case 'recording_started':\n              setIsRecording(true);\n              setRecordingStatus(`Recording started for: ${data.target_sign}`);\n              break;\n            case 'recording_stopped':\n              setIsRecording(false);\n              if (data.result) {\n                setRecordingStatus(`Recording saved: ${data.result.frame_count} frames`);\n              } else {\n                setRecordingStatus('Recording stopped');\n              }\n              break;\n            default:\n              console.log('Unknown message type:', data.type);\n          }\n        } catch (error) {\n          console.error('Error parsing WebSocket message:', error);\n        }\n      };\n      wsRef.current.onclose = () => {\n        console.log('Disconnected from sign detection backend');\n        setIsConnected(false);\n        setRecordingStatus('Disconnected from backend');\n\n        // Attempt to reconnect after 3 seconds\n        reconnectTimeoutRef.current = setTimeout(() => {\n          console.log('Attempting to reconnect...');\n          connect();\n        }, 3000);\n      };\n      wsRef.current.onerror = error => {\n        console.error('WebSocket error:', error);\n        setRecordingStatus('Connection error');\n      };\n    } catch (error) {\n      console.error('Error connecting to WebSocket:', error);\n      setRecordingStatus('Failed to connect to backend');\n    }\n  }, []);\n  const disconnect = useCallback(() => {\n    if (reconnectTimeoutRef.current) {\n      clearTimeout(reconnectTimeoutRef.current);\n    }\n    if (frameIntervalRef.current) {\n      clearInterval(frameIntervalRef.current);\n    }\n    if (wsRef.current) {\n      wsRef.current.close();\n      wsRef.current = null;\n    }\n    setIsConnected(false);\n  }, []);\n  const sendFrame = useCallback(frameData => {\n    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {\n      const message = {\n        type: 'frame',\n        frame: frameData\n      };\n      wsRef.current.send(JSON.stringify(message));\n    }\n  }, []);\n  const startRecording = useCallback(targetSignName => {\n    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {\n      const message = {\n        type: 'start_recording',\n        target_sign: targetSignName\n      };\n      wsRef.current.send(JSON.stringify(message));\n      setTargetSign(targetSignName);\n    }\n  }, []);\n  const stopRecording = useCallback(() => {\n    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {\n      const message = {\n        type: 'stop_recording'\n      };\n      wsRef.current.send(JSON.stringify(message));\n    }\n  }, []);\n  const startFrameCapture = useCallback((webcamRef, interval = 200) => {\n    if (frameIntervalRef.current) {\n      clearInterval(frameIntervalRef.current);\n    }\n    frameIntervalRef.current = setInterval(() => {\n      if (webcamRef.current && isConnected) {\n        const imageSrc = webcamRef.current.getScreenshot();\n        if (imageSrc) {\n          console.log('Sending frame to backend');\n          sendFrame(imageSrc);\n        }\n      }\n    }, interval);\n  }, [isConnected, sendFrame]);\n  const stopFrameCapture = useCallback(() => {\n    if (frameIntervalRef.current) {\n      clearInterval(frameIntervalRef.current);\n      frameIntervalRef.current = null;\n    }\n  }, []);\n\n  // Auto-connect on mount\n  useEffect(() => {\n    connect();\n    return () => {\n      disconnect();\n    };\n  }, [connect, disconnect]);\n  return {\n    isConnected,\n    prediction,\n    isRecording,\n    recordingStatus,\n    processedFrame,\n    signMatched,\n    targetSign,\n    connect,\n    disconnect,\n    sendFrame,\n    startRecording,\n    stopRecording,\n    startFrameCapture,\n    stopFrameCapture\n  };\n};\n_s(useSignDetection, \"CYe++AhPKYcrUvT1LxTEmutdyZE=\");", "map": {"version": 3, "names": ["useState", "useRef", "useCallback", "useEffect", "BACKEND_URL", "useSignDetection", "_s", "isConnected", "setIsConnected", "prediction", "setPrediction", "isRecording", "setIsRecording", "recordingStatus", "setRecordingStatus", "processedFrame", "setProcessedFrame", "signMatched", "setSignMatched", "targetSign", "setTargetSign", "wsRef", "reconnectTimeoutRef", "frameIntervalRef", "connect", "current", "WebSocket", "onopen", "console", "log", "onmessage", "event", "data", "JSON", "parse", "type", "processed_frame", "sign_matched", "target_sign", "sign", "Math", "round", "confidence", "result", "frame_count", "error", "onclose", "setTimeout", "onerror", "disconnect", "clearTimeout", "clearInterval", "close", "sendFrame", "frameData", "readyState", "OPEN", "message", "frame", "send", "stringify", "startRecording", "targetSignName", "stopRecording", "startFrameCapture", "webcamRef", "interval", "setInterval", "imageSrc", "getScreenshot", "stopFrameCapture"], "sources": ["D:/ASL/training-frontend/src/hooks/useSignDetection.js"], "sourcesContent": ["import { useState, useRef, useCallback, useEffect } from 'react';\n\nconst BACKEND_URL = 'ws://localhost:8001/ws/detect';\n\nexport const useSignDetection = () => {\n  const [isConnected, setIsConnected] = useState(false);\n  const [prediction, setPrediction] = useState(null);\n  const [isRecording, setIsRecording] = useState(false);\n  const [recordingStatus, setRecordingStatus] = useState('');\n  const [processedFrame, setProcessedFrame] = useState(null);\n  const [signMatched, setSignMatched] = useState(false);\n  const [targetSign, setTargetSign] = useState('');\n  \n  const wsRef = useRef(null);\n  const reconnectTimeoutRef = useRef(null);\n  const frameIntervalRef = useRef(null);\n  \n  const connect = useCallback(() => {\n    try {\n      wsRef.current = new WebSocket(BACKEND_URL);\n      \n      wsRef.current.onopen = () => {\n        console.log('Connected to sign detection backend');\n        setIsConnected(true);\n        setRecordingStatus('Connected to AI backend');\n      };\n      \n      wsRef.current.onmessage = (event) => {\n        try {\n          const data = JSON.parse(event.data);\n          console.log('Received from backend:', data.type, data.prediction);\n\n          switch (data.type) {\n            case 'frame_processed':\n              setPrediction(data.prediction);\n              setProcessedFrame(data.processed_frame);\n              setSignMatched(data.sign_matched || false);\n              setTargetSign(data.target_sign || '');\n              if (data.prediction) {\n                console.log(`Detected: ${data.prediction.sign} (${Math.round(data.prediction.confidence * 100)}%)`);\n              }\n              break;\n              \n            case 'recording_started':\n              setIsRecording(true);\n              setRecordingStatus(`Recording started for: ${data.target_sign}`);\n              break;\n              \n            case 'recording_stopped':\n              setIsRecording(false);\n              if (data.result) {\n                setRecordingStatus(`Recording saved: ${data.result.frame_count} frames`);\n              } else {\n                setRecordingStatus('Recording stopped');\n              }\n              break;\n              \n            default:\n              console.log('Unknown message type:', data.type);\n          }\n        } catch (error) {\n          console.error('Error parsing WebSocket message:', error);\n        }\n      };\n      \n      wsRef.current.onclose = () => {\n        console.log('Disconnected from sign detection backend');\n        setIsConnected(false);\n        setRecordingStatus('Disconnected from backend');\n        \n        // Attempt to reconnect after 3 seconds\n        reconnectTimeoutRef.current = setTimeout(() => {\n          console.log('Attempting to reconnect...');\n          connect();\n        }, 3000);\n      };\n      \n      wsRef.current.onerror = (error) => {\n        console.error('WebSocket error:', error);\n        setRecordingStatus('Connection error');\n      };\n      \n    } catch (error) {\n      console.error('Error connecting to WebSocket:', error);\n      setRecordingStatus('Failed to connect to backend');\n    }\n  }, []);\n  \n  const disconnect = useCallback(() => {\n    if (reconnectTimeoutRef.current) {\n      clearTimeout(reconnectTimeoutRef.current);\n    }\n    \n    if (frameIntervalRef.current) {\n      clearInterval(frameIntervalRef.current);\n    }\n    \n    if (wsRef.current) {\n      wsRef.current.close();\n      wsRef.current = null;\n    }\n    \n    setIsConnected(false);\n  }, []);\n  \n  const sendFrame = useCallback((frameData) => {\n    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {\n      const message = {\n        type: 'frame',\n        frame: frameData\n      };\n      wsRef.current.send(JSON.stringify(message));\n    }\n  }, []);\n  \n  const startRecording = useCallback((targetSignName) => {\n    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {\n      const message = {\n        type: 'start_recording',\n        target_sign: targetSignName\n      };\n      wsRef.current.send(JSON.stringify(message));\n      setTargetSign(targetSignName);\n    }\n  }, []);\n  \n  const stopRecording = useCallback(() => {\n    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {\n      const message = {\n        type: 'stop_recording'\n      };\n      wsRef.current.send(JSON.stringify(message));\n    }\n  }, []);\n  \n  const startFrameCapture = useCallback((webcamRef, interval = 200) => {\n    if (frameIntervalRef.current) {\n      clearInterval(frameIntervalRef.current);\n    }\n\n    frameIntervalRef.current = setInterval(() => {\n      if (webcamRef.current && isConnected) {\n        const imageSrc = webcamRef.current.getScreenshot();\n        if (imageSrc) {\n          console.log('Sending frame to backend');\n          sendFrame(imageSrc);\n        }\n      }\n    }, interval);\n  }, [isConnected, sendFrame]);\n  \n  const stopFrameCapture = useCallback(() => {\n    if (frameIntervalRef.current) {\n      clearInterval(frameIntervalRef.current);\n      frameIntervalRef.current = null;\n    }\n  }, []);\n  \n  // Auto-connect on mount\n  useEffect(() => {\n    connect();\n    \n    return () => {\n      disconnect();\n    };\n  }, [connect, disconnect]);\n  \n  return {\n    isConnected,\n    prediction,\n    isRecording,\n    recordingStatus,\n    processedFrame,\n    signMatched,\n    targetSign,\n    connect,\n    disconnect,\n    sendFrame,\n    startRecording,\n    stopRecording,\n    startFrameCapture,\n    stopFrameCapture\n  };\n};\n"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,MAAM,EAAEC,WAAW,EAAEC,SAAS,QAAQ,OAAO;AAEhE,MAAMC,WAAW,GAAG,+BAA+B;AAEnD,OAAO,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGR,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACS,UAAU,EAAEC,aAAa,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACW,WAAW,EAAEC,cAAc,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACa,eAAe,EAAEC,kBAAkB,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACe,cAAc,EAAEC,iBAAiB,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACiB,WAAW,EAAEC,cAAc,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAMqB,KAAK,GAAGpB,MAAM,CAAC,IAAI,CAAC;EAC1B,MAAMqB,mBAAmB,GAAGrB,MAAM,CAAC,IAAI,CAAC;EACxC,MAAMsB,gBAAgB,GAAGtB,MAAM,CAAC,IAAI,CAAC;EAErC,MAAMuB,OAAO,GAAGtB,WAAW,CAAC,MAAM;IAChC,IAAI;MACFmB,KAAK,CAACI,OAAO,GAAG,IAAIC,SAAS,CAACtB,WAAW,CAAC;MAE1CiB,KAAK,CAACI,OAAO,CAACE,MAAM,GAAG,MAAM;QAC3BC,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;QAClDrB,cAAc,CAAC,IAAI,CAAC;QACpBM,kBAAkB,CAAC,yBAAyB,CAAC;MAC/C,CAAC;MAEDO,KAAK,CAACI,OAAO,CAACK,SAAS,GAAIC,KAAK,IAAK;QACnC,IAAI;UACF,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,KAAK,CAACC,IAAI,CAAC;UACnCJ,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEG,IAAI,CAACG,IAAI,EAAEH,IAAI,CAACvB,UAAU,CAAC;UAEjE,QAAQuB,IAAI,CAACG,IAAI;YACf,KAAK,iBAAiB;cACpBzB,aAAa,CAACsB,IAAI,CAACvB,UAAU,CAAC;cAC9BO,iBAAiB,CAACgB,IAAI,CAACI,eAAe,CAAC;cACvClB,cAAc,CAACc,IAAI,CAACK,YAAY,IAAI,KAAK,CAAC;cAC1CjB,aAAa,CAACY,IAAI,CAACM,WAAW,IAAI,EAAE,CAAC;cACrC,IAAIN,IAAI,CAACvB,UAAU,EAAE;gBACnBmB,OAAO,CAACC,GAAG,CAAC,aAAaG,IAAI,CAACvB,UAAU,CAAC8B,IAAI,KAAKC,IAAI,CAACC,KAAK,CAACT,IAAI,CAACvB,UAAU,CAACiC,UAAU,GAAG,GAAG,CAAC,IAAI,CAAC;cACrG;cACA;YAEF,KAAK,mBAAmB;cACtB9B,cAAc,CAAC,IAAI,CAAC;cACpBE,kBAAkB,CAAC,0BAA0BkB,IAAI,CAACM,WAAW,EAAE,CAAC;cAChE;YAEF,KAAK,mBAAmB;cACtB1B,cAAc,CAAC,KAAK,CAAC;cACrB,IAAIoB,IAAI,CAACW,MAAM,EAAE;gBACf7B,kBAAkB,CAAC,oBAAoBkB,IAAI,CAACW,MAAM,CAACC,WAAW,SAAS,CAAC;cAC1E,CAAC,MAAM;gBACL9B,kBAAkB,CAAC,mBAAmB,CAAC;cACzC;cACA;YAEF;cACEc,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEG,IAAI,CAACG,IAAI,CAAC;UACnD;QACF,CAAC,CAAC,OAAOU,KAAK,EAAE;UACdjB,OAAO,CAACiB,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QAC1D;MACF,CAAC;MAEDxB,KAAK,CAACI,OAAO,CAACqB,OAAO,GAAG,MAAM;QAC5BlB,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;QACvDrB,cAAc,CAAC,KAAK,CAAC;QACrBM,kBAAkB,CAAC,2BAA2B,CAAC;;QAE/C;QACAQ,mBAAmB,CAACG,OAAO,GAAGsB,UAAU,CAAC,MAAM;UAC7CnB,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;UACzCL,OAAO,CAAC,CAAC;QACX,CAAC,EAAE,IAAI,CAAC;MACV,CAAC;MAEDH,KAAK,CAACI,OAAO,CAACuB,OAAO,GAAIH,KAAK,IAAK;QACjCjB,OAAO,CAACiB,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;QACxC/B,kBAAkB,CAAC,kBAAkB,CAAC;MACxC,CAAC;IAEH,CAAC,CAAC,OAAO+B,KAAK,EAAE;MACdjB,OAAO,CAACiB,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD/B,kBAAkB,CAAC,8BAA8B,CAAC;IACpD;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMmC,UAAU,GAAG/C,WAAW,CAAC,MAAM;IACnC,IAAIoB,mBAAmB,CAACG,OAAO,EAAE;MAC/ByB,YAAY,CAAC5B,mBAAmB,CAACG,OAAO,CAAC;IAC3C;IAEA,IAAIF,gBAAgB,CAACE,OAAO,EAAE;MAC5B0B,aAAa,CAAC5B,gBAAgB,CAACE,OAAO,CAAC;IACzC;IAEA,IAAIJ,KAAK,CAACI,OAAO,EAAE;MACjBJ,KAAK,CAACI,OAAO,CAAC2B,KAAK,CAAC,CAAC;MACrB/B,KAAK,CAACI,OAAO,GAAG,IAAI;IACtB;IAEAjB,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM6C,SAAS,GAAGnD,WAAW,CAAEoD,SAAS,IAAK;IAC3C,IAAIjC,KAAK,CAACI,OAAO,IAAIJ,KAAK,CAACI,OAAO,CAAC8B,UAAU,KAAK7B,SAAS,CAAC8B,IAAI,EAAE;MAChE,MAAMC,OAAO,GAAG;QACdtB,IAAI,EAAE,OAAO;QACbuB,KAAK,EAAEJ;MACT,CAAC;MACDjC,KAAK,CAACI,OAAO,CAACkC,IAAI,CAAC1B,IAAI,CAAC2B,SAAS,CAACH,OAAO,CAAC,CAAC;IAC7C;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,cAAc,GAAG3D,WAAW,CAAE4D,cAAc,IAAK;IACrD,IAAIzC,KAAK,CAACI,OAAO,IAAIJ,KAAK,CAACI,OAAO,CAAC8B,UAAU,KAAK7B,SAAS,CAAC8B,IAAI,EAAE;MAChE,MAAMC,OAAO,GAAG;QACdtB,IAAI,EAAE,iBAAiB;QACvBG,WAAW,EAAEwB;MACf,CAAC;MACDzC,KAAK,CAACI,OAAO,CAACkC,IAAI,CAAC1B,IAAI,CAAC2B,SAAS,CAACH,OAAO,CAAC,CAAC;MAC3CrC,aAAa,CAAC0C,cAAc,CAAC;IAC/B;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,aAAa,GAAG7D,WAAW,CAAC,MAAM;IACtC,IAAImB,KAAK,CAACI,OAAO,IAAIJ,KAAK,CAACI,OAAO,CAAC8B,UAAU,KAAK7B,SAAS,CAAC8B,IAAI,EAAE;MAChE,MAAMC,OAAO,GAAG;QACdtB,IAAI,EAAE;MACR,CAAC;MACDd,KAAK,CAACI,OAAO,CAACkC,IAAI,CAAC1B,IAAI,CAAC2B,SAAS,CAACH,OAAO,CAAC,CAAC;IAC7C;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMO,iBAAiB,GAAG9D,WAAW,CAAC,CAAC+D,SAAS,EAAEC,QAAQ,GAAG,GAAG,KAAK;IACnE,IAAI3C,gBAAgB,CAACE,OAAO,EAAE;MAC5B0B,aAAa,CAAC5B,gBAAgB,CAACE,OAAO,CAAC;IACzC;IAEAF,gBAAgB,CAACE,OAAO,GAAG0C,WAAW,CAAC,MAAM;MAC3C,IAAIF,SAAS,CAACxC,OAAO,IAAIlB,WAAW,EAAE;QACpC,MAAM6D,QAAQ,GAAGH,SAAS,CAACxC,OAAO,CAAC4C,aAAa,CAAC,CAAC;QAClD,IAAID,QAAQ,EAAE;UACZxC,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;UACvCwB,SAAS,CAACe,QAAQ,CAAC;QACrB;MACF;IACF,CAAC,EAAEF,QAAQ,CAAC;EACd,CAAC,EAAE,CAAC3D,WAAW,EAAE8C,SAAS,CAAC,CAAC;EAE5B,MAAMiB,gBAAgB,GAAGpE,WAAW,CAAC,MAAM;IACzC,IAAIqB,gBAAgB,CAACE,OAAO,EAAE;MAC5B0B,aAAa,CAAC5B,gBAAgB,CAACE,OAAO,CAAC;MACvCF,gBAAgB,CAACE,OAAO,GAAG,IAAI;IACjC;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAtB,SAAS,CAAC,MAAM;IACdqB,OAAO,CAAC,CAAC;IAET,OAAO,MAAM;MACXyB,UAAU,CAAC,CAAC;IACd,CAAC;EACH,CAAC,EAAE,CAACzB,OAAO,EAAEyB,UAAU,CAAC,CAAC;EAEzB,OAAO;IACL1C,WAAW;IACXE,UAAU;IACVE,WAAW;IACXE,eAAe;IACfE,cAAc;IACdE,WAAW;IACXE,UAAU;IACVK,OAAO;IACPyB,UAAU;IACVI,SAAS;IACTQ,cAAc;IACdE,aAAa;IACbC,iBAAiB;IACjBM;EACF,CAAC;AACH,CAAC;AAAChE,EAAA,CAnLWD,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}