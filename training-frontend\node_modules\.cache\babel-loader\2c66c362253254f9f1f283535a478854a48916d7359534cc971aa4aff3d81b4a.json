{"ast": null, "code": "import _taggedTemplateLiteral from\"D:/ASL/training-frontend/node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js\";var _templateObject,_templateObject2,_templateObject3,_templateObject4,_templateObject5,_templateObject6,_templateObject7,_templateObject8,_templateObject9,_templateObject0,_templateObject1,_templateObject10,_templateObject11,_templateObject12,_templateObject13,_templateObject14,_templateObject15,_templateObject16,_templateObject17,_templateObject18,_templateObject19,_templateObject20,_templateObject21,_templateObject22,_templateObject23,_templateObject24,_templateObject25,_templateObject26,_templateObject27,_templateObject28,_templateObject29,_templateObject30,_templateObject31,_templateObject32,_templateObject33,_templateObject34,_templateObject35;import{useState,useRef,useCallback,useEffect}from'react';import styled from'styled-components';import Webcam from'react-webcam';import{<PERSON>,<PERSON>,<PERSON>Lef<PERSON>,Play,Square,Download,Eye,Target,Wifi,WifiOff}from'lucide-react';import{useSignDetection}from'../hooks/useSignDetection';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const TrainingContainer=styled.div(_templateObject||(_templateObject=_taggedTemplateLiteral([\"\\n  min-height: 100vh;\\n  background: var(--bg-primary);\\n  position: relative;\\n  overflow-x: hidden;\\n\\n  &::before {\\n    content: '';\\n    position: fixed;\\n    top: 0;\\n    left: 0;\\n    right: 0;\\n    bottom: 0;\\n    background:\\n      radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),\\n      radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.03) 0%, transparent 50%);\\n    pointer-events: none;\\n    z-index: 0;\\n  }\\n\"])));const Navigation=styled.nav(_templateObject2||(_templateObject2=_taggedTemplateLiteral([\"\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  z-index: 50;\\n  background: var(--bg-glass);\\n  backdrop-filter: blur(20px);\\n  border-bottom: 1px solid var(--border-neural);\\n  padding: var(--space-4) 0;\\n  transition: var(--transition-normal);\\n\"])));const NavContainer=styled.div(_templateObject3||(_templateObject3=_taggedTemplateLiteral([\"\\n  max-width: 1400px;\\n  margin: 0 auto;\\n  padding: 0 var(--space-6);\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n\\n  @media (max-width: 768px) {\\n    padding: 0 var(--space-4);\\n  }\\n\"])));const Logo=styled.div(_templateObject4||(_templateObject4=_taggedTemplateLiteral([\"\\n  font-family: var(--font-primary);\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n  background: var(--text-gradient);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n  display: flex;\\n  align-items: center;\\n  gap: var(--space-3);\\n\\n  @media (max-width: 768px) {\\n    font-size: 1.25rem;\\n    gap: var(--space-2);\\n  }\\n\"])));const LogoIcon=styled.div(_templateObject5||(_templateObject5=_taggedTemplateLiteral([\"\\n  width: 40px;\\n  height: 40px;\\n  background: var(--bg-neural);\\n  border-radius: var(--radius-lg);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: white;\\n  box-shadow: var(--shadow-neural);\\n\\n  @media (max-width: 768px) {\\n    width: 36px;\\n    height: 36px;\\n  }\\n\"])));const BackButton=styled.button(_templateObject6||(_templateObject6=_taggedTemplateLiteral([\"\\n  background: var(--bg-glass);\\n  color: var(--text-secondary);\\n  border: 1px solid var(--border-neural);\\n  padding: var(--space-3) var(--space-5);\\n  border-radius: var(--radius-xl);\\n  font-size: 0.9rem;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: var(--transition-normal);\\n  display: flex;\\n  align-items: center;\\n  gap: var(--space-2);\\n  backdrop-filter: blur(10px);\\n\\n  &:hover {\\n    background: var(--primary-50);\\n    color: var(--primary-600);\\n    border-color: var(--primary-300);\\n    transform: translateY(-1px);\\n    box-shadow: var(--shadow-lg);\\n  }\\n\\n  @media (max-width: 768px) {\\n    padding: var(--space-2) var(--space-4);\\n    font-size: 0.85rem;\\n  }\\n\"])));const PageTitle=styled.h1(_templateObject7||(_templateObject7=_taggedTemplateLiteral([\"\\n  font-family: var(--font-primary);\\n  font-size: 2.5rem;\\n  font-weight: 700;\\n  background: var(--text-gradient);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n  text-align: center;\\n  margin-bottom: var(--space-4);\\n  letter-spacing: -0.02em;\\n\\n  @media (max-width: 768px) {\\n    font-size: 2rem;\\n  }\\n\"])));const PageSubtitle=styled.p(_templateObject8||(_templateObject8=_taggedTemplateLiteral([\"\\n  font-size: 1.125rem;\\n  color: var(--text-secondary);\\n  text-align: center;\\n  margin-bottom: var(--space-16);\\n  max-width: 700px;\\n  margin-left: auto;\\n  margin-right: auto;\\n  line-height: 1.6;\\n\\n  @media (max-width: 768px) {\\n    margin-bottom: var(--space-12);\\n    font-size: 1rem;\\n  }\\n\"])));const StatusBadge=styled.div(_templateObject9||(_templateObject9=_taggedTemplateLiteral([\"\\n  display: inline-flex;\\n  align-items: center;\\n  gap: var(--space-2);\\n  background: var(--bg-glass);\\n  border: 1px solid var(--border-neural);\\n  border-radius: var(--radius-full);\\n  padding: var(--space-2) var(--space-4);\\n  margin-bottom: var(--space-8);\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: var(--text-accent);\\n  backdrop-filter: blur(10px);\\n  box-shadow: var(--shadow-glow);\\n\\n  @media (max-width: 768px) {\\n    font-size: 0.8rem;\\n    padding: var(--space-2) var(--space-3);\\n  }\\n\"])));const MainContent=styled.main(_templateObject0||(_templateObject0=_taggedTemplateLiteral([\"\\n  padding: var(--space-20) var(--space-4) var(--space-16);\\n  max-width: 1200px;\\n  margin: 0 auto;\\n\\n  @media (max-width: 768px) {\\n    padding: var(--space-12) var(--space-3) var(--space-8);\\n    max-width: 100%;\\n  }\\n\"])));const TrainingGrid=styled.div(_templateObject1||(_templateObject1=_taggedTemplateLiteral([\"\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: var(--space-8);\\n  max-width: 1200px;\\n  margin: 0 auto var(--space-12);\\n\\n  @media (max-width: 1024px) {\\n    grid-template-columns: 1fr;\\n    gap: var(--space-4);\\n  }\\n\\n  @media (max-width: 768px) {\\n    gap: var(--space-3);\\n    margin: 0 auto var(--space-6);\\n  }\\n\"])));const CameraSection=styled.div(_templateObject10||(_templateObject10=_taggedTemplateLiteral([\"\\n  background: var(--bg-glass);\\n  border-radius: var(--radius-2xl);\\n  padding: var(--space-10);\\n  border: 1px solid var(--border-neural);\\n  backdrop-filter: blur(20px);\\n  box-shadow: var(--shadow-lg);\\n  transition: var(--transition-normal);\\n  position: relative;\\n  overflow: hidden;\\n\\n  @media (max-width: 768px) {\\n    padding: var(--space-6);\\n    border-radius: var(--radius-xl);\\n  }\\n\\n  &::before {\\n    content: '';\\n    position: absolute;\\n    top: 0;\\n    left: 0;\\n    right: 0;\\n    height: 4px;\\n    background: var(--bg-neural);\\n    transform: scaleX(0);\\n    transition: var(--transition-normal);\\n  }\\n\\n  &:hover {\\n    box-shadow: var(--shadow-xl), var(--shadow-glow);\\n    border-color: var(--primary-300);\\n\\n    &::before {\\n      transform: scaleX(1);\\n    }\\n  }\\n\\n  @media (max-width: 768px) {\\n    padding: var(--space-8);\\n  }\\n\"])));const SectionTitle=styled.h2(_templateObject11||(_templateObject11=_taggedTemplateLiteral([\"\\n  font-family: var(--font-primary);\\n  font-size: 1.25rem;\\n  margin-bottom: var(--space-6);\\n  color: var(--text-primary);\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: var(--space-3);\\n\\n  @media (max-width: 768px) {\\n    font-size: 1.125rem;\\n    margin-bottom: var(--space-4);\\n  }\\n\"])));const SectionIcon=styled.div(_templateObject12||(_templateObject12=_taggedTemplateLiteral([\"\\n  width: 36px;\\n  height: 36px;\\n  background: var(--bg-neural);\\n  border-radius: var(--radius-lg);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: white;\\n  box-shadow: var(--shadow-neural);\\n\\n  @media (max-width: 768px) {\\n    width: 32px;\\n    height: 32px;\\n  }\\n\"])));const WebcamContainer=styled.div(_templateObject13||(_templateObject13=_taggedTemplateLiteral([\"\\n  position: relative;\\n  border-radius: var(--radius-2xl);\\n  overflow: hidden;\\n  background: var(--neural-100);\\n  aspect-ratio: 4/3;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  border: 3px solid var(--border-neural);\\n  margin-bottom: var(--space-6);\\n  box-shadow: var(--shadow-lg);\\n\\n  @media (max-width: 768px) {\\n    aspect-ratio: 3/4;\\n    margin-bottom: var(--space-4);\\n    border-radius: var(--radius-xl);\\n    border-width: 2px;\\n  }\\n\"])));const StyledWebcam=styled(Webcam)(_templateObject14||(_templateObject14=_taggedTemplateLiteral([\"\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n\"])));const RecordingOverlay=styled.div(_templateObject15||(_templateObject15=_taggedTemplateLiteral([\"\\n  position: absolute;\\n  top: var(--space-4);\\n  right: var(--space-4);\\n  background: \",\";\\n  color: white;\\n  padding: var(--space-3) var(--space-5);\\n  border-radius: var(--radius-full);\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: var(--space-2);\\n  box-shadow: var(--shadow-lg);\\n  animation: \",\";\\n\\n  @keyframes pulse {\\n    0%, 100% { opacity: 1; transform: scale(1); }\\n    50% { opacity: 0.8; transform: scale(1.05); }\\n  }\\n\"])),props=>props.isRecording?'var(--error-500)':'var(--neural-600)',props=>props.isRecording?'pulse 1.5s infinite':'none');const SignSection=styled.div(_templateObject16||(_templateObject16=_taggedTemplateLiteral([\"\\n  background: var(--bg-primary);\\n  border-radius: var(--radius-2xl);\\n  padding: var(--space-8);\\n  border: 1px solid var(--border-light);\\n  box-shadow: var(--shadow-lg);\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  text-align: center;\\n  transition: all 0.3s ease;\\n\\n  &:hover {\\n    box-shadow: var(--shadow-xl);\\n    border-color: var(--primary-200);\\n  }\\n\\n  @media (max-width: 768px) {\\n    padding: var(--space-6);\\n  }\\n\"])));const SignSelector=styled.select(_templateObject17||(_templateObject17=_taggedTemplateLiteral([\"\\n  width: 100%;\\n  padding: var(--space-3) var(--space-4);\\n  border: 2px solid var(--border-light);\\n  border-radius: var(--radius-lg);\\n  background: var(--bg-primary);\\n  color: var(--text-primary);\\n  font-size: 1rem;\\n  font-weight: 500;\\n  margin-bottom: var(--space-4);\\n  cursor: pointer;\\n  transition: var(--transition-normal);\\n\\n  &:focus {\\n    outline: none;\\n    border-color: var(--primary-500);\\n    box-shadow: 0 0 0 3px var(--primary-100);\\n  }\\n\\n  &:hover {\\n    border-color: var(--primary-300);\\n  }\\n\\n  option {\\n    padding: var(--space-2);\\n    background: var(--bg-primary);\\n    color: var(--text-primary);\\n  }\\n\\n  @media (max-width: 768px) {\\n    font-size: 1.125rem;\\n    padding: var(--space-4);\\n    margin-bottom: var(--space-3);\\n  }\\n\"])));const SignDisplay=styled.div(_templateObject18||(_templateObject18=_taggedTemplateLiteral([\"\\n  width: 300px;\\n  height: 300px;\\n  background: var(--primary-50);\\n  border-radius: var(--radius-2xl);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-bottom: var(--space-6);\\n  border: 2px solid var(--primary-200);\\n  transition: all 0.3s ease;\\n  overflow: hidden;\\n\\n  img {\\n    width: 100%;\\n    height: 100%;\\n    object-fit: cover;\\n    border-radius: var(--radius-xl);\\n  }\\n\\n  &:hover {\\n    transform: scale(1.02);\\n    border-color: var(--primary-300);\\n  }\\n\\n  @media (max-width: 768px) {\\n    width: 250px;\\n    height: 250px;\\n  }\\n\"])));const SignName=styled.h3(_templateObject19||(_templateObject19=_taggedTemplateLiteral([\"\\n  font-family: var(--font-primary);\\n  font-size: 1.5rem;\\n  margin-bottom: var(--space-3);\\n  color: var(--text-primary);\\n  font-weight: 700;\\n\\n  @media (max-width: 768px) {\\n    font-size: 1.25rem;\\n  }\\n\"])));const SignDescription=styled.p(_templateObject20||(_templateObject20=_taggedTemplateLiteral([\"\\n  text-align: center;\\n  line-height: 1.6;\\n  color: var(--text-secondary);\\n  font-size: 0.9rem;\\n  font-weight: 400;\\n  max-width: 280px;\\n\"])));const ControlsSection=styled.div(_templateObject21||(_templateObject21=_taggedTemplateLiteral([\"\\n  display: flex;\\n  justify-content: center;\\n  gap: var(--space-4);\\n  margin-top: var(--space-8);\\n\\n  @media (max-width: 768px) {\\n    flex-direction: column;\\n    align-items: center;\\n    gap: var(--space-3);\\n  }\\n\"])));const ControlButton=styled.button(_templateObject22||(_templateObject22=_taggedTemplateLiteral([\"\\n  background: \",\";\\n  border: \",\";\\n  color: \",\";\\n  padding: var(--space-3) var(--space-6);\\n  border-radius: var(--radius-lg);\\n  cursor: pointer;\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  transition: all 0.2s ease;\\n  min-width: 160px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: var(--space-2);\\n\\n  @media (max-width: 768px) {\\n    padding: var(--space-4) var(--space-8);\\n    font-size: 1rem;\\n    min-width: 180px;\\n    border-radius: var(--radius-xl);\\n  }\\n  box-shadow: \",\";\\n\\n  &:hover {\\n    transform: translateY(-2px);\\n    box-shadow: \",\";\\n    background: \",\";\\n  }\\n\\n  &:disabled {\\n    opacity: 0.5;\\n    cursor: not-allowed;\\n    transform: none;\\n  }\\n\\n  @media (max-width: 768px) {\\n    width: 100%;\\n    max-width: 280px;\\n  }\\n\"])),props=>props.variant==='primary'?'var(--primary-600)':'var(--bg-primary)',props=>props.variant==='primary'?'none':'1px solid var(--border-medium)',props=>props.variant==='primary'?'white':'var(--text-primary)',props=>props.variant==='primary'?'var(--shadow-lg)':'var(--shadow-sm)',props=>props.variant==='primary'?'var(--shadow-xl)':'var(--shadow-md)',props=>props.variant==='primary'?'var(--primary-700)':'var(--gray-50)');const StatusMessage=styled.div(_templateObject23||(_templateObject23=_taggedTemplateLiteral([\"\\n  text-align: center;\\n  margin-top: var(--space-6);\\n  padding: var(--space-4) var(--space-6);\\n  border-radius: var(--radius-lg);\\n  background: \",\";\\n  color: white;\\n  font-weight: 500;\\n  font-size: 0.875rem;\\n  max-width: 400px;\\n  margin-left: auto;\\n  margin-right: auto;\\n\"])),props=>props.type==='success'?'var(--success-500)':props.type==='error'?'var(--error-500)':'var(--primary-600)');const RecordingsSection=styled.div(_templateObject24||(_templateObject24=_taggedTemplateLiteral([\"\\n  margin-top: var(--space-16);\\n  background: var(--bg-secondary);\\n  padding: var(--space-12) var(--space-4);\\n  border-radius: var(--radius-2xl);\\n  max-width: 1200px;\\n  margin-left: auto;\\n  margin-right: auto;\\n\"])));const RecordingsTitle=styled.h3(_templateObject25||(_templateObject25=_taggedTemplateLiteral([\"\\n  font-family: var(--font-primary);\\n  color: var(--text-primary);\\n  margin-bottom: var(--space-8);\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n  text-align: center;\\n\"])));const RecordingsGrid=styled.div(_templateObject26||(_templateObject26=_taggedTemplateLiteral([\"\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\n  gap: var(--space-6);\\n\\n  @media (max-width: 768px) {\\n    grid-template-columns: 1fr;\\n    gap: var(--space-4);\\n  }\\n\"])));const RecordingCard=styled.div(_templateObject27||(_templateObject27=_taggedTemplateLiteral([\"\\n  background: var(--bg-primary);\\n  padding: var(--space-6);\\n  border-radius: var(--radius-xl);\\n  border: 1px solid var(--border-light);\\n  box-shadow: var(--shadow-sm);\\n  transition: all 0.3s ease;\\n\\n  &:hover {\\n    transform: translateY(-2px);\\n    border-color: var(--primary-200);\\n    box-shadow: var(--shadow-lg);\\n  }\\n\"])));const RecordingTitle=styled.p(_templateObject28||(_templateObject28=_taggedTemplateLiteral([\"\\n  margin: 0 0 var(--space-2) 0;\\n  color: var(--text-primary);\\n  font-weight: 600;\\n  font-size: 1rem;\\n  font-family: var(--font-primary);\\n\"])));const RecordingTime=styled.p(_templateObject29||(_templateObject29=_taggedTemplateLiteral([\"\\n  margin: 0 0 var(--space-4) 0;\\n  font-size: 0.8rem;\\n  color: var(--text-tertiary);\\n\"])));const DownloadButton=styled.button(_templateObject30||(_templateObject30=_taggedTemplateLiteral([\"\\n  background: var(--primary-600);\\n  border: none;\\n  border-radius: var(--radius-lg);\\n  padding: var(--space-2) var(--space-4);\\n  color: white;\\n  cursor: pointer;\\n  font-size: 0.8rem;\\n  font-weight: 500;\\n  transition: all 0.2s ease;\\n  display: flex;\\n  align-items: center;\\n  gap: var(--space-2);\\n  margin: 0 auto;\\n\\n  &:hover {\\n    background: var(--primary-700);\\n    transform: translateY(-1px);\\n  }\\n\"])));const PredictionDisplay=styled.div(_templateObject31||(_templateObject31=_taggedTemplateLiteral([\"\\n  background: var(--bg-glass);\\n  border: 2px solid \",\";\\n  border-radius: var(--radius-xl);\\n  padding: var(--space-4);\\n  margin-bottom: var(--space-4);\\n  text-align: center;\\n  transition: var(--transition-normal);\\n  backdrop-filter: blur(10px);\\n  opacity: \",\";\\n  min-height: 80px;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n\\n  \",\"\\n\\n  \",\"\\n\\n  @keyframes pulse {\\n    0%, 100% { transform: scale(1); }\\n    50% { transform: scale(1.02); }\\n  }\\n\\n  @media (max-width: 768px) {\\n    padding: var(--space-3);\\n    margin-bottom: var(--space-3);\\n    min-height: 70px;\\n  }\\n\"])),props=>{if(props.matched)return'var(--success-400)';if(props.isStale)return'var(--warning-300)';return'var(--border-light)';},props=>props.isStale?0.7:1,props=>props.matched&&\"\\n    background: var(--success-50);\\n    box-shadow: 0 0 20px var(--success-200);\\n    animation: pulse 1s ease-in-out;\\n  \",props=>props.isStale&&\"\\n    background: var(--warning-50);\\n  \");const PredictionText=styled.div(_templateObject32||(_templateObject32=_taggedTemplateLiteral([\"\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: \",\";\\n  margin-bottom: var(--space-2);\\n\\n  @media (max-width: 768px) {\\n    font-size: 1.125rem;\\n  }\\n\"])),props=>{if(props.matched)return'var(--success-700)';if(props.isStale)return'var(--warning-700)';return'var(--text-primary)';});const ConfidenceBar=styled.div(_templateObject33||(_templateObject33=_taggedTemplateLiteral([\"\\n  width: 100%;\\n  height: 8px;\\n  background: var(--bg-secondary);\\n  border-radius: var(--radius-full);\\n  overflow: hidden;\\n  margin-top: var(--space-2);\\n\"])));const ConfidenceFill=styled.div(_templateObject34||(_templateObject34=_taggedTemplateLiteral([\"\\n  height: 100%;\\n  background: \",\";\\n  width: \",\"%;\\n  transition: width 0.3s ease;\\n\"])),props=>{if(props.confidence>0.8)return'var(--success-500)';if(props.confidence>0.6)return'var(--warning-500)';return'var(--error-500)';},props=>props.confidence*100);const ConnectionStatus=styled.div(_templateObject35||(_templateObject35=_taggedTemplateLiteral([\"\\n  display: flex;\\n  align-items: center;\\n  gap: var(--space-2);\\n  padding: var(--space-2) var(--space-3);\\n  border-radius: var(--radius-lg);\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  background: \",\";\\n  color: \",\";\\n  border: 1px solid \",\";\\n\"])),props=>props.connected?'var(--success-50)':'var(--error-50)',props=>props.connected?'var(--success-700)':'var(--error-700)',props=>props.connected?'var(--success-200)':'var(--error-200)');// Sign language data with GIFs (matching Streamlit app)\nconst signLanguageData={\"after\":{name:\"After\",gif:\"https://lifeprint.com/asl101/gifs/a/after-over-across.gif\",description:\"Move your dominant hand over and past your non-dominant hand\"},\"airplane\":{name:\"Airplane\",gif:\"https://www.lifeprint.com/asl101/gifs/a/airplane-flying.gif\",description:\"Extend your hand like a plane and move it through the air\"},\"all\":{name:\"All\",gif:\"https://lifeprint.com/asl101/gifs/a/all-whole.gif\",description:\"Circle your dominant hand around your non-dominant hand\"},\"alligator\":{name:\"Alligator\",gif:\"https://lifeprint.com/asl101/gifs/a/alligator.gif\",description:\"Clap your hands together like an alligator's mouth\"},\"animal\":{name:\"Animal\",gif:\"https://www.lifeprint.com/asl101/gifs-animated/animal.gif\",description:\"Place fingertips on chest and move hands back and forth\"},\"any\":{name:\"Any\",gif:\"https://lifeprint.com/asl101/gifs/a/any.gif\",description:\"Point with index finger and twist your wrist\"},\"apple\":{name:\"Apple\",gif:\"https://media.giphy.com/media/l0HlHb4dtZZiMYEA8/giphy.gif\",description:\"Twist your knuckle against your cheek\"},\"arm\":{name:\"Arm\",gif:\"https://th.bing.com/th/id/OIP.KkJLqfbp6XEHiIe-jOUb2AHaEc?w=251&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",description:\"Pat your arm with your opposite hand\"},\"aunt\":{name:\"Aunt\",gif:\"https://th.bing.com/th/id/OIP.Yz5UUZdNTrVWXf72we_N6wHaHa?rs=1&pid=ImgDetMain\",description:\"Make an 'A' handshape near your cheek and shake it\"},\"baby\":{name:\"Baby\",gif:\"https://media.giphy.com/media/l0HlHb4dtZZiMYEA8/giphy.gif\",description:\"Rock your arms as if holding a baby\"},\"ball\":{name:\"Ball\",gif:\"https://lifeprint.com/asl101/gifs/b/ball.gif\",description:\"Cup your hands as if holding a ball\"},\"banana\":{name:\"Banana\",gif:\"https://media.giphy.com/media/l0HlHb4dtZZiMYEA8/giphy.gif\",description:\"Peel an imaginary banana with your fingers\"},\"bear\":{name:\"Bear\",gif:\"https://lifeprint.com/asl101/gifs/b/bear.gif\",description:\"Cross your arms and scratch like a bear\"},\"beautiful\":{name:\"Beautiful\",gif:\"https://media.giphy.com/media/l0HlHb4dtZZiMYEA8/giphy.gif\",description:\"Circle your face with your hand and close it into a fist\"},\"bed\":{name:\"Bed\",gif:\"https://lifeprint.com/asl101/gifs/b/bed.gif\",description:\"Rest your head on your hands as if sleeping\"},\"bee\":{name:\"Bee\",gif:\"https://lifeprint.com/asl101/gifs/b/bee.gif\",description:\"Pinch your cheek and brush away as if swatting a bee\"},\"bird\":{name:\"Bird\",gif:\"https://lifeprint.com/asl101/gifs/b/bird.gif\",description:\"Pinch your fingers together near your mouth like a beak\"},\"black\":{name:\"Black\",gif:\"https://lifeprint.com/asl101/gifs/b/black.gif\",description:\"Draw your index finger across your forehead\"},\"blue\":{name:\"Blue\",gif:\"https://lifeprint.com/asl101/gifs/b/blue.gif\",description:\"Shake a 'B' handshape\"},\"book\":{name:\"Book\",gif:\"https://lifeprint.com/asl101/gifs/b/book.gif\",description:\"Open your hands like opening a book\"},\"boy\":{name:\"Boy\",gif:\"https://lifeprint.com/asl101/gifs/b/boy.gif\",description:\"Snap your fingers at your forehead\"},\"brother\":{name:\"Brother\",gif:\"https://lifeprint.com/asl101/gifs/b/brother.gif\",description:\"Make an 'L' shape and point to your forehead, then point forward\"},\"brown\":{name:\"Brown\",gif:\"https://lifeprint.com/asl101/gifs/b/brown.gif\",description:\"Slide your index finger down your cheek\"},\"bug\":{name:\"Bug\",gif:\"https://lifeprint.com/asl101/gifs/b/bug.gif\",description:\"Pinch your nose with your thumb and index finger\"},\"butterfly\":{name:\"Butterfly\",gif:\"https://lifeprint.com/asl101/gifs/b/butterfly.gif\",description:\"Cross your thumbs and flutter your fingers like wings\"},\"car\":{name:\"Car\",gif:\"https://lifeprint.com/asl101/gifs/c/car.gif\",description:\"Pretend to steer a car with both hands\"},\"cat\":{name:\"Cat\",gif:\"https://lifeprint.com/asl101/gifs/c/cat.gif\",description:\"Pinch your cheek and pull out like whiskers\"},\"chair\":{name:\"Chair\",gif:\"https://lifeprint.com/asl101/gifs/c/chair.gif\",description:\"Tap your fingers on your other hand like sitting\"},\"clean\":{name:\"Clean\",gif:\"https://lifeprint.com/asl101/gifs/c/clean.gif\",description:\"Wipe one palm with the other\"},\"cold\":{name:\"Cold\",gif:\"https://media.giphy.com/media/l0HlHb4dtZZiMYEA8/giphy.gif\",description:\"Shiver with both hands in fists\"},\"cow\":{name:\"Cow\",gif:\"https://lifeprint.com/asl101/gifs/c/cow.gif\",description:\"Twist your thumb at your temple like a horn\"},\"cry\":{name:\"Cry\",gif:\"https://lifeprint.com/asl101/gifs/c/cry.gif\",description:\"Draw tears down your cheeks with your index fingers\"},\"cute\":{name:\"Cute\",gif:\"https://lifeprint.com/asl101/gifs/c/cute-sugar.gif\",description:\"Brush your chin with your fingers\"},\"dad\":{name:\"Dad\",gif:\"https://media.giphy.com/media/l0MYQcLKwtl5v6H1S/giphy.gif\",description:\"Tap your forehead with your thumb\"},\"dance\":{name:\"Dance\",gif:\"https://www.bing.com/th/id/OGC.********************************?pid=1.7&rurl=https%3a%2f%2fmedia.giphy.com%2fmedia%2f3o7TKMspYQjQTbOz2U%2fgiphy.gif&ehk=h%2bdBHCxuoOT89ovSy5uTk6MCL9acaBEV6ld9lrVDRF4%3d\",description:\"Swing your fingers over your palm like dancing\"},\"dirty\":{name:\"Dirty\",gif:\"https://th.bing.com/th/id/OIP.wRA7r1OPPUuEoLL4Hds9jAHaHa?rs=1&pid=ImgDetMain\",description:\"Wiggle your fingers under your chin\"},\"dog\":{name:\"Dog\",gif:\"https://th.bing.com/th/id/R.********************************?rik=uVshGsOHXgldoQ&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fd%2fdog1.gif&ehk=Xnfrvg0xwy1u%2fae9vWdKYMT25%2bF6qoteW2FThCbVrOA%3d&risl=&pid=ImgRaw&r=0\",description:\"Pat your leg and snap your fingers\"},\"eat\":{name:\"Eat\",gif:\"https://lifeprint.com/asl101/gifs/e/eat.gif\",description:\"Bring your fingers to your mouth as if eating\"},\"elephant\":{name:\"Elephant\",gif:\"https://lifeprint.com/asl101/gifs/e/elephant.gif\",description:\"Trace the shape of an elephant's trunk with your hand\"},\"fish\":{name:\"Fish\",gif:\"https://lifeprint.com/asl101/gifs/f/fish.gif\",description:\"Move your hand like a fish swimming\"},\"flower\":{name:\"Flower\",gif:\"https://lifeprint.com/asl101/gifs/f/flower.gif\",description:\"Touch your nose with your fingertips\"},\"friend\":{name:\"Friend\",gif:\"https://lifeprint.com/asl101/gifs/f/friend.gif\",description:\"Hook your index fingers together\"},\"girl\":{name:\"Girl\",gif:\"https://lifeprint.com/asl101/gifs/g/girl.gif\",description:\"Trace your jawline with your thumb\"},\"go\":{name:\"Go\",gif:\"https://lifeprint.com/asl101/gifs/g/go.gif\",description:\"Point both index fingers forward and bend them\"},\"good\":{name:\"Good\",gif:\"https://lifeprint.com/asl101/gifs/g/good.gif\",description:\"Touch your chin and move your hand forward\"},\"green\":{name:\"Green\",gif:\"https://lifeprint.com/asl101/gifs/g/green.gif\",description:\"Shake a 'G' handshape\"},\"hair\":{name:\"Hair\",gif:\"https://www.lifeprint.com/asl101/gifs/h/hair-g-version.gif\",description:\"Pinch a strand of your hair\"},\"happy\":{name:\"Happy\",gif:\"https://media0.giphy.com/media/3o7TKFpahYpUp4g0N2/giphy.gif?cid=790b7611bca188a92e2e759876756ef62ba95b7cdd337c77&rid=giphy.gif&ct=g\",description:\"Brush your chest upward with both hands\"},\"hello\":{name:\"Hello\",gif:\"https://media.giphy.com/media/3o7TKNKOfKlIhbD3gY/giphy.gif\",description:\"Wave your hand from side to side with palm facing forward\"},\"home\":{name:\"Home\",gif:\"https://th.bing.com/th/id/R.********************************?rik=%2bnBd%2foQjxnoPfg&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fh%2fhome-2.gif&ehk=7yD%2f%2fh6JN1Y4D4BOrUjgKW4Jccy2Y4GVYLf%2fzyk%2b5YY%3d&risl=&pid=ImgRaw&r=0\",description:\"Touch your mouth then your cheek\"},\"horse\":{name:\"Horse\",gif:\"https://media.giphy.com/media/l0HlM5HffraiQaHUk/giphy.gif\",description:\"Extend your thumb and fingers at your temple like ears\"},\"hot\":{name:\"Hot\",gif:\"https://media.giphy.com/media/3o6Zt99k5aDok347bG/giphy.gif\",description:\"Touch your mouth and quickly move your hand away\"},\"hungry\":{name:\"Hungry\",gif:\"https://media.giphy.com/media/l3vR0xkdFEz4tnfTq/giphy.gif\",description:\"Move your hand down your chest like food going down\"},\"jump\":{name:\"Jump\",gif:\"https://lifeprint.com/asl101/gifs-animated/jump.gif\",description:\"Bounce your fingers on your palm\"},\"like\":{name:\"Like\",gif:\"https://lifeprint.com/asl101/gifs/l/like.gif\",description:\"Pull your thumb and middle finger from your chest\"},\"look\":{name:\"Look\",gif:\"https://th.bing.com/th/id/R.********************************?rik=pYhzip7LqNs7qw&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fl%2flook-at-1.gif&ehk=rFJ7dBrMGFDK0nHLzrOPAzROVE7yqyDEcb%2btLqKqYOI%3d&risl=&pid=ImgRaw&r=0\",description:\"Point your fingers from your eyes forward\"},\"love\":{name:\"Love\",gif:\"https://lifeprint.com/asl101/gifs/l/love.gif\",description:\"Cross your arms over your chest\"},\"mom\":{name:\"Mom\",gif:\"https://media.giphy.com/media/l0MYQcLKwtl5v6H1S/giphy.gif\",description:\"Tap your chin with your thumb\"},\"more\":{name:\"More\",gif:\"https://lifeprint.com/asl101/gifs/m/more.gif\",description:\"Tap your fingertips together\"},\"no\":{name:\"No\",gif:\"https://lifeprint.com/asl101/gifs/n/no.gif\",description:\"Snap your fingers together\"},\"orange\":{name:\"Orange\",gif:\"https://lifeprint.com/asl101/gifs/o/orange.gif\",description:\"Squeeze your hand at your mouth like squeezing an orange\"},\"please\":{name:\"Please\",gif:\"https://lifeprint.com/asl101/gifs/p/please.gif\",description:\"Rub your chest in a circular motion\"},\"red\":{name:\"Red\",gif:\"https://lifeprint.com/asl101/gifs/r/red.gif\",description:\"Brush your lips with your index finger\"},\"run\":{name:\"Run\",gif:\"https://lifeprint.com/asl101/gifs/r/run.gif\",description:\"Hook your thumbs and wiggle your fingers\"},\"sad\":{name:\"Sad\",gif:\"https://lifeprint.com/asl101/gifs/s/sad.gif\",description:\"Drop both hands down your face\"},\"see\":{name:\"See\",gif:\"https://lifeprint.com/asl101/gifs/l/look-at-2.gif\",description:\"Point from your eyes forward\"},\"sleep\":{name:\"Sleep\",gif:\"https://media4.giphy.com/media/3o7TKnRuBdakLslcaI/200.gif?cid=790b76110d8f185a9713f36dd65a0df801576e01b403c95c&rid=200.gif&ct=g\",description:\"Rest your head on your hands\"},\"sorry\":{name:\"Sorry\",gif:\"https://lifeprint.com/asl101/gifs/s/sorry.gif\",description:\"Rub your fist in a circle on your chest\"},\"thank\":{name:\"Thank You\",gif:\"https://lifeprint.com/asl101/gifs/t/thank-you.gif\",description:\"Touch your chin and move your hand forward\"},\"water\":{name:\"Water\",gif:\"https://lifeprint.com/asl101/gifs/w/water.gif\",description:\"Tap your chin with a 'W' handshape\"},\"white\":{name:\"White\",gif:\"https://lifeprint.com/asl101/gifs/w/white.gif\",description:\"Touch your chest and pull your hand away\"},\"yellow\":{name:\"Yellow\",gif:\"https://lifeprint.com/asl101/gifs/y/yellow.gif\",description:\"Shake a 'Y' handshape\"},\"yes\":{name:\"Yes\",gif:\"https://media.tenor.com/oYIirlyIih0AAAAC/yes-asl.gif\",description:\"Nod your fist up and down\"}};const TrainingPage=_ref=>{let{onBackToHome}=_ref;const[currentSign,setCurrentSign]=useState('hello');const[status,setStatus]=useState('');const[isCapturing,setIsCapturing]=useState(false);const[autoRecordMode,setAutoRecordMode]=useState(false);// eslint-disable-next-line no-unused-vars\nconst[recordedVideos,setRecordedVideos]=useState([]);const webcamRef=useRef(null);const autoRecordTimeoutRef=useRef(null);const matchCountRef=useRef(0);// Use sign detection hook\nconst{isConnected,prediction,isAIRecording,recordingStatus,signMatched,targetSign,startRecording:startAIRecording,stopRecording:stopAIRecording,startFrameCapture}=useSignDetection();const handleSignChange=useCallback(event=>{setCurrentSign(event.target.value);},[]);const startDetection=useCallback(()=>{if(!webcamRef.current){setStatus('Camera not available');return;}setIsCapturing(true);startFrameCapture(webcamRef,100);// Send frame every 100ms\nsetStatus('AI detection started');},[startFrameCapture]);const startRecording=useCallback(()=>{if(!isConnected){setStatus('AI backend not connected');return;}if(!webcamRef.current){setStatus('Camera not available');return;}// Toggle auto-record mode\nif(!autoRecordMode){setAutoRecordMode(true);setStatus(\"Get ready! Perform \\\"\".concat(signLanguageData[currentSign].name,\"\\\" sign when you're confident. Recording will start automatically when detected.\"));matchCountRef.current=0;}else{setAutoRecordMode(false);setStatus('Auto-recording disabled');matchCountRef.current=0;if(autoRecordTimeoutRef.current){clearTimeout(autoRecordTimeoutRef.current);}}// Also start frame capture if not already started\nif(!isCapturing){startDetection();}},[currentSign,isConnected,isCapturing,startDetection,autoRecordMode]);const stopRecording=useCallback(()=>{// Stop AI recording and auto-record mode\nif(isAIRecording){stopAIRecording();}setAutoRecordMode(false);matchCountRef.current=0;if(autoRecordTimeoutRef.current){clearTimeout(autoRecordTimeoutRef.current);}setStatus('Recording stopped');},[stopAIRecording,isAIRecording]);const downloadRecording=video=>{const a=document.createElement('a');a.href=video.url;a.download=\"sign_\".concat(video.sign,\"_\").concat(video.timestamp,\".webm\");a.click();};// Auto-start detection when connected\nuseEffect(()=>{if(isConnected&&webcamRef.current&&!isCapturing){startDetection();}},[isConnected,startDetection,isCapturing]);// Smart auto-recording logic\nuseEffect(()=>{if(!prediction||!autoRecordMode){matchCountRef.current=0;return;}const predictedSign=prediction.sign.toLowerCase();const targetSignLower=signLanguageData[currentSign].name.toLowerCase();const confidence=prediction.confidence;// Check if sign matches with high confidence\nif(predictedSign===targetSignLower&&confidence>0.8){matchCountRef.current+=1;// Start recording after 3 consecutive matches\nif(matchCountRef.current>=3&&!isAIRecording){setStatus(\"Perfect! Recording \".concat(signLanguageData[currentSign].name,\"...\"));startAIRecording(signLanguageData[currentSign].name);// Auto-stop recording after 3 seconds\nautoRecordTimeoutRef.current=setTimeout(()=>{stopAIRecording();setStatus(\"Recording complete! Great job with \".concat(signLanguageData[currentSign].name));setAutoRecordMode(false);matchCountRef.current=0;},3000);}}else{// Reset match count if sign doesn't match\nmatchCountRef.current=0;}return()=>{if(autoRecordTimeoutRef.current){clearTimeout(autoRecordTimeoutRef.current);}};},[prediction,autoRecordMode,currentSign,isAIRecording,startAIRecording,stopAIRecording]);return/*#__PURE__*/_jsxs(TrainingContainer,{children:[/*#__PURE__*/_jsx(Navigation,{children:/*#__PURE__*/_jsxs(NavContainer,{children:[/*#__PURE__*/_jsxs(Logo,{children:[/*#__PURE__*/_jsx(LogoIcon,{children:/*#__PURE__*/_jsx(Brain,{size:24})}),\"ASL Neural\"]}),/*#__PURE__*/_jsxs(BackButton,{onClick:onBackToHome,children:[/*#__PURE__*/_jsx(ArrowLeft,{size:18}),\"Back to Home\"]})]})}),/*#__PURE__*/_jsxs(MainContent,{children:[/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'center',marginBottom:'var(--space-12)'},children:/*#__PURE__*/_jsxs(StatusBadge,{children:[/*#__PURE__*/_jsx(Eye,{size:16}),\"Neural Vision Active\"]})}),/*#__PURE__*/_jsx(PageTitle,{children:\"AI Training Session\"}),/*#__PURE__*/_jsx(PageSubtitle,{children:\"Experience real-time neural network analysis as our AI learns from your sign language practice\"}),/*#__PURE__*/_jsxs(TrainingGrid,{children:[/*#__PURE__*/_jsxs(CameraSection,{children:[/*#__PURE__*/_jsxs(SectionTitle,{children:[/*#__PURE__*/_jsx(SectionIcon,{children:/*#__PURE__*/_jsx(Camera,{size:24})}),\"Neural Vision Feed\"]}),/*#__PURE__*/_jsxs(ConnectionStatus,{connected:isConnected,children:[isConnected?/*#__PURE__*/_jsx(Wifi,{size:16}):/*#__PURE__*/_jsx(WifiOff,{size:16}),isConnected?'AI Connected':'AI Disconnected']}),prediction&&/*#__PURE__*/_jsxs(PredictionDisplay,{matched:signMatched,isStale:prediction.isStale,children:[/*#__PURE__*/_jsxs(PredictionText,{matched:signMatched,isStale:prediction.isStale,children:[\"Detected: \",prediction.sign,prediction.isStale&&' (previous)']}),/*#__PURE__*/_jsx(ConfidenceBar,{children:/*#__PURE__*/_jsx(ConfidenceFill,{confidence:prediction.confidence})}),/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'0.875rem',marginTop:'8px',color:'var(--text-secondary)'},children:[\"Confidence: \",Math.round(prediction.confidence*100),\"%\",signMatched&&targetSign&&/*#__PURE__*/_jsx(\"span\",{style:{color:'var(--success-600)',marginLeft:'8px'},children:\"\\u2713 Match! Recording...\"}),autoRecordMode&&!isAIRecording&&/*#__PURE__*/_jsxs(\"div\",{style:{color:'var(--primary-600)',marginTop:'4px'},children:[\"\\uD83C\\uDFAF Auto-record mode: Perform \\\"\",signLanguageData[currentSign].name,\"\\\" sign\"]})]})]}),!prediction&&autoRecordMode&&/*#__PURE__*/_jsxs(PredictionDisplay,{children:[/*#__PURE__*/_jsxs(PredictionText,{children:[\"\\uD83C\\uDFAF Ready to detect \\\"\",signLanguageData[currentSign].name,\"\\\"\"]}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'0.875rem',color:'var(--text-secondary)'},children:\"Perform the sign when you're confident. Recording will start automatically.\"})]}),/*#__PURE__*/_jsxs(WebcamContainer,{children:[/*#__PURE__*/_jsx(StyledWebcam,{ref:webcamRef,audio:false,screenshotFormat:\"image/jpeg\",videoConstraints:{width:640,height:480,facingMode:\"user\"}}),/*#__PURE__*/_jsx(RecordingOverlay,{isRecording:isAIRecording,children:isAIRecording?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"div\",{style:{width:'8px',height:'8px',borderRadius:'50%',backgroundColor:'white',marginRight:'4px'}}),\"Recording\"]}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Eye,{size:16}),\"Ready\"]})})]})]}),/*#__PURE__*/_jsxs(SignSection,{children:[/*#__PURE__*/_jsxs(SectionTitle,{children:[/*#__PURE__*/_jsx(SectionIcon,{children:/*#__PURE__*/_jsx(Target,{size:24})}),\"Select a Sign\"]}),/*#__PURE__*/_jsx(SignSelector,{value:currentSign,onChange:handleSignChange,disabled:isAIRecording,children:Object.keys(signLanguageData).map(signKey=>/*#__PURE__*/_jsx(\"option\",{value:signKey,children:signLanguageData[signKey].name},signKey))}),/*#__PURE__*/_jsxs(SignDisplay,{children:[/*#__PURE__*/_jsx(\"img\",{src:signLanguageData[currentSign].gif,alt:signLanguageData[currentSign].name,onError:e=>{e.target.style.display='none';e.target.nextSibling.style.display='flex';}}),/*#__PURE__*/_jsx(\"div\",{style:{display:'none',fontSize:'3rem'},children:\"\\uD83D\\uDCF7\"})]}),/*#__PURE__*/_jsx(SignName,{children:signLanguageData[currentSign].name}),/*#__PURE__*/_jsx(SignDescription,{children:signLanguageData[currentSign].description})]})]}),/*#__PURE__*/_jsx(ControlsSection,{children:/*#__PURE__*/_jsx(ControlButton,{variant:\"primary\",onClick:isAIRecording||autoRecordMode?stopRecording:startRecording,children:isAIRecording?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Square,{size:18}),\"Stop Recording\"]}):autoRecordMode?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Square,{size:18}),\"Cancel Auto-Record\"]}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Play,{size:18}),\"Start Smart Recording\"]})})}),(status||recordingStatus)&&/*#__PURE__*/_jsx(StatusMessage,{type:(status||recordingStatus).includes('error')?'error':(status||recordingStatus).includes('success')?'success':'info',children:recordingStatus||status}),recordedVideos.length>0&&/*#__PURE__*/_jsxs(RecordingsSection,{children:[/*#__PURE__*/_jsx(RecordingsTitle,{children:\"Your Practice Recordings\"}),/*#__PURE__*/_jsx(RecordingsGrid,{children:recordedVideos.map(video=>/*#__PURE__*/_jsxs(RecordingCard,{children:[/*#__PURE__*/_jsx(RecordingTitle,{children:video.sign}),/*#__PURE__*/_jsx(RecordingTime,{children:new Date(video.timestamp).toLocaleString()}),/*#__PURE__*/_jsxs(DownloadButton,{onClick:()=>downloadRecording(video),children:[/*#__PURE__*/_jsx(Download,{size:16}),\"Download\"]})]},video.id))})]})]})]});};export default TrainingPage;", "map": {"version": 3, "names": ["useState", "useRef", "useCallback", "useEffect", "styled", "Webcam", "Brain", "Camera", "ArrowLeft", "Play", "Square", "Download", "Eye", "Target", "Wifi", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useSignDetection", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "TrainingContainer", "div", "_templateObject", "_taggedTemplateLiteral", "Navigation", "nav", "_templateObject2", "NavContainer", "_templateObject3", "Logo", "_templateObject4", "LogoIcon", "_templateObject5", "BackButton", "button", "_templateObject6", "Page<PERSON><PERSON>le", "h1", "_templateObject7", "PageSubtitle", "p", "_templateObject8", "StatusBadge", "_templateObject9", "MainContent", "main", "_templateObject0", "TrainingGrid", "_templateObject1", "CameraSection", "_templateObject10", "SectionTitle", "h2", "_templateObject11", "SectionIcon", "_templateObject12", "WebcamContainer", "_templateObject13", "StyledWebcam", "_templateObject14", "RecordingOverlay", "_templateObject15", "props", "isRecording", "SignSection", "_templateObject16", "SignSelector", "select", "_templateObject17", "SignDisplay", "_templateObject18", "SignName", "h3", "_templateObject19", "SignDescription", "_templateObject20", "ControlsSection", "_templateObject21", "ControlButton", "_templateObject22", "variant", "StatusMessage", "_templateObject23", "type", "RecordingsSection", "_templateObject24", "RecordingsTitle", "_templateObject25", "RecordingsGrid", "_templateObject26", "RecordingCard", "_templateObject27", "RecordingTitle", "_templateObject28", "RecordingTime", "_templateObject29", "DownloadButton", "_templateObject30", "PredictionDisplay", "_templateObject31", "matched", "isStale", "PredictionText", "_templateObject32", "ConfidenceBar", "_templateObject33", "ConfidenceFill", "_templateObject34", "confidence", "ConnectionStatus", "_templateObject35", "connected", "signLanguageData", "name", "gif", "description", "TrainingPage", "_ref", "onBackToHome", "currentSign", "setCurrentSign", "status", "setStatus", "isCapturing", "setIsCapturing", "autoRecordMode", "setAutoRecordMode", "recordedVideos", "setRecordedVideos", "webcamRef", "autoRecordTimeoutRef", "matchCountRef", "isConnected", "prediction", "isAIRecording", "recordingStatus", "signMatched", "targetSign", "startRecording", "startAIRecording", "stopRecording", "stopAIRecording", "startFrameCapture", "handleSignChange", "event", "target", "value", "startDetection", "current", "concat", "clearTimeout", "downloadRecording", "video", "a", "document", "createElement", "href", "url", "download", "sign", "timestamp", "click", "predictedSign", "toLowerCase", "targetSignLower", "setTimeout", "children", "size", "onClick", "style", "textAlign", "marginBottom", "fontSize", "marginTop", "color", "Math", "round", "marginLeft", "ref", "audio", "screenshotFormat", "videoConstraints", "width", "height", "facingMode", "borderRadius", "backgroundColor", "marginRight", "onChange", "disabled", "Object", "keys", "map", "sign<PERSON><PERSON>", "src", "alt", "onError", "e", "display", "nextS<PERSON>ling", "includes", "length", "Date", "toLocaleString", "id"], "sources": ["D:/ASL/training-frontend/src/components/TrainingPage.js"], "sourcesContent": ["import { useState, useRef, useCallback, useEffect } from 'react';\r\nimport styled from 'styled-components';\r\nimport Webcam from 'react-webcam';\r\nimport {\r\n  Brain,\r\n  Camera,\r\n  ArrowLeft,\r\n  Play,\r\n  Square,\r\n  Download,\r\n  Eye,\r\n  Target,\r\n  Wifi,\r\n  WifiOff\r\n} from 'lucide-react';\r\nimport { useSignDetection } from '../hooks/useSignDetection';\r\n\r\nconst TrainingContainer = styled.div`\r\n  min-height: 100vh;\r\n  background: var(--bg-primary);\r\n  position: relative;\r\n  overflow-x: hidden;\r\n\r\n  &::before {\r\n    content: '';\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background:\r\n      radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),\r\n      radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.03) 0%, transparent 50%);\r\n    pointer-events: none;\r\n    z-index: 0;\r\n  }\r\n`;\r\n\r\nconst Navigation = styled.nav`\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  z-index: 50;\r\n  background: var(--bg-glass);\r\n  backdrop-filter: blur(20px);\r\n  border-bottom: 1px solid var(--border-neural);\r\n  padding: var(--space-4) 0;\r\n  transition: var(--transition-normal);\r\n`;\r\n\r\nconst NavContainer = styled.div`\r\n  max-width: 1400px;\r\n  margin: 0 auto;\r\n  padding: 0 var(--space-6);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n\r\n  @media (max-width: 768px) {\r\n    padding: 0 var(--space-4);\r\n  }\r\n`;\r\n\r\nconst Logo = styled.div`\r\n  font-family: var(--font-primary);\r\n  font-size: 1.5rem;\r\n  font-weight: 700;\r\n  background: var(--text-gradient);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--space-3);\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 1.25rem;\r\n    gap: var(--space-2);\r\n  }\r\n`;\r\n\r\nconst LogoIcon = styled.div`\r\n  width: 40px;\r\n  height: 40px;\r\n  background: var(--bg-neural);\r\n  border-radius: var(--radius-lg);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  box-shadow: var(--shadow-neural);\r\n\r\n  @media (max-width: 768px) {\r\n    width: 36px;\r\n    height: 36px;\r\n  }\r\n`;\r\n\r\nconst BackButton = styled.button`\r\n  background: var(--bg-glass);\r\n  color: var(--text-secondary);\r\n  border: 1px solid var(--border-neural);\r\n  padding: var(--space-3) var(--space-5);\r\n  border-radius: var(--radius-xl);\r\n  font-size: 0.9rem;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  transition: var(--transition-normal);\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--space-2);\r\n  backdrop-filter: blur(10px);\r\n\r\n  &:hover {\r\n    background: var(--primary-50);\r\n    color: var(--primary-600);\r\n    border-color: var(--primary-300);\r\n    transform: translateY(-1px);\r\n    box-shadow: var(--shadow-lg);\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    padding: var(--space-2) var(--space-4);\r\n    font-size: 0.85rem;\r\n  }\r\n`;\r\n\r\nconst PageTitle = styled.h1`\r\n  font-family: var(--font-primary);\r\n  font-size: 2.5rem;\r\n  font-weight: 700;\r\n  background: var(--text-gradient);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n  text-align: center;\r\n  margin-bottom: var(--space-4);\r\n  letter-spacing: -0.02em;\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 2rem;\r\n  }\r\n`;\r\n\r\nconst PageSubtitle = styled.p`\r\n  font-size: 1.125rem;\r\n  color: var(--text-secondary);\r\n  text-align: center;\r\n  margin-bottom: var(--space-16);\r\n  max-width: 700px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n  line-height: 1.6;\r\n\r\n  @media (max-width: 768px) {\r\n    margin-bottom: var(--space-12);\r\n    font-size: 1rem;\r\n  }\r\n`;\r\n\r\nconst StatusBadge = styled.div`\r\n  display: inline-flex;\r\n  align-items: center;\r\n  gap: var(--space-2);\r\n  background: var(--bg-glass);\r\n  border: 1px solid var(--border-neural);\r\n  border-radius: var(--radius-full);\r\n  padding: var(--space-2) var(--space-4);\r\n  margin-bottom: var(--space-8);\r\n  font-size: 0.875rem;\r\n  font-weight: 500;\r\n  color: var(--text-accent);\r\n  backdrop-filter: blur(10px);\r\n  box-shadow: var(--shadow-glow);\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 0.8rem;\r\n    padding: var(--space-2) var(--space-3);\r\n  }\r\n`;\r\n\r\nconst MainContent = styled.main`\r\n  padding: var(--space-20) var(--space-4) var(--space-16);\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n\r\n  @media (max-width: 768px) {\r\n    padding: var(--space-12) var(--space-3) var(--space-8);\r\n    max-width: 100%;\r\n  }\r\n`;\r\n\r\nconst TrainingGrid = styled.div`\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: var(--space-8);\r\n  max-width: 1200px;\r\n  margin: 0 auto var(--space-12);\r\n\r\n  @media (max-width: 1024px) {\r\n    grid-template-columns: 1fr;\r\n    gap: var(--space-4);\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    gap: var(--space-3);\r\n    margin: 0 auto var(--space-6);\r\n  }\r\n`;\r\n\r\nconst CameraSection = styled.div`\r\n  background: var(--bg-glass);\r\n  border-radius: var(--radius-2xl);\r\n  padding: var(--space-10);\r\n  border: 1px solid var(--border-neural);\r\n  backdrop-filter: blur(20px);\r\n  box-shadow: var(--shadow-lg);\r\n  transition: var(--transition-normal);\r\n  position: relative;\r\n  overflow: hidden;\r\n\r\n  @media (max-width: 768px) {\r\n    padding: var(--space-6);\r\n    border-radius: var(--radius-xl);\r\n  }\r\n\r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    height: 4px;\r\n    background: var(--bg-neural);\r\n    transform: scaleX(0);\r\n    transition: var(--transition-normal);\r\n  }\r\n\r\n  &:hover {\r\n    box-shadow: var(--shadow-xl), var(--shadow-glow);\r\n    border-color: var(--primary-300);\r\n\r\n    &::before {\r\n      transform: scaleX(1);\r\n    }\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    padding: var(--space-8);\r\n  }\r\n`;\r\n\r\nconst SectionTitle = styled.h2`\r\n  font-family: var(--font-primary);\r\n  font-size: 1.25rem;\r\n  margin-bottom: var(--space-6);\r\n  color: var(--text-primary);\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--space-3);\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 1.125rem;\r\n    margin-bottom: var(--space-4);\r\n  }\r\n`;\r\n\r\nconst SectionIcon = styled.div`\r\n  width: 36px;\r\n  height: 36px;\r\n  background: var(--bg-neural);\r\n  border-radius: var(--radius-lg);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  box-shadow: var(--shadow-neural);\r\n\r\n  @media (max-width: 768px) {\r\n    width: 32px;\r\n    height: 32px;\r\n  }\r\n`;\r\n\r\nconst WebcamContainer = styled.div`\r\n  position: relative;\r\n  border-radius: var(--radius-2xl);\r\n  overflow: hidden;\r\n  background: var(--neural-100);\r\n  aspect-ratio: 4/3;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border: 3px solid var(--border-neural);\r\n  margin-bottom: var(--space-6);\r\n  box-shadow: var(--shadow-lg);\r\n\r\n  @media (max-width: 768px) {\r\n    aspect-ratio: 3/4;\r\n    margin-bottom: var(--space-4);\r\n    border-radius: var(--radius-xl);\r\n    border-width: 2px;\r\n  }\r\n`;\r\n\r\nconst StyledWebcam = styled(Webcam)`\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n`;\r\n\r\nconst RecordingOverlay = styled.div`\r\n  position: absolute;\r\n  top: var(--space-4);\r\n  right: var(--space-4);\r\n  background: ${props => props.isRecording ?\r\n    'var(--error-500)' :\r\n    'var(--neural-600)'\r\n  };\r\n  color: white;\r\n  padding: var(--space-3) var(--space-5);\r\n  border-radius: var(--radius-full);\r\n  font-size: 0.9rem;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--space-2);\r\n  box-shadow: var(--shadow-lg);\r\n  animation: ${props => props.isRecording ? 'pulse 1.5s infinite' : 'none'};\r\n\r\n  @keyframes pulse {\r\n    0%, 100% { opacity: 1; transform: scale(1); }\r\n    50% { opacity: 0.8; transform: scale(1.05); }\r\n  }\r\n`;\r\n\r\nconst SignSection = styled.div`\r\n  background: var(--bg-primary);\r\n  border-radius: var(--radius-2xl);\r\n  padding: var(--space-8);\r\n  border: 1px solid var(--border-light);\r\n  box-shadow: var(--shadow-lg);\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  text-align: center;\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    box-shadow: var(--shadow-xl);\r\n    border-color: var(--primary-200);\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    padding: var(--space-6);\r\n  }\r\n`;\r\n\r\nconst SignSelector = styled.select`\r\n  width: 100%;\r\n  padding: var(--space-3) var(--space-4);\r\n  border: 2px solid var(--border-light);\r\n  border-radius: var(--radius-lg);\r\n  background: var(--bg-primary);\r\n  color: var(--text-primary);\r\n  font-size: 1rem;\r\n  font-weight: 500;\r\n  margin-bottom: var(--space-4);\r\n  cursor: pointer;\r\n  transition: var(--transition-normal);\r\n\r\n  &:focus {\r\n    outline: none;\r\n    border-color: var(--primary-500);\r\n    box-shadow: 0 0 0 3px var(--primary-100);\r\n  }\r\n\r\n  &:hover {\r\n    border-color: var(--primary-300);\r\n  }\r\n\r\n  option {\r\n    padding: var(--space-2);\r\n    background: var(--bg-primary);\r\n    color: var(--text-primary);\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 1.125rem;\r\n    padding: var(--space-4);\r\n    margin-bottom: var(--space-3);\r\n  }\r\n`;\r\n\r\nconst SignDisplay = styled.div`\r\n  width: 300px;\r\n  height: 300px;\r\n  background: var(--primary-50);\r\n  border-radius: var(--radius-2xl);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-bottom: var(--space-6);\r\n  border: 2px solid var(--primary-200);\r\n  transition: all 0.3s ease;\r\n  overflow: hidden;\r\n\r\n  img {\r\n    width: 100%;\r\n    height: 100%;\r\n    object-fit: cover;\r\n    border-radius: var(--radius-xl);\r\n  }\r\n\r\n  &:hover {\r\n    transform: scale(1.02);\r\n    border-color: var(--primary-300);\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    width: 250px;\r\n    height: 250px;\r\n  }\r\n`;\r\n\r\nconst SignName = styled.h3`\r\n  font-family: var(--font-primary);\r\n  font-size: 1.5rem;\r\n  margin-bottom: var(--space-3);\r\n  color: var(--text-primary);\r\n  font-weight: 700;\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 1.25rem;\r\n  }\r\n`;\r\n\r\nconst SignDescription = styled.p`\r\n  text-align: center;\r\n  line-height: 1.6;\r\n  color: var(--text-secondary);\r\n  font-size: 0.9rem;\r\n  font-weight: 400;\r\n  max-width: 280px;\r\n`;\r\n\r\nconst ControlsSection = styled.div`\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: var(--space-4);\r\n  margin-top: var(--space-8);\r\n\r\n  @media (max-width: 768px) {\r\n    flex-direction: column;\r\n    align-items: center;\r\n    gap: var(--space-3);\r\n  }\r\n`;\r\n\r\nconst ControlButton = styled.button`\r\n  background: ${props => props.variant === 'primary'\r\n    ? 'var(--primary-600)'\r\n    : 'var(--bg-primary)'};\r\n  border: ${props => props.variant === 'primary'\r\n    ? 'none'\r\n    : '1px solid var(--border-medium)'};\r\n  color: ${props => props.variant === 'primary'\r\n    ? 'white'\r\n    : 'var(--text-primary)'};\r\n  padding: var(--space-3) var(--space-6);\r\n  border-radius: var(--radius-lg);\r\n  cursor: pointer;\r\n  font-size: 0.9rem;\r\n  font-weight: 600;\r\n  transition: all 0.2s ease;\r\n  min-width: 160px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: var(--space-2);\r\n\r\n  @media (max-width: 768px) {\r\n    padding: var(--space-4) var(--space-8);\r\n    font-size: 1rem;\r\n    min-width: 180px;\r\n    border-radius: var(--radius-xl);\r\n  }\r\n  box-shadow: ${props => props.variant === 'primary'\r\n    ? 'var(--shadow-lg)'\r\n    : 'var(--shadow-sm)'};\r\n\r\n  &:hover {\r\n    transform: translateY(-2px);\r\n    box-shadow: ${props => props.variant === 'primary'\r\n      ? 'var(--shadow-xl)'\r\n      : 'var(--shadow-md)'};\r\n    background: ${props => props.variant === 'primary'\r\n      ? 'var(--primary-700)'\r\n      : 'var(--gray-50)'};\r\n  }\r\n\r\n  &:disabled {\r\n    opacity: 0.5;\r\n    cursor: not-allowed;\r\n    transform: none;\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    width: 100%;\r\n    max-width: 280px;\r\n  }\r\n`;\r\n\r\nconst StatusMessage = styled.div`\r\n  text-align: center;\r\n  margin-top: var(--space-6);\r\n  padding: var(--space-4) var(--space-6);\r\n  border-radius: var(--radius-lg);\r\n  background: ${props =>\r\n    props.type === 'success' ? 'var(--success-500)' :\r\n    props.type === 'error' ? 'var(--error-500)' :\r\n    'var(--primary-600)'\r\n  };\r\n  color: white;\r\n  font-weight: 500;\r\n  font-size: 0.875rem;\r\n  max-width: 400px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n`;\r\n\r\nconst RecordingsSection = styled.div`\r\n  margin-top: var(--space-16);\r\n  background: var(--bg-secondary);\r\n  padding: var(--space-12) var(--space-4);\r\n  border-radius: var(--radius-2xl);\r\n  max-width: 1200px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n`;\r\n\r\nconst RecordingsTitle = styled.h3`\r\n  font-family: var(--font-primary);\r\n  color: var(--text-primary);\r\n  margin-bottom: var(--space-8);\r\n  font-size: 1.5rem;\r\n  font-weight: 600;\r\n  text-align: center;\r\n`;\r\n\r\nconst RecordingsGrid = styled.div`\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\r\n  gap: var(--space-6);\r\n\r\n  @media (max-width: 768px) {\r\n    grid-template-columns: 1fr;\r\n    gap: var(--space-4);\r\n  }\r\n`;\r\n\r\nconst RecordingCard = styled.div`\r\n  background: var(--bg-primary);\r\n  padding: var(--space-6);\r\n  border-radius: var(--radius-xl);\r\n  border: 1px solid var(--border-light);\r\n  box-shadow: var(--shadow-sm);\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    transform: translateY(-2px);\r\n    border-color: var(--primary-200);\r\n    box-shadow: var(--shadow-lg);\r\n  }\r\n`;\r\n\r\nconst RecordingTitle = styled.p`\r\n  margin: 0 0 var(--space-2) 0;\r\n  color: var(--text-primary);\r\n  font-weight: 600;\r\n  font-size: 1rem;\r\n  font-family: var(--font-primary);\r\n`;\r\n\r\nconst RecordingTime = styled.p`\r\n  margin: 0 0 var(--space-4) 0;\r\n  font-size: 0.8rem;\r\n  color: var(--text-tertiary);\r\n`;\r\n\r\nconst DownloadButton = styled.button`\r\n  background: var(--primary-600);\r\n  border: none;\r\n  border-radius: var(--radius-lg);\r\n  padding: var(--space-2) var(--space-4);\r\n  color: white;\r\n  cursor: pointer;\r\n  font-size: 0.8rem;\r\n  font-weight: 500;\r\n  transition: all 0.2s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--space-2);\r\n  margin: 0 auto;\r\n\r\n  &:hover {\r\n    background: var(--primary-700);\r\n    transform: translateY(-1px);\r\n  }\r\n`;\r\n\r\nconst PredictionDisplay = styled.div`\r\n  background: var(--bg-glass);\r\n  border: 2px solid ${props => {\r\n    if (props.matched) return 'var(--success-400)';\r\n    if (props.isStale) return 'var(--warning-300)';\r\n    return 'var(--border-light)';\r\n  }};\r\n  border-radius: var(--radius-xl);\r\n  padding: var(--space-4);\r\n  margin-bottom: var(--space-4);\r\n  text-align: center;\r\n  transition: var(--transition-normal);\r\n  backdrop-filter: blur(10px);\r\n  opacity: ${props => props.isStale ? 0.7 : 1};\r\n  min-height: 80px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n\r\n  ${props => props.matched && `\r\n    background: var(--success-50);\r\n    box-shadow: 0 0 20px var(--success-200);\r\n    animation: pulse 1s ease-in-out;\r\n  `}\r\n\r\n  ${props => props.isStale && `\r\n    background: var(--warning-50);\r\n  `}\r\n\r\n  @keyframes pulse {\r\n    0%, 100% { transform: scale(1); }\r\n    50% { transform: scale(1.02); }\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    padding: var(--space-3);\r\n    margin-bottom: var(--space-3);\r\n    min-height: 70px;\r\n  }\r\n`;\r\n\r\nconst PredictionText = styled.div`\r\n  font-size: 1.25rem;\r\n  font-weight: 600;\r\n  color: ${props => {\r\n    if (props.matched) return 'var(--success-700)';\r\n    if (props.isStale) return 'var(--warning-700)';\r\n    return 'var(--text-primary)';\r\n  }};\r\n  margin-bottom: var(--space-2);\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 1.125rem;\r\n  }\r\n`;\r\n\r\nconst ConfidenceBar = styled.div`\r\n  width: 100%;\r\n  height: 8px;\r\n  background: var(--bg-secondary);\r\n  border-radius: var(--radius-full);\r\n  overflow: hidden;\r\n  margin-top: var(--space-2);\r\n`;\r\n\r\nconst ConfidenceFill = styled.div`\r\n  height: 100%;\r\n  background: ${props => {\r\n    if (props.confidence > 0.8) return 'var(--success-500)';\r\n    if (props.confidence > 0.6) return 'var(--warning-500)';\r\n    return 'var(--error-500)';\r\n  }};\r\n  width: ${props => (props.confidence * 100)}%;\r\n  transition: width 0.3s ease;\r\n`;\r\n\r\nconst ConnectionStatus = styled.div`\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--space-2);\r\n  padding: var(--space-2) var(--space-3);\r\n  border-radius: var(--radius-lg);\r\n  font-size: 0.875rem;\r\n  font-weight: 500;\r\n  background: ${props => props.connected ? 'var(--success-50)' : 'var(--error-50)'};\r\n  color: ${props => props.connected ? 'var(--success-700)' : 'var(--error-700)'};\r\n  border: 1px solid ${props => props.connected ? 'var(--success-200)' : 'var(--error-200)'};\r\n`;\r\n\r\n// Sign language data with GIFs (matching Streamlit app)\r\nconst signLanguageData = {\r\n  \"after\": {\r\n    name: \"After\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/a/after-over-across.gif\",\r\n    description: \"Move your dominant hand over and past your non-dominant hand\"\r\n  },\r\n  \"airplane\": {\r\n    name: \"Airplane\",\r\n    gif: \"https://www.lifeprint.com/asl101/gifs/a/airplane-flying.gif\",\r\n    description: \"Extend your hand like a plane and move it through the air\"\r\n  },\r\n  \"all\": {\r\n    name: \"All\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/a/all-whole.gif\",\r\n    description: \"Circle your dominant hand around your non-dominant hand\"\r\n  },\r\n  \"alligator\": {\r\n    name: \"Alligator\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/a/alligator.gif\",\r\n    description: \"Clap your hands together like an alligator's mouth\"\r\n  },\r\n  \"animal\": {\r\n    name: \"Animal\",\r\n    gif: \"https://www.lifeprint.com/asl101/gifs-animated/animal.gif\",\r\n    description: \"Place fingertips on chest and move hands back and forth\"\r\n  },\r\n  \"any\": {\r\n    name: \"Any\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/a/any.gif\",\r\n    description: \"Point with index finger and twist your wrist\"\r\n  },\r\n  \"apple\": {\r\n    name: \"Apple\",\r\n    gif: \"https://media.giphy.com/media/l0HlHb4dtZZiMYEA8/giphy.gif\",\r\n    description: \"Twist your knuckle against your cheek\"\r\n  },\r\n  \"arm\": {\r\n    name: \"Arm\",\r\n    gif: \"https://th.bing.com/th/id/OIP.KkJLqfbp6XEHiIe-jOUb2AHaEc?w=251&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\r\n    description: \"Pat your arm with your opposite hand\"\r\n  },\r\n  \"aunt\": {\r\n    name: \"Aunt\",\r\n    gif: \"https://th.bing.com/th/id/OIP.Yz5UUZdNTrVWXf72we_N6wHaHa?rs=1&pid=ImgDetMain\",\r\n    description: \"Make an 'A' handshape near your cheek and shake it\"\r\n  },\r\n  \"baby\": {\r\n    name: \"Baby\",\r\n    gif: \"https://media.giphy.com/media/l0HlHb4dtZZiMYEA8/giphy.gif\",\r\n    description: \"Rock your arms as if holding a baby\"\r\n  },\r\n  \"ball\": {\r\n    name: \"Ball\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/b/ball.gif\",\r\n    description: \"Cup your hands as if holding a ball\"\r\n  },\r\n  \"banana\": {\r\n    name: \"Banana\",\r\n    gif: \"https://media.giphy.com/media/l0HlHb4dtZZiMYEA8/giphy.gif\",\r\n    description: \"Peel an imaginary banana with your fingers\"\r\n  },\r\n  \"bear\": {\r\n    name: \"Bear\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/b/bear.gif\",\r\n    description: \"Cross your arms and scratch like a bear\"\r\n  },\r\n  \"beautiful\": {\r\n    name: \"Beautiful\",\r\n    gif: \"https://media.giphy.com/media/l0HlHb4dtZZiMYEA8/giphy.gif\",\r\n    description: \"Circle your face with your hand and close it into a fist\"\r\n  },\r\n  \"bed\": {\r\n    name: \"Bed\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/b/bed.gif\",\r\n    description: \"Rest your head on your hands as if sleeping\"\r\n  },\r\n  \"bee\": {\r\n    name: \"Bee\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/b/bee.gif\",\r\n    description: \"Pinch your cheek and brush away as if swatting a bee\"\r\n  },\r\n  \"bird\": {\r\n    name: \"Bird\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/b/bird.gif\",\r\n    description: \"Pinch your fingers together near your mouth like a beak\"\r\n  },\r\n  \"black\": {\r\n    name: \"Black\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/b/black.gif\",\r\n    description: \"Draw your index finger across your forehead\"\r\n  },\r\n  \"blue\": {\r\n    name: \"Blue\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/b/blue.gif\",\r\n    description: \"Shake a 'B' handshape\"\r\n  },\r\n  \"book\": {\r\n    name: \"Book\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/b/book.gif\",\r\n    description: \"Open your hands like opening a book\"\r\n  },\r\n  \"boy\": {\r\n    name: \"Boy\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/b/boy.gif\",\r\n    description: \"Snap your fingers at your forehead\"\r\n  },\r\n  \"brother\": {\r\n    name: \"Brother\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/b/brother.gif\",\r\n    description: \"Make an 'L' shape and point to your forehead, then point forward\"\r\n  },\r\n  \"brown\": {\r\n    name: \"Brown\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/b/brown.gif\",\r\n    description: \"Slide your index finger down your cheek\"\r\n  },\r\n  \"bug\": {\r\n    name: \"Bug\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/b/bug.gif\",\r\n    description: \"Pinch your nose with your thumb and index finger\"\r\n  },\r\n  \"butterfly\": {\r\n    name: \"Butterfly\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/b/butterfly.gif\",\r\n    description: \"Cross your thumbs and flutter your fingers like wings\"\r\n  },\r\n  \"car\": {\r\n    name: \"Car\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/c/car.gif\",\r\n    description: \"Pretend to steer a car with both hands\"\r\n  },\r\n  \"cat\": {\r\n    name: \"Cat\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/c/cat.gif\",\r\n    description: \"Pinch your cheek and pull out like whiskers\"\r\n  },\r\n  \"chair\": {\r\n    name: \"Chair\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/c/chair.gif\",\r\n    description: \"Tap your fingers on your other hand like sitting\"\r\n  },\r\n  \"clean\": {\r\n    name: \"Clean\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/c/clean.gif\",\r\n    description: \"Wipe one palm with the other\"\r\n  },\r\n  \"cold\": {\r\n    name: \"Cold\",\r\n    gif: \"https://media.giphy.com/media/l0HlHb4dtZZiMYEA8/giphy.gif\",\r\n    description: \"Shiver with both hands in fists\"\r\n  },\r\n  \"cow\": {\r\n    name: \"Cow\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/c/cow.gif\",\r\n    description: \"Twist your thumb at your temple like a horn\"\r\n  },\r\n  \"cry\": {\r\n    name: \"Cry\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/c/cry.gif\",\r\n    description: \"Draw tears down your cheeks with your index fingers\"\r\n  },\r\n  \"cute\": {\r\n    name: \"Cute\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/c/cute-sugar.gif\",\r\n    description: \"Brush your chin with your fingers\"\r\n  },\r\n  \"dad\": {\r\n    name: \"Dad\",\r\n    gif: \"https://media.giphy.com/media/l0MYQcLKwtl5v6H1S/giphy.gif\",\r\n    description: \"Tap your forehead with your thumb\"\r\n  },\r\n  \"dance\": {\r\n    name: \"Dance\",\r\n    gif: \"https://www.bing.com/th/id/OGC.********************************?pid=1.7&rurl=https%3a%2f%2fmedia.giphy.com%2fmedia%2f3o7TKMspYQjQTbOz2U%2fgiphy.gif&ehk=h%2bdBHCxuoOT89ovSy5uTk6MCL9acaBEV6ld9lrVDRF4%3d\",\r\n    description: \"Swing your fingers over your palm like dancing\"\r\n  },\r\n  \"dirty\": {\r\n    name: \"Dirty\",\r\n    gif: \"https://th.bing.com/th/id/OIP.wRA7r1OPPUuEoLL4Hds9jAHaHa?rs=1&pid=ImgDetMain\",\r\n    description: \"Wiggle your fingers under your chin\"\r\n  },\r\n  \"dog\": {\r\n    name: \"Dog\",\r\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=uVshGsOHXgldoQ&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fd%2fdog1.gif&ehk=Xnfrvg0xwy1u%2fae9vWdKYMT25%2bF6qoteW2FThCbVrOA%3d&risl=&pid=ImgRaw&r=0\",\r\n    description: \"Pat your leg and snap your fingers\"\r\n  },\r\n  \"eat\": {\r\n    name: \"Eat\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/e/eat.gif\",\r\n    description: \"Bring your fingers to your mouth as if eating\"\r\n  },\r\n  \"elephant\": {\r\n    name: \"Elephant\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/e/elephant.gif\",\r\n    description: \"Trace the shape of an elephant's trunk with your hand\"\r\n  },\r\n  \"fish\": {\r\n    name: \"Fish\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/f/fish.gif\",\r\n    description: \"Move your hand like a fish swimming\"\r\n  },\r\n  \"flower\": {\r\n    name: \"Flower\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/f/flower.gif\",\r\n    description: \"Touch your nose with your fingertips\"\r\n  },\r\n  \"friend\": {\r\n    name: \"Friend\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/f/friend.gif\",\r\n    description: \"Hook your index fingers together\"\r\n  },\r\n  \"girl\": {\r\n    name: \"Girl\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/g/girl.gif\",\r\n    description: \"Trace your jawline with your thumb\"\r\n  },\r\n  \"go\": {\r\n    name: \"Go\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/g/go.gif\",\r\n    description: \"Point both index fingers forward and bend them\"\r\n  },\r\n  \"good\": {\r\n    name: \"Good\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/g/good.gif\",\r\n    description: \"Touch your chin and move your hand forward\"\r\n  },\r\n  \"green\": {\r\n    name: \"Green\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/g/green.gif\",\r\n    description: \"Shake a 'G' handshape\"\r\n  },\r\n  \"hair\": {\r\n    name: \"Hair\",\r\n    gif: \"https://www.lifeprint.com/asl101/gifs/h/hair-g-version.gif\",\r\n    description: \"Pinch a strand of your hair\"\r\n  },\r\n  \"happy\": {\r\n    name: \"Happy\",\r\n    gif: \"https://media0.giphy.com/media/3o7TKFpahYpUp4g0N2/giphy.gif?cid=790b7611bca188a92e2e759876756ef62ba95b7cdd337c77&rid=giphy.gif&ct=g\",\r\n    description: \"Brush your chest upward with both hands\"\r\n  },\r\n  \"hello\": {\r\n    name: \"Hello\",\r\n    gif: \"https://media.giphy.com/media/3o7TKNKOfKlIhbD3gY/giphy.gif\",\r\n    description: \"Wave your hand from side to side with palm facing forward\"\r\n  },\r\n  \"home\": {\r\n    name: \"Home\",\r\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=%2bnBd%2foQjxnoPfg&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fh%2fhome-2.gif&ehk=7yD%2f%2fh6JN1Y4D4BOrUjgKW4Jccy2Y4GVYLf%2fzyk%2b5YY%3d&risl=&pid=ImgRaw&r=0\",\r\n    description: \"Touch your mouth then your cheek\"\r\n  },\r\n  \"horse\": {\r\n    name: \"Horse\",\r\n    gif: \"https://media.giphy.com/media/l0HlM5HffraiQaHUk/giphy.gif\",\r\n    description: \"Extend your thumb and fingers at your temple like ears\"\r\n  },\r\n  \"hot\": {\r\n    name: \"Hot\",\r\n    gif: \"https://media.giphy.com/media/3o6Zt99k5aDok347bG/giphy.gif\",\r\n    description: \"Touch your mouth and quickly move your hand away\"\r\n  },\r\n  \"hungry\": {\r\n    name: \"Hungry\",\r\n    gif: \"https://media.giphy.com/media/l3vR0xkdFEz4tnfTq/giphy.gif\",\r\n    description: \"Move your hand down your chest like food going down\"\r\n  },\r\n  \"jump\": {\r\n    name: \"Jump\",\r\n    gif: \"https://lifeprint.com/asl101/gifs-animated/jump.gif\",\r\n    description: \"Bounce your fingers on your palm\"\r\n  },\r\n  \"like\": {\r\n    name: \"Like\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/l/like.gif\",\r\n    description: \"Pull your thumb and middle finger from your chest\"\r\n  },\r\n  \"look\": {\r\n    name: \"Look\",\r\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=pYhzip7LqNs7qw&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fl%2flook-at-1.gif&ehk=rFJ7dBrMGFDK0nHLzrOPAzROVE7yqyDEcb%2btLqKqYOI%3d&risl=&pid=ImgRaw&r=0\",\r\n    description: \"Point your fingers from your eyes forward\"\r\n  },\r\n  \"love\": {\r\n    name: \"Love\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/l/love.gif\",\r\n    description: \"Cross your arms over your chest\"\r\n  },\r\n  \"mom\": {\r\n    name: \"Mom\",\r\n    gif: \"https://media.giphy.com/media/l0MYQcLKwtl5v6H1S/giphy.gif\",\r\n    description: \"Tap your chin with your thumb\"\r\n  },\r\n  \"more\": {\r\n    name: \"More\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/m/more.gif\",\r\n    description: \"Tap your fingertips together\"\r\n  },\r\n  \"no\": {\r\n    name: \"No\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/n/no.gif\",\r\n    description: \"Snap your fingers together\"\r\n  },\r\n  \"orange\": {\r\n    name: \"Orange\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/o/orange.gif\",\r\n    description: \"Squeeze your hand at your mouth like squeezing an orange\"\r\n  },\r\n  \"please\": {\r\n    name: \"Please\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/p/please.gif\",\r\n    description: \"Rub your chest in a circular motion\"\r\n  },\r\n  \"red\": {\r\n    name: \"Red\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/r/red.gif\",\r\n    description: \"Brush your lips with your index finger\"\r\n  },\r\n  \"run\": {\r\n    name: \"Run\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/r/run.gif\",\r\n    description: \"Hook your thumbs and wiggle your fingers\"\r\n  },\r\n  \"sad\": {\r\n    name: \"Sad\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/s/sad.gif\",\r\n    description: \"Drop both hands down your face\"\r\n  },\r\n  \"see\": {\r\n    name: \"See\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/l/look-at-2.gif\",\r\n    description: \"Point from your eyes forward\"\r\n  },\r\n  \"sleep\": {\r\n    name: \"Sleep\",\r\n    gif: \"https://media4.giphy.com/media/3o7TKnRuBdakLslcaI/200.gif?cid=790b76110d8f185a9713f36dd65a0df801576e01b403c95c&rid=200.gif&ct=g\",\r\n    description: \"Rest your head on your hands\"\r\n  },\r\n  \"sorry\": {\r\n    name: \"Sorry\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/s/sorry.gif\",\r\n    description: \"Rub your fist in a circle on your chest\"\r\n  },\r\n  \"thank\": {\r\n    name: \"Thank You\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/t/thank-you.gif\",\r\n    description: \"Touch your chin and move your hand forward\"\r\n  },\r\n  \"water\": {\r\n    name: \"Water\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/w/water.gif\",\r\n    description: \"Tap your chin with a 'W' handshape\"\r\n  },\r\n  \"white\": {\r\n    name: \"White\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/w/white.gif\",\r\n    description: \"Touch your chest and pull your hand away\"\r\n  },\r\n  \"yellow\": {\r\n    name: \"Yellow\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/y/yellow.gif\",\r\n    description: \"Shake a 'Y' handshape\"\r\n  },\r\n  \"yes\": {\r\n    name: \"Yes\",\r\n    gif: \"https://media.tenor.com/oYIirlyIih0AAAAC/yes-asl.gif\",\r\n    description: \"Nod your fist up and down\"\r\n  }\r\n};\r\n\r\nconst TrainingPage = ({ onBackToHome }) => {\r\n  const [currentSign, setCurrentSign] = useState('hello');\r\n  const [status, setStatus] = useState('');\r\n  const [isCapturing, setIsCapturing] = useState(false);\r\n  const [autoRecordMode, setAutoRecordMode] = useState(false);\r\n  // eslint-disable-next-line no-unused-vars\r\n  const [recordedVideos, setRecordedVideos] = useState([]);\r\n\r\n  const webcamRef = useRef(null);\r\n  const autoRecordTimeoutRef = useRef(null);\r\n  const matchCountRef = useRef(0);\r\n\r\n  // Use sign detection hook\r\n  const {\r\n    isConnected,\r\n    prediction,\r\n    isAIRecording,\r\n    recordingStatus,\r\n    signMatched,\r\n    targetSign,\r\n    startRecording: startAIRecording,\r\n    stopRecording: stopAIRecording,\r\n    startFrameCapture\r\n  } = useSignDetection();\r\n\r\n  const handleSignChange = useCallback((event) => {\r\n    setCurrentSign(event.target.value);\r\n  }, []);\r\n\r\n  const startDetection = useCallback(() => {\r\n    if (!webcamRef.current) {\r\n      setStatus('Camera not available');\r\n      return;\r\n    }\r\n\r\n    setIsCapturing(true);\r\n    startFrameCapture(webcamRef, 100); // Send frame every 100ms\r\n    setStatus('AI detection started');\r\n  }, [startFrameCapture]);\r\n\r\n  const startRecording = useCallback(() => {\r\n    if (!isConnected) {\r\n      setStatus('AI backend not connected');\r\n      return;\r\n    }\r\n\r\n    if (!webcamRef.current) {\r\n      setStatus('Camera not available');\r\n      return;\r\n    }\r\n\r\n    // Toggle auto-record mode\r\n    if (!autoRecordMode) {\r\n      setAutoRecordMode(true);\r\n      setStatus(`Get ready! Perform \"${signLanguageData[currentSign].name}\" sign when you're confident. Recording will start automatically when detected.`);\r\n      matchCountRef.current = 0;\r\n    } else {\r\n      setAutoRecordMode(false);\r\n      setStatus('Auto-recording disabled');\r\n      matchCountRef.current = 0;\r\n      if (autoRecordTimeoutRef.current) {\r\n        clearTimeout(autoRecordTimeoutRef.current);\r\n      }\r\n    }\r\n\r\n    // Also start frame capture if not already started\r\n    if (!isCapturing) {\r\n      startDetection();\r\n    }\r\n  }, [currentSign, isConnected, isCapturing, startDetection, autoRecordMode]);\r\n\r\n  const stopRecording = useCallback(() => {\r\n    // Stop AI recording and auto-record mode\r\n    if (isAIRecording) {\r\n      stopAIRecording();\r\n    }\r\n    setAutoRecordMode(false);\r\n    matchCountRef.current = 0;\r\n    if (autoRecordTimeoutRef.current) {\r\n      clearTimeout(autoRecordTimeoutRef.current);\r\n    }\r\n    setStatus('Recording stopped');\r\n  }, [stopAIRecording, isAIRecording]);\r\n\r\n  const downloadRecording = (video) => {\r\n    const a = document.createElement('a');\r\n    a.href = video.url;\r\n    a.download = `sign_${video.sign}_${video.timestamp}.webm`;\r\n    a.click();\r\n  };\r\n\r\n  // Auto-start detection when connected\r\n  useEffect(() => {\r\n    if (isConnected && webcamRef.current && !isCapturing) {\r\n      startDetection();\r\n    }\r\n  }, [isConnected, startDetection, isCapturing]);\r\n\r\n  // Smart auto-recording logic\r\n  useEffect(() => {\r\n    if (!prediction || !autoRecordMode) {\r\n      matchCountRef.current = 0;\r\n      return;\r\n    }\r\n\r\n    const predictedSign = prediction.sign.toLowerCase();\r\n    const targetSignLower = signLanguageData[currentSign].name.toLowerCase();\r\n    const confidence = prediction.confidence;\r\n\r\n    // Check if sign matches with high confidence\r\n    if (predictedSign === targetSignLower && confidence > 0.8) {\r\n      matchCountRef.current += 1;\r\n\r\n      // Start recording after 3 consecutive matches\r\n      if (matchCountRef.current >= 3 && !isAIRecording) {\r\n        setStatus(`Perfect! Recording ${signLanguageData[currentSign].name}...`);\r\n        startAIRecording(signLanguageData[currentSign].name);\r\n\r\n        // Auto-stop recording after 3 seconds\r\n        autoRecordTimeoutRef.current = setTimeout(() => {\r\n          stopAIRecording();\r\n          setStatus(`Recording complete! Great job with ${signLanguageData[currentSign].name}`);\r\n          setAutoRecordMode(false);\r\n          matchCountRef.current = 0;\r\n        }, 3000);\r\n      }\r\n    } else {\r\n      // Reset match count if sign doesn't match\r\n      matchCountRef.current = 0;\r\n    }\r\n\r\n    return () => {\r\n      if (autoRecordTimeoutRef.current) {\r\n        clearTimeout(autoRecordTimeoutRef.current);\r\n      }\r\n    };\r\n  }, [prediction, autoRecordMode, currentSign, isAIRecording, startAIRecording, stopAIRecording]);\r\n\r\n  return (\r\n    <TrainingContainer>\r\n      <Navigation>\r\n        <NavContainer>\r\n          <Logo>\r\n            <LogoIcon>\r\n              <Brain size={24} />\r\n            </LogoIcon>\r\n            ASL Neural\r\n          </Logo>\r\n          <BackButton onClick={onBackToHome}>\r\n            <ArrowLeft size={18} />\r\n            Back to Home\r\n          </BackButton>\r\n        </NavContainer>\r\n      </Navigation>\r\n\r\n      <MainContent>\r\n        <div style={{ textAlign: 'center', marginBottom: 'var(--space-12)' }}>\r\n          <StatusBadge>\r\n            <Eye size={16} />\r\n            Neural Vision Active\r\n          </StatusBadge>\r\n        </div>\r\n\r\n        <PageTitle>AI Training Session</PageTitle>\r\n        <PageSubtitle>\r\n          Experience real-time neural network analysis as our AI learns from your sign language practice\r\n        </PageSubtitle>\r\n\r\n        <TrainingGrid>\r\n          <CameraSection>\r\n            <SectionTitle>\r\n              <SectionIcon>\r\n                <Camera size={24} />\r\n              </SectionIcon>\r\n              Neural Vision Feed\r\n            </SectionTitle>\r\n\r\n            <ConnectionStatus connected={isConnected}>\r\n              {isConnected ? <Wifi size={16} /> : <WifiOff size={16} />}\r\n              {isConnected ? 'AI Connected' : 'AI Disconnected'}\r\n            </ConnectionStatus>\r\n\r\n            {prediction && (\r\n              <PredictionDisplay matched={signMatched} isStale={prediction.isStale}>\r\n                <PredictionText matched={signMatched} isStale={prediction.isStale}>\r\n                  Detected: {prediction.sign}\r\n                  {prediction.isStale && ' (previous)'}\r\n                </PredictionText>\r\n                <ConfidenceBar>\r\n                  <ConfidenceFill confidence={prediction.confidence} />\r\n                </ConfidenceBar>\r\n                <div style={{ fontSize: '0.875rem', marginTop: '8px', color: 'var(--text-secondary)' }}>\r\n                  Confidence: {Math.round(prediction.confidence * 100)}%\r\n                  {signMatched && targetSign && (\r\n                    <span style={{ color: 'var(--success-600)', marginLeft: '8px' }}>\r\n                      ✓ Match! Recording...\r\n                    </span>\r\n                  )}\r\n                  {autoRecordMode && !isAIRecording && (\r\n                    <div style={{ color: 'var(--primary-600)', marginTop: '4px' }}>\r\n                      🎯 Auto-record mode: Perform \"{signLanguageData[currentSign].name}\" sign\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </PredictionDisplay>\r\n            )}\r\n\r\n            {!prediction && autoRecordMode && (\r\n              <PredictionDisplay>\r\n                <PredictionText>\r\n                  🎯 Ready to detect \"{signLanguageData[currentSign].name}\"\r\n                </PredictionText>\r\n                <div style={{ fontSize: '0.875rem', color: 'var(--text-secondary)' }}>\r\n                  Perform the sign when you're confident. Recording will start automatically.\r\n                </div>\r\n              </PredictionDisplay>\r\n            )}\r\n            <WebcamContainer>\r\n              <StyledWebcam\r\n                ref={webcamRef}\r\n                audio={false}\r\n                screenshotFormat=\"image/jpeg\"\r\n                videoConstraints={{\r\n                  width: 640,\r\n                  height: 480,\r\n                  facingMode: \"user\"\r\n                }}\r\n              />\r\n              <RecordingOverlay isRecording={isAIRecording}>\r\n                {isAIRecording ? (\r\n                  <>\r\n                    <div style={{\r\n                      width: '8px',\r\n                      height: '8px',\r\n                      borderRadius: '50%',\r\n                      backgroundColor: 'white',\r\n                      marginRight: '4px'\r\n                    }} />\r\n                    Recording\r\n                  </>\r\n                ) : (\r\n                  <>\r\n                    <Eye size={16} />\r\n                    Ready\r\n                  </>\r\n                )}\r\n              </RecordingOverlay>\r\n            </WebcamContainer>\r\n          </CameraSection>\r\n\r\n          <SignSection>\r\n            <SectionTitle>\r\n              <SectionIcon>\r\n                <Target size={24} />\r\n              </SectionIcon>\r\n              Select a Sign\r\n            </SectionTitle>\r\n            <SignSelector\r\n              value={currentSign}\r\n              onChange={handleSignChange}\r\n              disabled={isAIRecording}\r\n            >\r\n              {Object.keys(signLanguageData).map(signKey => (\r\n                <option key={signKey} value={signKey}>\r\n                  {signLanguageData[signKey].name}\r\n                </option>\r\n              ))}\r\n            </SignSelector>\r\n            <SignDisplay>\r\n              <img\r\n                src={signLanguageData[currentSign].gif}\r\n                alt={signLanguageData[currentSign].name}\r\n                onError={(e) => {\r\n                  e.target.style.display = 'none';\r\n                  e.target.nextSibling.style.display = 'flex';\r\n                }}\r\n              />\r\n              <div style={{display: 'none', fontSize: '3rem'}}>\r\n                📷\r\n              </div>\r\n            </SignDisplay>\r\n            <SignName>{signLanguageData[currentSign].name}</SignName>\r\n            <SignDescription>\r\n              {signLanguageData[currentSign].description}\r\n            </SignDescription>\r\n          </SignSection>\r\n        </TrainingGrid>\r\n\r\n        <ControlsSection>\r\n          <ControlButton\r\n            variant=\"primary\"\r\n            onClick={isAIRecording || autoRecordMode ? stopRecording : startRecording}\r\n          >\r\n            {isAIRecording ? (\r\n              <>\r\n                <Square size={18} />\r\n                Stop Recording\r\n              </>\r\n            ) : autoRecordMode ? (\r\n              <>\r\n                <Square size={18} />\r\n                Cancel Auto-Record\r\n              </>\r\n            ) : (\r\n              <>\r\n                <Play size={18} />\r\n                Start Smart Recording\r\n              </>\r\n            )}\r\n          </ControlButton>\r\n        </ControlsSection>\r\n\r\n        {(status || recordingStatus) && (\r\n          <StatusMessage type={(status || recordingStatus).includes('error') ? 'error' : (status || recordingStatus).includes('success') ? 'success' : 'info'}>\r\n            {recordingStatus || status}\r\n          </StatusMessage>\r\n        )}\r\n\r\n        {recordedVideos.length > 0 && (\r\n          <RecordingsSection>\r\n            <RecordingsTitle>Your Practice Recordings</RecordingsTitle>\r\n            <RecordingsGrid>\r\n              {recordedVideos.map((video) => (\r\n                <RecordingCard key={video.id}>\r\n                  <RecordingTitle>{video.sign}</RecordingTitle>\r\n                  <RecordingTime>\r\n                    {new Date(video.timestamp).toLocaleString()}\r\n                  </RecordingTime>\r\n                  <DownloadButton onClick={() => downloadRecording(video)}>\r\n                    <Download size={16} />\r\n                    Download\r\n                  </DownloadButton>\r\n                </RecordingCard>\r\n              ))}\r\n            </RecordingsGrid>\r\n          </RecordingsSection>\r\n        )}\r\n      </MainContent>\r\n    </TrainingContainer>\r\n  );\r\n};\r\n\r\nexport default TrainingPage; "], "mappings": "gxBAAA,OAASA,QAAQ,CAAEC,MAAM,CAAEC,WAAW,CAAEC,SAAS,KAAQ,OAAO,CAChE,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CACtC,MAAO,CAAAC,MAAM,KAAM,cAAc,CACjC,OACEC,KAAK,CACLC,MAAM,CACNC,SAAS,CACTC,IAAI,CACJC,MAAM,CACNC,QAAQ,CACRC,GAAG,CACHC,MAAM,CACNC,IAAI,CACJC,OAAO,KACF,cAAc,CACrB,OAASC,gBAAgB,KAAQ,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE7D,KAAM,CAAAC,iBAAiB,CAAGnB,MAAM,CAACoB,GAAG,CAAAC,eAAA,GAAAA,eAAA,CAAAC,sBAAA,udAmBnC,CAED,KAAM,CAAAC,UAAU,CAAGvB,MAAM,CAACwB,GAAG,CAAAC,gBAAA,GAAAA,gBAAA,CAAAH,sBAAA,2QAW5B,CAED,KAAM,CAAAI,YAAY,CAAG1B,MAAM,CAACoB,GAAG,CAAAO,gBAAA,GAAAA,gBAAA,CAAAL,sBAAA,oOAW9B,CAED,KAAM,CAAAM,IAAI,CAAG5B,MAAM,CAACoB,GAAG,CAAAS,gBAAA,GAAAA,gBAAA,CAAAP,sBAAA,+XAgBtB,CAED,KAAM,CAAAQ,QAAQ,CAAG9B,MAAM,CAACoB,GAAG,CAAAW,gBAAA,GAAAA,gBAAA,CAAAT,sBAAA,uTAe1B,CAED,KAAM,CAAAU,UAAU,CAAGhC,MAAM,CAACiC,MAAM,CAAAC,gBAAA,GAAAA,gBAAA,CAAAZ,sBAAA,+rBA2B/B,CAED,KAAM,CAAAa,SAAS,CAAGnC,MAAM,CAACoC,EAAE,CAAAC,gBAAA,GAAAA,gBAAA,CAAAf,sBAAA,qXAe1B,CAED,KAAM,CAAAgB,YAAY,CAAGtC,MAAM,CAACuC,CAAC,CAAAC,gBAAA,GAAAA,gBAAA,CAAAlB,sBAAA,qTAc5B,CAED,KAAM,CAAAmB,WAAW,CAAGzC,MAAM,CAACoB,GAAG,CAAAsB,gBAAA,GAAAA,gBAAA,CAAApB,sBAAA,0gBAmB7B,CAED,KAAM,CAAAqB,WAAW,CAAG3C,MAAM,CAAC4C,IAAI,CAAAC,gBAAA,GAAAA,gBAAA,CAAAvB,sBAAA,wOAS9B,CAED,KAAM,CAAAwB,YAAY,CAAG9C,MAAM,CAACoB,GAAG,CAAA2B,gBAAA,GAAAA,gBAAA,CAAAzB,sBAAA,sVAgB9B,CAED,KAAM,CAAA0B,aAAa,CAAGhD,MAAM,CAACoB,GAAG,CAAA6B,iBAAA,GAAAA,iBAAA,CAAA3B,sBAAA,02BAwC/B,CAED,KAAM,CAAA4B,YAAY,CAAGlD,MAAM,CAACmD,EAAE,CAAAC,iBAAA,GAAAA,iBAAA,CAAA9B,sBAAA,kUAc7B,CAED,KAAM,CAAA+B,WAAW,CAAGrD,MAAM,CAACoB,GAAG,CAAAkC,iBAAA,GAAAA,iBAAA,CAAAhC,sBAAA,uTAe7B,CAED,KAAM,CAAAiC,eAAe,CAAGvD,MAAM,CAACoB,GAAG,CAAAoC,iBAAA,GAAAA,iBAAA,CAAAlC,sBAAA,ueAmBjC,CAED,KAAM,CAAAmC,YAAY,CAAGzD,MAAM,CAACC,MAAM,CAAC,CAAAyD,iBAAA,GAAAA,iBAAA,CAAApC,sBAAA,iEAIlC,CAED,KAAM,CAAAqC,gBAAgB,CAAG3D,MAAM,CAACoB,GAAG,CAAAwC,iBAAA,GAAAA,iBAAA,CAAAtC,sBAAA,6eAInBuC,KAAK,EAAIA,KAAK,CAACC,WAAW,CACtC,kBAAkB,CAClB,mBAAmB,CAWRD,KAAK,EAAIA,KAAK,CAACC,WAAW,CAAG,qBAAqB,CAAG,MAAM,CAMzE,CAED,KAAM,CAAAC,WAAW,CAAG/D,MAAM,CAACoB,GAAG,CAAA4C,iBAAA,GAAAA,iBAAA,CAAA1C,sBAAA,odAoB7B,CAED,KAAM,CAAA2C,YAAY,CAAGjE,MAAM,CAACkE,MAAM,CAAAC,iBAAA,GAAAA,iBAAA,CAAA7C,sBAAA,6wBAkCjC,CAED,KAAM,CAAA8C,WAAW,CAAGpE,MAAM,CAACoB,GAAG,CAAAiD,iBAAA,GAAAA,iBAAA,CAAA/C,sBAAA,glBA6B7B,CAED,KAAM,CAAAgD,QAAQ,CAAGtE,MAAM,CAACuE,EAAE,CAAAC,iBAAA,GAAAA,iBAAA,CAAAlD,sBAAA,0NAUzB,CAED,KAAM,CAAAmD,eAAe,CAAGzE,MAAM,CAACuC,CAAC,CAAAmC,iBAAA,GAAAA,iBAAA,CAAApD,sBAAA,uJAO/B,CAED,KAAM,CAAAqD,eAAe,CAAG3E,MAAM,CAACoB,GAAG,CAAAwD,iBAAA,GAAAA,iBAAA,CAAAtD,sBAAA,sOAWjC,CAED,KAAM,CAAAuD,aAAa,CAAG7E,MAAM,CAACiC,MAAM,CAAA6C,iBAAA,GAAAA,iBAAA,CAAAxD,sBAAA,iyBACnBuC,KAAK,EAAIA,KAAK,CAACkB,OAAO,GAAK,SAAS,CAC9C,oBAAoB,CACpB,mBAAmB,CACblB,KAAK,EAAIA,KAAK,CAACkB,OAAO,GAAK,SAAS,CAC1C,MAAM,CACN,gCAAgC,CAC3BlB,KAAK,EAAIA,KAAK,CAACkB,OAAO,GAAK,SAAS,CACzC,OAAO,CACP,qBAAqB,CAmBXlB,KAAK,EAAIA,KAAK,CAACkB,OAAO,GAAK,SAAS,CAC9C,kBAAkB,CAClB,kBAAkB,CAINlB,KAAK,EAAIA,KAAK,CAACkB,OAAO,GAAK,SAAS,CAC9C,kBAAkB,CAClB,kBAAkB,CACRlB,KAAK,EAAIA,KAAK,CAACkB,OAAO,GAAK,SAAS,CAC9C,oBAAoB,CACpB,gBAAgB,CAavB,CAED,KAAM,CAAAC,aAAa,CAAGhF,MAAM,CAACoB,GAAG,CAAA6D,iBAAA,GAAAA,iBAAA,CAAA3D,sBAAA,mSAKhBuC,KAAK,EACjBA,KAAK,CAACqB,IAAI,GAAK,SAAS,CAAG,oBAAoB,CAC/CrB,KAAK,CAACqB,IAAI,GAAK,OAAO,CAAG,kBAAkB,CAC3C,oBAAoB,CAQvB,CAED,KAAM,CAAAC,iBAAiB,CAAGnF,MAAM,CAACoB,GAAG,CAAAgE,iBAAA,GAAAA,iBAAA,CAAA9D,sBAAA,kOAQnC,CAED,KAAM,CAAA+D,eAAe,CAAGrF,MAAM,CAACuE,EAAE,CAAAe,iBAAA,GAAAA,iBAAA,CAAAhE,sBAAA,kLAOhC,CAED,KAAM,CAAAiE,cAAc,CAAGvF,MAAM,CAACoB,GAAG,CAAAoE,iBAAA,GAAAA,iBAAA,CAAAlE,sBAAA,qNAShC,CAED,KAAM,CAAAmE,aAAa,CAAGzF,MAAM,CAACoB,GAAG,CAAAsE,iBAAA,GAAAA,iBAAA,CAAApE,sBAAA,qVAa/B,CAED,KAAM,CAAAqE,cAAc,CAAG3F,MAAM,CAACuC,CAAC,CAAAqD,iBAAA,GAAAA,iBAAA,CAAAtE,sBAAA,wJAM9B,CAED,KAAM,CAAAuE,aAAa,CAAG7F,MAAM,CAACuC,CAAC,CAAAuD,iBAAA,GAAAA,iBAAA,CAAAxE,sBAAA,iGAI7B,CAED,KAAM,CAAAyE,cAAc,CAAG/F,MAAM,CAACiC,MAAM,CAAA+D,iBAAA,GAAAA,iBAAA,CAAA1E,sBAAA,2aAmBnC,CAED,KAAM,CAAA2E,iBAAiB,CAAGjG,MAAM,CAACoB,GAAG,CAAA8E,iBAAA,GAAAA,iBAAA,CAAA5E,sBAAA,+mBAEduC,KAAK,EAAI,CAC3B,GAAIA,KAAK,CAACsC,OAAO,CAAE,MAAO,oBAAoB,CAC9C,GAAItC,KAAK,CAACuC,OAAO,CAAE,MAAO,oBAAoB,CAC9C,MAAO,qBAAqB,CAC9B,CAAC,CAOUvC,KAAK,EAAIA,KAAK,CAACuC,OAAO,CAAG,GAAG,CAAG,CAAC,CAMzCvC,KAAK,EAAIA,KAAK,CAACsC,OAAO,gIAIvB,CAECtC,KAAK,EAAIA,KAAK,CAACuC,OAAO,4CAEvB,CAYF,CAED,KAAM,CAAAC,cAAc,CAAGrG,MAAM,CAACoB,GAAG,CAAAkF,iBAAA,GAAAA,iBAAA,CAAAhF,sBAAA,uKAGtBuC,KAAK,EAAI,CAChB,GAAIA,KAAK,CAACsC,OAAO,CAAE,MAAO,oBAAoB,CAC9C,GAAItC,KAAK,CAACuC,OAAO,CAAE,MAAO,oBAAoB,CAC9C,MAAO,qBAAqB,CAC9B,CAAC,CAMF,CAED,KAAM,CAAAG,aAAa,CAAGvG,MAAM,CAACoB,GAAG,CAAAoF,iBAAA,GAAAA,iBAAA,CAAAlF,sBAAA,wKAO/B,CAED,KAAM,CAAAmF,cAAc,CAAGzG,MAAM,CAACoB,GAAG,CAAAsF,iBAAA,GAAAA,iBAAA,CAAApF,sBAAA,+FAEjBuC,KAAK,EAAI,CACrB,GAAIA,KAAK,CAAC8C,UAAU,CAAG,GAAG,CAAE,MAAO,oBAAoB,CACvD,GAAI9C,KAAK,CAAC8C,UAAU,CAAG,GAAG,CAAE,MAAO,oBAAoB,CACvD,MAAO,kBAAkB,CAC3B,CAAC,CACQ9C,KAAK,EAAKA,KAAK,CAAC8C,UAAU,CAAG,GAAI,CAE3C,CAED,KAAM,CAAAC,gBAAgB,CAAG5G,MAAM,CAACoB,GAAG,CAAAyF,iBAAA,GAAAA,iBAAA,CAAAvF,sBAAA,qQAQnBuC,KAAK,EAAIA,KAAK,CAACiD,SAAS,CAAG,mBAAmB,CAAG,iBAAiB,CACvEjD,KAAK,EAAIA,KAAK,CAACiD,SAAS,CAAG,oBAAoB,CAAG,kBAAkB,CACzDjD,KAAK,EAAIA,KAAK,CAACiD,SAAS,CAAG,oBAAoB,CAAG,kBAAkB,CACzF,CAED;AACA,KAAM,CAAAC,gBAAgB,CAAG,CACvB,OAAO,CAAE,CACPC,IAAI,CAAE,OAAO,CACbC,GAAG,CAAE,2DAA2D,CAChEC,WAAW,CAAE,8DACf,CAAC,CACD,UAAU,CAAE,CACVF,IAAI,CAAE,UAAU,CAChBC,GAAG,CAAE,6DAA6D,CAClEC,WAAW,CAAE,2DACf,CAAC,CACD,KAAK,CAAE,CACLF,IAAI,CAAE,KAAK,CACXC,GAAG,CAAE,mDAAmD,CACxDC,WAAW,CAAE,yDACf,CAAC,CACD,WAAW,CAAE,CACXF,IAAI,CAAE,WAAW,CACjBC,GAAG,CAAE,mDAAmD,CACxDC,WAAW,CAAE,oDACf,CAAC,CACD,QAAQ,CAAE,CACRF,IAAI,CAAE,QAAQ,CACdC,GAAG,CAAE,2DAA2D,CAChEC,WAAW,CAAE,yDACf,CAAC,CACD,KAAK,CAAE,CACLF,IAAI,CAAE,KAAK,CACXC,GAAG,CAAE,6CAA6C,CAClDC,WAAW,CAAE,8CACf,CAAC,CACD,OAAO,CAAE,CACPF,IAAI,CAAE,OAAO,CACbC,GAAG,CAAE,2DAA2D,CAChEC,WAAW,CAAE,uCACf,CAAC,CACD,KAAK,CAAE,CACLF,IAAI,CAAE,KAAK,CACXC,GAAG,CAAE,kGAAkG,CACvGC,WAAW,CAAE,sCACf,CAAC,CACD,MAAM,CAAE,CACNF,IAAI,CAAE,MAAM,CACZC,GAAG,CAAE,8EAA8E,CACnFC,WAAW,CAAE,oDACf,CAAC,CACD,MAAM,CAAE,CACNF,IAAI,CAAE,MAAM,CACZC,GAAG,CAAE,2DAA2D,CAChEC,WAAW,CAAE,qCACf,CAAC,CACD,MAAM,CAAE,CACNF,IAAI,CAAE,MAAM,CACZC,GAAG,CAAE,8CAA8C,CACnDC,WAAW,CAAE,qCACf,CAAC,CACD,QAAQ,CAAE,CACRF,IAAI,CAAE,QAAQ,CACdC,GAAG,CAAE,2DAA2D,CAChEC,WAAW,CAAE,4CACf,CAAC,CACD,MAAM,CAAE,CACNF,IAAI,CAAE,MAAM,CACZC,GAAG,CAAE,8CAA8C,CACnDC,WAAW,CAAE,yCACf,CAAC,CACD,WAAW,CAAE,CACXF,IAAI,CAAE,WAAW,CACjBC,GAAG,CAAE,2DAA2D,CAChEC,WAAW,CAAE,0DACf,CAAC,CACD,KAAK,CAAE,CACLF,IAAI,CAAE,KAAK,CACXC,GAAG,CAAE,6CAA6C,CAClDC,WAAW,CAAE,6CACf,CAAC,CACD,KAAK,CAAE,CACLF,IAAI,CAAE,KAAK,CACXC,GAAG,CAAE,6CAA6C,CAClDC,WAAW,CAAE,sDACf,CAAC,CACD,MAAM,CAAE,CACNF,IAAI,CAAE,MAAM,CACZC,GAAG,CAAE,8CAA8C,CACnDC,WAAW,CAAE,yDACf,CAAC,CACD,OAAO,CAAE,CACPF,IAAI,CAAE,OAAO,CACbC,GAAG,CAAE,+CAA+C,CACpDC,WAAW,CAAE,6CACf,CAAC,CACD,MAAM,CAAE,CACNF,IAAI,CAAE,MAAM,CACZC,GAAG,CAAE,8CAA8C,CACnDC,WAAW,CAAE,uBACf,CAAC,CACD,MAAM,CAAE,CACNF,IAAI,CAAE,MAAM,CACZC,GAAG,CAAE,8CAA8C,CACnDC,WAAW,CAAE,qCACf,CAAC,CACD,KAAK,CAAE,CACLF,IAAI,CAAE,KAAK,CACXC,GAAG,CAAE,6CAA6C,CAClDC,WAAW,CAAE,oCACf,CAAC,CACD,SAAS,CAAE,CACTF,IAAI,CAAE,SAAS,CACfC,GAAG,CAAE,iDAAiD,CACtDC,WAAW,CAAE,kEACf,CAAC,CACD,OAAO,CAAE,CACPF,IAAI,CAAE,OAAO,CACbC,GAAG,CAAE,+CAA+C,CACpDC,WAAW,CAAE,yCACf,CAAC,CACD,KAAK,CAAE,CACLF,IAAI,CAAE,KAAK,CACXC,GAAG,CAAE,6CAA6C,CAClDC,WAAW,CAAE,kDACf,CAAC,CACD,WAAW,CAAE,CACXF,IAAI,CAAE,WAAW,CACjBC,GAAG,CAAE,mDAAmD,CACxDC,WAAW,CAAE,uDACf,CAAC,CACD,KAAK,CAAE,CACLF,IAAI,CAAE,KAAK,CACXC,GAAG,CAAE,6CAA6C,CAClDC,WAAW,CAAE,wCACf,CAAC,CACD,KAAK,CAAE,CACLF,IAAI,CAAE,KAAK,CACXC,GAAG,CAAE,6CAA6C,CAClDC,WAAW,CAAE,6CACf,CAAC,CACD,OAAO,CAAE,CACPF,IAAI,CAAE,OAAO,CACbC,GAAG,CAAE,+CAA+C,CACpDC,WAAW,CAAE,kDACf,CAAC,CACD,OAAO,CAAE,CACPF,IAAI,CAAE,OAAO,CACbC,GAAG,CAAE,+CAA+C,CACpDC,WAAW,CAAE,8BACf,CAAC,CACD,MAAM,CAAE,CACNF,IAAI,CAAE,MAAM,CACZC,GAAG,CAAE,2DAA2D,CAChEC,WAAW,CAAE,iCACf,CAAC,CACD,KAAK,CAAE,CACLF,IAAI,CAAE,KAAK,CACXC,GAAG,CAAE,6CAA6C,CAClDC,WAAW,CAAE,6CACf,CAAC,CACD,KAAK,CAAE,CACLF,IAAI,CAAE,KAAK,CACXC,GAAG,CAAE,6CAA6C,CAClDC,WAAW,CAAE,qDACf,CAAC,CACD,MAAM,CAAE,CACNF,IAAI,CAAE,MAAM,CACZC,GAAG,CAAE,oDAAoD,CACzDC,WAAW,CAAE,mCACf,CAAC,CACD,KAAK,CAAE,CACLF,IAAI,CAAE,KAAK,CACXC,GAAG,CAAE,2DAA2D,CAChEC,WAAW,CAAE,mCACf,CAAC,CACD,OAAO,CAAE,CACPF,IAAI,CAAE,OAAO,CACbC,GAAG,CAAE,0MAA0M,CAC/MC,WAAW,CAAE,gDACf,CAAC,CACD,OAAO,CAAE,CACPF,IAAI,CAAE,OAAO,CACbC,GAAG,CAAE,8EAA8E,CACnFC,WAAW,CAAE,qCACf,CAAC,CACD,KAAK,CAAE,CACLF,IAAI,CAAE,KAAK,CACXC,GAAG,CAAE,2NAA2N,CAChOC,WAAW,CAAE,oCACf,CAAC,CACD,KAAK,CAAE,CACLF,IAAI,CAAE,KAAK,CACXC,GAAG,CAAE,6CAA6C,CAClDC,WAAW,CAAE,+CACf,CAAC,CACD,UAAU,CAAE,CACVF,IAAI,CAAE,UAAU,CAChBC,GAAG,CAAE,kDAAkD,CACvDC,WAAW,CAAE,uDACf,CAAC,CACD,MAAM,CAAE,CACNF,IAAI,CAAE,MAAM,CACZC,GAAG,CAAE,8CAA8C,CACnDC,WAAW,CAAE,qCACf,CAAC,CACD,QAAQ,CAAE,CACRF,IAAI,CAAE,QAAQ,CACdC,GAAG,CAAE,gDAAgD,CACrDC,WAAW,CAAE,sCACf,CAAC,CACD,QAAQ,CAAE,CACRF,IAAI,CAAE,QAAQ,CACdC,GAAG,CAAE,gDAAgD,CACrDC,WAAW,CAAE,kCACf,CAAC,CACD,MAAM,CAAE,CACNF,IAAI,CAAE,MAAM,CACZC,GAAG,CAAE,8CAA8C,CACnDC,WAAW,CAAE,oCACf,CAAC,CACD,IAAI,CAAE,CACJF,IAAI,CAAE,IAAI,CACVC,GAAG,CAAE,4CAA4C,CACjDC,WAAW,CAAE,gDACf,CAAC,CACD,MAAM,CAAE,CACNF,IAAI,CAAE,MAAM,CACZC,GAAG,CAAE,8CAA8C,CACnDC,WAAW,CAAE,4CACf,CAAC,CACD,OAAO,CAAE,CACPF,IAAI,CAAE,OAAO,CACbC,GAAG,CAAE,+CAA+C,CACpDC,WAAW,CAAE,uBACf,CAAC,CACD,MAAM,CAAE,CACNF,IAAI,CAAE,MAAM,CACZC,GAAG,CAAE,4DAA4D,CACjEC,WAAW,CAAE,6BACf,CAAC,CACD,OAAO,CAAE,CACPF,IAAI,CAAE,OAAO,CACbC,GAAG,CAAE,qIAAqI,CAC1IC,WAAW,CAAE,yCACf,CAAC,CACD,OAAO,CAAE,CACPF,IAAI,CAAE,OAAO,CACbC,GAAG,CAAE,4DAA4D,CACjEC,WAAW,CAAE,2DACf,CAAC,CACD,MAAM,CAAE,CACNF,IAAI,CAAE,MAAM,CACZC,GAAG,CAAE,qOAAqO,CAC1OC,WAAW,CAAE,kCACf,CAAC,CACD,OAAO,CAAE,CACPF,IAAI,CAAE,OAAO,CACbC,GAAG,CAAE,2DAA2D,CAChEC,WAAW,CAAE,wDACf,CAAC,CACD,KAAK,CAAE,CACLF,IAAI,CAAE,KAAK,CACXC,GAAG,CAAE,4DAA4D,CACjEC,WAAW,CAAE,kDACf,CAAC,CACD,QAAQ,CAAE,CACRF,IAAI,CAAE,QAAQ,CACdC,GAAG,CAAE,2DAA2D,CAChEC,WAAW,CAAE,qDACf,CAAC,CACD,MAAM,CAAE,CACNF,IAAI,CAAE,MAAM,CACZC,GAAG,CAAE,qDAAqD,CAC1DC,WAAW,CAAE,kCACf,CAAC,CACD,MAAM,CAAE,CACNF,IAAI,CAAE,MAAM,CACZC,GAAG,CAAE,8CAA8C,CACnDC,WAAW,CAAE,mDACf,CAAC,CACD,MAAM,CAAE,CACNF,IAAI,CAAE,MAAM,CACZC,GAAG,CAAE,8NAA8N,CACnOC,WAAW,CAAE,2CACf,CAAC,CACD,MAAM,CAAE,CACNF,IAAI,CAAE,MAAM,CACZC,GAAG,CAAE,8CAA8C,CACnDC,WAAW,CAAE,iCACf,CAAC,CACD,KAAK,CAAE,CACLF,IAAI,CAAE,KAAK,CACXC,GAAG,CAAE,2DAA2D,CAChEC,WAAW,CAAE,+BACf,CAAC,CACD,MAAM,CAAE,CACNF,IAAI,CAAE,MAAM,CACZC,GAAG,CAAE,8CAA8C,CACnDC,WAAW,CAAE,8BACf,CAAC,CACD,IAAI,CAAE,CACJF,IAAI,CAAE,IAAI,CACVC,GAAG,CAAE,4CAA4C,CACjDC,WAAW,CAAE,4BACf,CAAC,CACD,QAAQ,CAAE,CACRF,IAAI,CAAE,QAAQ,CACdC,GAAG,CAAE,gDAAgD,CACrDC,WAAW,CAAE,0DACf,CAAC,CACD,QAAQ,CAAE,CACRF,IAAI,CAAE,QAAQ,CACdC,GAAG,CAAE,gDAAgD,CACrDC,WAAW,CAAE,qCACf,CAAC,CACD,KAAK,CAAE,CACLF,IAAI,CAAE,KAAK,CACXC,GAAG,CAAE,6CAA6C,CAClDC,WAAW,CAAE,wCACf,CAAC,CACD,KAAK,CAAE,CACLF,IAAI,CAAE,KAAK,CACXC,GAAG,CAAE,6CAA6C,CAClDC,WAAW,CAAE,0CACf,CAAC,CACD,KAAK,CAAE,CACLF,IAAI,CAAE,KAAK,CACXC,GAAG,CAAE,6CAA6C,CAClDC,WAAW,CAAE,gCACf,CAAC,CACD,KAAK,CAAE,CACLF,IAAI,CAAE,KAAK,CACXC,GAAG,CAAE,mDAAmD,CACxDC,WAAW,CAAE,8BACf,CAAC,CACD,OAAO,CAAE,CACPF,IAAI,CAAE,OAAO,CACbC,GAAG,CAAE,iIAAiI,CACtIC,WAAW,CAAE,8BACf,CAAC,CACD,OAAO,CAAE,CACPF,IAAI,CAAE,OAAO,CACbC,GAAG,CAAE,+CAA+C,CACpDC,WAAW,CAAE,yCACf,CAAC,CACD,OAAO,CAAE,CACPF,IAAI,CAAE,WAAW,CACjBC,GAAG,CAAE,mDAAmD,CACxDC,WAAW,CAAE,4CACf,CAAC,CACD,OAAO,CAAE,CACPF,IAAI,CAAE,OAAO,CACbC,GAAG,CAAE,+CAA+C,CACpDC,WAAW,CAAE,oCACf,CAAC,CACD,OAAO,CAAE,CACPF,IAAI,CAAE,OAAO,CACbC,GAAG,CAAE,+CAA+C,CACpDC,WAAW,CAAE,0CACf,CAAC,CACD,QAAQ,CAAE,CACRF,IAAI,CAAE,QAAQ,CACdC,GAAG,CAAE,gDAAgD,CACrDC,WAAW,CAAE,uBACf,CAAC,CACD,KAAK,CAAE,CACLF,IAAI,CAAE,KAAK,CACXC,GAAG,CAAE,sDAAsD,CAC3DC,WAAW,CAAE,2BACf,CACF,CAAC,CAED,KAAM,CAAAC,YAAY,CAAGC,IAAA,EAAsB,IAArB,CAAEC,YAAa,CAAC,CAAAD,IAAA,CACpC,KAAM,CAACE,WAAW,CAAEC,cAAc,CAAC,CAAG3H,QAAQ,CAAC,OAAO,CAAC,CACvD,KAAM,CAAC4H,MAAM,CAAEC,SAAS,CAAC,CAAG7H,QAAQ,CAAC,EAAE,CAAC,CACxC,KAAM,CAAC8H,WAAW,CAAEC,cAAc,CAAC,CAAG/H,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAACgI,cAAc,CAAEC,iBAAiB,CAAC,CAAGjI,QAAQ,CAAC,KAAK,CAAC,CAC3D;AACA,KAAM,CAACkI,cAAc,CAAEC,iBAAiB,CAAC,CAAGnI,QAAQ,CAAC,EAAE,CAAC,CAExD,KAAM,CAAAoI,SAAS,CAAGnI,MAAM,CAAC,IAAI,CAAC,CAC9B,KAAM,CAAAoI,oBAAoB,CAAGpI,MAAM,CAAC,IAAI,CAAC,CACzC,KAAM,CAAAqI,aAAa,CAAGrI,MAAM,CAAC,CAAC,CAAC,CAE/B;AACA,KAAM,CACJsI,WAAW,CACXC,UAAU,CACVC,aAAa,CACbC,eAAe,CACfC,WAAW,CACXC,UAAU,CACVC,cAAc,CAAEC,gBAAgB,CAChCC,aAAa,CAAEC,eAAe,CAC9BC,iBACF,CAAC,CAAGjI,gBAAgB,CAAC,CAAC,CAEtB,KAAM,CAAAkI,gBAAgB,CAAGhJ,WAAW,CAAEiJ,KAAK,EAAK,CAC9CxB,cAAc,CAACwB,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC,CACpC,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAC,cAAc,CAAGpJ,WAAW,CAAC,IAAM,CACvC,GAAI,CAACkI,SAAS,CAACmB,OAAO,CAAE,CACtB1B,SAAS,CAAC,sBAAsB,CAAC,CACjC,OACF,CAEAE,cAAc,CAAC,IAAI,CAAC,CACpBkB,iBAAiB,CAACb,SAAS,CAAE,GAAG,CAAC,CAAE;AACnCP,SAAS,CAAC,sBAAsB,CAAC,CACnC,CAAC,CAAE,CAACoB,iBAAiB,CAAC,CAAC,CAEvB,KAAM,CAAAJ,cAAc,CAAG3I,WAAW,CAAC,IAAM,CACvC,GAAI,CAACqI,WAAW,CAAE,CAChBV,SAAS,CAAC,0BAA0B,CAAC,CACrC,OACF,CAEA,GAAI,CAACO,SAAS,CAACmB,OAAO,CAAE,CACtB1B,SAAS,CAAC,sBAAsB,CAAC,CACjC,OACF,CAEA;AACA,GAAI,CAACG,cAAc,CAAE,CACnBC,iBAAiB,CAAC,IAAI,CAAC,CACvBJ,SAAS,yBAAA2B,MAAA,CAAwBrC,gBAAgB,CAACO,WAAW,CAAC,CAACN,IAAI,oFAAiF,CAAC,CACrJkB,aAAa,CAACiB,OAAO,CAAG,CAAC,CAC3B,CAAC,IAAM,CACLtB,iBAAiB,CAAC,KAAK,CAAC,CACxBJ,SAAS,CAAC,yBAAyB,CAAC,CACpCS,aAAa,CAACiB,OAAO,CAAG,CAAC,CACzB,GAAIlB,oBAAoB,CAACkB,OAAO,CAAE,CAChCE,YAAY,CAACpB,oBAAoB,CAACkB,OAAO,CAAC,CAC5C,CACF,CAEA;AACA,GAAI,CAACzB,WAAW,CAAE,CAChBwB,cAAc,CAAC,CAAC,CAClB,CACF,CAAC,CAAE,CAAC5B,WAAW,CAAEa,WAAW,CAAET,WAAW,CAAEwB,cAAc,CAAEtB,cAAc,CAAC,CAAC,CAE3E,KAAM,CAAAe,aAAa,CAAG7I,WAAW,CAAC,IAAM,CACtC;AACA,GAAIuI,aAAa,CAAE,CACjBO,eAAe,CAAC,CAAC,CACnB,CACAf,iBAAiB,CAAC,KAAK,CAAC,CACxBK,aAAa,CAACiB,OAAO,CAAG,CAAC,CACzB,GAAIlB,oBAAoB,CAACkB,OAAO,CAAE,CAChCE,YAAY,CAACpB,oBAAoB,CAACkB,OAAO,CAAC,CAC5C,CACA1B,SAAS,CAAC,mBAAmB,CAAC,CAChC,CAAC,CAAE,CAACmB,eAAe,CAAEP,aAAa,CAAC,CAAC,CAEpC,KAAM,CAAAiB,iBAAiB,CAAIC,KAAK,EAAK,CACnC,KAAM,CAAAC,CAAC,CAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,CACrCF,CAAC,CAACG,IAAI,CAAGJ,KAAK,CAACK,GAAG,CAClBJ,CAAC,CAACK,QAAQ,SAAAT,MAAA,CAAWG,KAAK,CAACO,IAAI,MAAAV,MAAA,CAAIG,KAAK,CAACQ,SAAS,SAAO,CACzDP,CAAC,CAACQ,KAAK,CAAC,CAAC,CACX,CAAC,CAED;AACAjK,SAAS,CAAC,IAAM,CACd,GAAIoI,WAAW,EAAIH,SAAS,CAACmB,OAAO,EAAI,CAACzB,WAAW,CAAE,CACpDwB,cAAc,CAAC,CAAC,CAClB,CACF,CAAC,CAAE,CAACf,WAAW,CAAEe,cAAc,CAAExB,WAAW,CAAC,CAAC,CAE9C;AACA3H,SAAS,CAAC,IAAM,CACd,GAAI,CAACqI,UAAU,EAAI,CAACR,cAAc,CAAE,CAClCM,aAAa,CAACiB,OAAO,CAAG,CAAC,CACzB,OACF,CAEA,KAAM,CAAAc,aAAa,CAAG7B,UAAU,CAAC0B,IAAI,CAACI,WAAW,CAAC,CAAC,CACnD,KAAM,CAAAC,eAAe,CAAGpD,gBAAgB,CAACO,WAAW,CAAC,CAACN,IAAI,CAACkD,WAAW,CAAC,CAAC,CACxE,KAAM,CAAAvD,UAAU,CAAGyB,UAAU,CAACzB,UAAU,CAExC;AACA,GAAIsD,aAAa,GAAKE,eAAe,EAAIxD,UAAU,CAAG,GAAG,CAAE,CACzDuB,aAAa,CAACiB,OAAO,EAAI,CAAC,CAE1B;AACA,GAAIjB,aAAa,CAACiB,OAAO,EAAI,CAAC,EAAI,CAACd,aAAa,CAAE,CAChDZ,SAAS,uBAAA2B,MAAA,CAAuBrC,gBAAgB,CAACO,WAAW,CAAC,CAACN,IAAI,OAAK,CAAC,CACxE0B,gBAAgB,CAAC3B,gBAAgB,CAACO,WAAW,CAAC,CAACN,IAAI,CAAC,CAEpD;AACAiB,oBAAoB,CAACkB,OAAO,CAAGiB,UAAU,CAAC,IAAM,CAC9CxB,eAAe,CAAC,CAAC,CACjBnB,SAAS,uCAAA2B,MAAA,CAAuCrC,gBAAgB,CAACO,WAAW,CAAC,CAACN,IAAI,CAAE,CAAC,CACrFa,iBAAiB,CAAC,KAAK,CAAC,CACxBK,aAAa,CAACiB,OAAO,CAAG,CAAC,CAC3B,CAAC,CAAE,IAAI,CAAC,CACV,CACF,CAAC,IAAM,CACL;AACAjB,aAAa,CAACiB,OAAO,CAAG,CAAC,CAC3B,CAEA,MAAO,IAAM,CACX,GAAIlB,oBAAoB,CAACkB,OAAO,CAAE,CAChCE,YAAY,CAACpB,oBAAoB,CAACkB,OAAO,CAAC,CAC5C,CACF,CAAC,CACH,CAAC,CAAE,CAACf,UAAU,CAAER,cAAc,CAAEN,WAAW,CAAEe,aAAa,CAAEK,gBAAgB,CAAEE,eAAe,CAAC,CAAC,CAE/F,mBACE5H,KAAA,CAACG,iBAAiB,EAAAkJ,QAAA,eAChBvJ,IAAA,CAACS,UAAU,EAAA8I,QAAA,cACTrJ,KAAA,CAACU,YAAY,EAAA2I,QAAA,eACXrJ,KAAA,CAACY,IAAI,EAAAyI,QAAA,eACHvJ,IAAA,CAACgB,QAAQ,EAAAuI,QAAA,cACPvJ,IAAA,CAACZ,KAAK,EAACoK,IAAI,CAAE,EAAG,CAAE,CAAC,CACX,CAAC,aAEb,EAAM,CAAC,cACPtJ,KAAA,CAACgB,UAAU,EAACuI,OAAO,CAAElD,YAAa,CAAAgD,QAAA,eAChCvJ,IAAA,CAACV,SAAS,EAACkK,IAAI,CAAE,EAAG,CAAE,CAAC,eAEzB,EAAY,CAAC,EACD,CAAC,CACL,CAAC,cAEbtJ,KAAA,CAAC2B,WAAW,EAAA0H,QAAA,eACVvJ,IAAA,QAAK0J,KAAK,CAAE,CAAEC,SAAS,CAAE,QAAQ,CAAEC,YAAY,CAAE,iBAAkB,CAAE,CAAAL,QAAA,cACnErJ,KAAA,CAACyB,WAAW,EAAA4H,QAAA,eACVvJ,IAAA,CAACN,GAAG,EAAC8J,IAAI,CAAE,EAAG,CAAE,CAAC,uBAEnB,EAAa,CAAC,CACX,CAAC,cAENxJ,IAAA,CAACqB,SAAS,EAAAkI,QAAA,CAAC,qBAAmB,CAAW,CAAC,cAC1CvJ,IAAA,CAACwB,YAAY,EAAA+H,QAAA,CAAC,gGAEd,CAAc,CAAC,cAEfrJ,KAAA,CAAC8B,YAAY,EAAAuH,QAAA,eACXrJ,KAAA,CAACgC,aAAa,EAAAqH,QAAA,eACZrJ,KAAA,CAACkC,YAAY,EAAAmH,QAAA,eACXvJ,IAAA,CAACuC,WAAW,EAAAgH,QAAA,cACVvJ,IAAA,CAACX,MAAM,EAACmK,IAAI,CAAE,EAAG,CAAE,CAAC,CACT,CAAC,qBAEhB,EAAc,CAAC,cAEftJ,KAAA,CAAC4F,gBAAgB,EAACE,SAAS,CAAEqB,WAAY,CAAAkC,QAAA,EACtClC,WAAW,cAAGrH,IAAA,CAACJ,IAAI,EAAC4J,IAAI,CAAE,EAAG,CAAE,CAAC,cAAGxJ,IAAA,CAACH,OAAO,EAAC2J,IAAI,CAAE,EAAG,CAAE,CAAC,CACxDnC,WAAW,CAAG,cAAc,CAAG,iBAAiB,EACjC,CAAC,CAElBC,UAAU,eACTpH,KAAA,CAACiF,iBAAiB,EAACE,OAAO,CAAEoC,WAAY,CAACnC,OAAO,CAAEgC,UAAU,CAAChC,OAAQ,CAAAiE,QAAA,eACnErJ,KAAA,CAACqF,cAAc,EAACF,OAAO,CAAEoC,WAAY,CAACnC,OAAO,CAAEgC,UAAU,CAAChC,OAAQ,CAAAiE,QAAA,EAAC,YACvD,CAACjC,UAAU,CAAC0B,IAAI,CACzB1B,UAAU,CAAChC,OAAO,EAAI,aAAa,EACtB,CAAC,cACjBtF,IAAA,CAACyF,aAAa,EAAA8D,QAAA,cACZvJ,IAAA,CAAC2F,cAAc,EAACE,UAAU,CAAEyB,UAAU,CAACzB,UAAW,CAAE,CAAC,CACxC,CAAC,cAChB3F,KAAA,QAAKwJ,KAAK,CAAE,CAAEG,QAAQ,CAAE,UAAU,CAAEC,SAAS,CAAE,KAAK,CAAEC,KAAK,CAAE,uBAAwB,CAAE,CAAAR,QAAA,EAAC,cAC1E,CAACS,IAAI,CAACC,KAAK,CAAC3C,UAAU,CAACzB,UAAU,CAAG,GAAG,CAAC,CAAC,GACrD,CAAC4B,WAAW,EAAIC,UAAU,eACxB1H,IAAA,SAAM0J,KAAK,CAAE,CAAEK,KAAK,CAAE,oBAAoB,CAAEG,UAAU,CAAE,KAAM,CAAE,CAAAX,QAAA,CAAC,4BAEjE,CAAM,CACP,CACAzC,cAAc,EAAI,CAACS,aAAa,eAC/BrH,KAAA,QAAKwJ,KAAK,CAAE,CAAEK,KAAK,CAAE,oBAAoB,CAAED,SAAS,CAAE,KAAM,CAAE,CAAAP,QAAA,EAAC,2CAC/B,CAACtD,gBAAgB,CAACO,WAAW,CAAC,CAACN,IAAI,CAAC,SACpE,EAAK,CACN,EACE,CAAC,EACW,CACpB,CAEA,CAACoB,UAAU,EAAIR,cAAc,eAC5B5G,KAAA,CAACiF,iBAAiB,EAAAoE,QAAA,eAChBrJ,KAAA,CAACqF,cAAc,EAAAgE,QAAA,EAAC,iCACM,CAACtD,gBAAgB,CAACO,WAAW,CAAC,CAACN,IAAI,CAAC,IAC1D,EAAgB,CAAC,cACjBlG,IAAA,QAAK0J,KAAK,CAAE,CAAEG,QAAQ,CAAE,UAAU,CAAEE,KAAK,CAAE,uBAAwB,CAAE,CAAAR,QAAA,CAAC,6EAEtE,CAAK,CAAC,EACW,CACpB,cACDrJ,KAAA,CAACuC,eAAe,EAAA8G,QAAA,eACdvJ,IAAA,CAAC2C,YAAY,EACXwH,GAAG,CAAEjD,SAAU,CACfkD,KAAK,CAAE,KAAM,CACbC,gBAAgB,CAAC,YAAY,CAC7BC,gBAAgB,CAAE,CAChBC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAE,GAAG,CACXC,UAAU,CAAE,MACd,CAAE,CACH,CAAC,cACFzK,IAAA,CAAC6C,gBAAgB,EAACG,WAAW,CAAEuE,aAAc,CAAAgC,QAAA,CAC1ChC,aAAa,cACZrH,KAAA,CAAAE,SAAA,EAAAmJ,QAAA,eACEvJ,IAAA,QAAK0J,KAAK,CAAE,CACVa,KAAK,CAAE,KAAK,CACZC,MAAM,CAAE,KAAK,CACbE,YAAY,CAAE,KAAK,CACnBC,eAAe,CAAE,OAAO,CACxBC,WAAW,CAAE,KACf,CAAE,CAAE,CAAC,YAEP,EAAE,CAAC,cAEH1K,KAAA,CAAAE,SAAA,EAAAmJ,QAAA,eACEvJ,IAAA,CAACN,GAAG,EAAC8J,IAAI,CAAE,EAAG,CAAE,CAAC,QAEnB,EAAE,CACH,CACe,CAAC,EACJ,CAAC,EACL,CAAC,cAEhBtJ,KAAA,CAAC+C,WAAW,EAAAsG,QAAA,eACVrJ,KAAA,CAACkC,YAAY,EAAAmH,QAAA,eACXvJ,IAAA,CAACuC,WAAW,EAAAgH,QAAA,cACVvJ,IAAA,CAACL,MAAM,EAAC6J,IAAI,CAAE,EAAG,CAAE,CAAC,CACT,CAAC,gBAEhB,EAAc,CAAC,cACfxJ,IAAA,CAACmD,YAAY,EACXgF,KAAK,CAAE3B,WAAY,CACnBqE,QAAQ,CAAE7C,gBAAiB,CAC3B8C,QAAQ,CAAEvD,aAAc,CAAAgC,QAAA,CAEvBwB,MAAM,CAACC,IAAI,CAAC/E,gBAAgB,CAAC,CAACgF,GAAG,CAACC,OAAO,eACxClL,IAAA,WAAsBmI,KAAK,CAAE+C,OAAQ,CAAA3B,QAAA,CAClCtD,gBAAgB,CAACiF,OAAO,CAAC,CAAChF,IAAI,EADpBgF,OAEL,CACT,CAAC,CACU,CAAC,cACfhL,KAAA,CAACoD,WAAW,EAAAiG,QAAA,eACVvJ,IAAA,QACEmL,GAAG,CAAElF,gBAAgB,CAACO,WAAW,CAAC,CAACL,GAAI,CACvCiF,GAAG,CAAEnF,gBAAgB,CAACO,WAAW,CAAC,CAACN,IAAK,CACxCmF,OAAO,CAAGC,CAAC,EAAK,CACdA,CAAC,CAACpD,MAAM,CAACwB,KAAK,CAAC6B,OAAO,CAAG,MAAM,CAC/BD,CAAC,CAACpD,MAAM,CAACsD,WAAW,CAAC9B,KAAK,CAAC6B,OAAO,CAAG,MAAM,CAC7C,CAAE,CACH,CAAC,cACFvL,IAAA,QAAK0J,KAAK,CAAE,CAAC6B,OAAO,CAAE,MAAM,CAAE1B,QAAQ,CAAE,MAAM,CAAE,CAAAN,QAAA,CAAC,cAEjD,CAAK,CAAC,EACK,CAAC,cACdvJ,IAAA,CAACwD,QAAQ,EAAA+F,QAAA,CAAEtD,gBAAgB,CAACO,WAAW,CAAC,CAACN,IAAI,CAAW,CAAC,cACzDlG,IAAA,CAAC2D,eAAe,EAAA4F,QAAA,CACbtD,gBAAgB,CAACO,WAAW,CAAC,CAACJ,WAAW,CAC3B,CAAC,EACP,CAAC,EACF,CAAC,cAEfpG,IAAA,CAAC6D,eAAe,EAAA0F,QAAA,cACdvJ,IAAA,CAAC+D,aAAa,EACZE,OAAO,CAAC,SAAS,CACjBwF,OAAO,CAAElC,aAAa,EAAIT,cAAc,CAAGe,aAAa,CAAGF,cAAe,CAAA4B,QAAA,CAEzEhC,aAAa,cACZrH,KAAA,CAAAE,SAAA,EAAAmJ,QAAA,eACEvJ,IAAA,CAACR,MAAM,EAACgK,IAAI,CAAE,EAAG,CAAE,CAAC,iBAEtB,EAAE,CAAC,CACD1C,cAAc,cAChB5G,KAAA,CAAAE,SAAA,EAAAmJ,QAAA,eACEvJ,IAAA,CAACR,MAAM,EAACgK,IAAI,CAAE,EAAG,CAAE,CAAC,qBAEtB,EAAE,CAAC,cAEHtJ,KAAA,CAAAE,SAAA,EAAAmJ,QAAA,eACEvJ,IAAA,CAACT,IAAI,EAACiK,IAAI,CAAE,EAAG,CAAE,CAAC,wBAEpB,EAAE,CACH,CACY,CAAC,CACD,CAAC,CAEjB,CAAC9C,MAAM,EAAIc,eAAe,gBACzBxH,IAAA,CAACkE,aAAa,EAACE,IAAI,CAAE,CAACsC,MAAM,EAAIc,eAAe,EAAEiE,QAAQ,CAAC,OAAO,CAAC,CAAG,OAAO,CAAG,CAAC/E,MAAM,EAAIc,eAAe,EAAEiE,QAAQ,CAAC,SAAS,CAAC,CAAG,SAAS,CAAG,MAAO,CAAAlC,QAAA,CACjJ/B,eAAe,EAAId,MAAM,CACb,CAChB,CAEAM,cAAc,CAAC0E,MAAM,CAAG,CAAC,eACxBxL,KAAA,CAACmE,iBAAiB,EAAAkF,QAAA,eAChBvJ,IAAA,CAACuE,eAAe,EAAAgF,QAAA,CAAC,0BAAwB,CAAiB,CAAC,cAC3DvJ,IAAA,CAACyE,cAAc,EAAA8E,QAAA,CACZvC,cAAc,CAACiE,GAAG,CAAExC,KAAK,eACxBvI,KAAA,CAACyE,aAAa,EAAA4E,QAAA,eACZvJ,IAAA,CAAC6E,cAAc,EAAA0E,QAAA,CAAEd,KAAK,CAACO,IAAI,CAAiB,CAAC,cAC7ChJ,IAAA,CAAC+E,aAAa,EAAAwE,QAAA,CACX,GAAI,CAAAoC,IAAI,CAAClD,KAAK,CAACQ,SAAS,CAAC,CAAC2C,cAAc,CAAC,CAAC,CAC9B,CAAC,cAChB1L,KAAA,CAAC+E,cAAc,EAACwE,OAAO,CAAEA,CAAA,GAAMjB,iBAAiB,CAACC,KAAK,CAAE,CAAAc,QAAA,eACtDvJ,IAAA,CAACP,QAAQ,EAAC+J,IAAI,CAAE,EAAG,CAAE,CAAC,WAExB,EAAgB,CAAC,GARCf,KAAK,CAACoD,EASX,CAChB,CAAC,CACY,CAAC,EACA,CACpB,EACU,CAAC,EACG,CAAC,CAExB,CAAC,CAED,cAAe,CAAAxF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}