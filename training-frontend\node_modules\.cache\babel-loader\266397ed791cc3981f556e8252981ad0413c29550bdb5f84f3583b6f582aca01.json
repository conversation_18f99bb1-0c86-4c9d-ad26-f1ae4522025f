{"ast": null, "code": "import _objectSpread from \"D:/ASL/training-frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"D:/ASL/training-frontend/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"className\"];\n/**\n * @license lucide-react v0.523.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport { forwardRef, createElement } from 'react';\nimport { mergeClasses, toKebabCase, toPascalCase } from './shared/src/utils.js';\nimport Icon from './Icon.js';\nconst createLucideIcon = (iconName, iconNode) => {\n  const Component = forwardRef((_ref, ref) => {\n    let {\n        className\n      } = _ref,\n      props = _objectWithoutProperties(_ref, _excluded);\n    return createElement(Icon, _objectSpread({\n      ref,\n      iconNode,\n      className: mergeClasses(\"lucide-\".concat(toKebabCase(toPascalCase(iconName))), \"lucide-\".concat(iconName), className)\n    }, props));\n  });\n  Component.displayName = toPascalCase(iconName);\n  return Component;\n};\nexport { createLucideIcon as default };", "map": {"version": 3, "names": ["createLucideIcon", "iconName", "iconNode", "Component", "forwardRef", "_ref", "ref", "className", "props", "_objectWithoutProperties", "_excluded", "createElement", "Icon", "_objectSpread", "mergeClasses", "concat", "toKebabCase", "toPascalCase", "displayName"], "sources": ["D:\\ASL\\training-frontend\\node_modules\\lucide-react\\src\\createLucideIcon.ts"], "sourcesContent": ["import { createElement, forwardRef } from 'react';\nimport { mergeClasses, toKebabCase, toPascalCase } from '@lucide/shared';\nimport { IconNode, LucideProps } from './types';\nimport Icon from './Icon';\n\n/**\n * Create a Lucide icon component\n * @param {string} iconName\n * @param {array} iconNode\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst createLucideIcon = (iconName: string, iconNode: IconNode) => {\n  const Component = forwardRef<SVGSVGElement, LucideProps>(({ className, ...props }, ref) =>\n    createElement(Icon, {\n      ref,\n      iconNode,\n      className: mergeClasses(\n        `lucide-${toKebabCase(toPascalCase(iconName))}`,\n        `lucide-${iconName}`,\n        className,\n      ),\n      ...props,\n    }),\n  );\n\n  Component.displayName = toPascalCase(iconName);\n\n  return Component;\n};\n\nexport default createLucideIcon;\n"], "mappings": ";;;;;;;;;;;;;AAWM,MAAAA,gBAAA,GAAmBA,CAACC,QAAA,EAAkBC,QAAuB;EACjE,MAAMC,SAAY,GAAAC,UAAA,CAAuC,CAAAC,IAAA,EAA0BC,GAAA;IAAA,IAAzB;QAAEC;MAAuB,IAAAF,IAAA;MAATG,KAAA,GAAAC,wBAAA,CAAAJ,IAAA,EAAAK,SAAA;IAAA,OACxEC,aAAA,CAAcC,IAAM,EAAAC,aAAA;MAClBP,GAAA;MACAJ,QAAA;MACAK,SAAW,EAAAO,YAAA,WAAAC,MAAA,CACCC,WAAA,CAAYC,YAAa,CAAAhB,QAAQ,CAAC,CAAC,aAAAc,MAAA,CACnCd,QAAQ,GAClBM,SACF;IAAA,GACGC,KAAA,CACJ;EAAA,CACH;EAEUL,SAAA,CAAAe,WAAA,GAAcD,YAAA,CAAahB,QAAQ;EAEtC,OAAAE,SAAA;AACT", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}