{"ast": null, "code": "// 250+ ASL signs for demo purposes\nconst signLanguageData = {\n  \"after\": {\n    name: \"After\",\n    gif: \"https://lifeprint.com/asl101/gifs/a/after-over-across.gif\",\n    description: \"Move your dominant hand over and past your non-dominant hand\"\n  },\n  \"airplane\": {\n    name: \"Airplane\",\n    gif: \"https://www.lifeprint.com/asl101/gifs/a/airplane-flying.gif\",\n    description: \"Extend your hand like a plane and move it through the air\"\n  },\n  // ... (repeat or add more real/placeholder signs)\n  // Example placeholder signs:\n  \"sign3\": {\n    name: \"Sign 3\",\n    gif: \"https://lifeprint.com/asl101/gifs/a/after-over-across.gif\",\n    description: \"Description for sign 3\"\n  },\n  \"sign4\": {\n    name: \"Sign 4\",\n    gif: \"https://lifeprint.com/asl101/gifs/a/after-over-across.gif\",\n    description: \"Description for sign 4\"\n  }\n  // ...\n};\n// Add up to 250+ signs\nfor (let i = 5; i <= 250; i++) {\n  signLanguageData[`sign${i}`] = {\n    name: `Sign ${i}`,\n    gif: \"https://lifeprint.com/asl101/gifs/a/after-over-across.gif\",\n    description: `Description for sign ${i}`\n  };\n}\nexport default signLanguageData;", "map": {"version": 3, "names": ["signLanguageData", "name", "gif", "description", "i"], "sources": ["D:/ASL/training-frontend/src/components/signs.js"], "sourcesContent": ["// 250+ ASL signs for demo purposes\r\nconst signLanguageData = {\r\n  \"after\": { name: \"After\", gif: \"https://lifeprint.com/asl101/gifs/a/after-over-across.gif\", description: \"Move your dominant hand over and past your non-dominant hand\" },\r\n  \"airplane\": { name: \"Airplane\", gif: \"https://www.lifeprint.com/asl101/gifs/a/airplane-flying.gif\", description: \"Extend your hand like a plane and move it through the air\" },\r\n  // ... (repeat or add more real/placeholder signs)\r\n  // Example placeholder signs:\r\n  \"sign3\": { name: \"Sign 3\", gif: \"https://lifeprint.com/asl101/gifs/a/after-over-across.gif\", description: \"Description for sign 3\" },\r\n  \"sign4\": { name: \"Sign 4\", gif: \"https://lifeprint.com/asl101/gifs/a/after-over-across.gif\", description: \"Description for sign 4\" },\r\n  // ...\r\n};\r\n// Add up to 250+ signs\r\nfor (let i = 5; i <= 250; i++) {\r\n  signLanguageData[`sign${i}`] = {\r\n    name: `Sign ${i}`,\r\n    gif: \"https://lifeprint.com/asl101/gifs/a/after-over-across.gif\",\r\n    description: `Description for sign ${i}`\r\n  };\r\n}\r\nexport default signLanguageData; "], "mappings": "AAAA;AACA,MAAMA,gBAAgB,GAAG;EACvB,OAAO,EAAE;IAAEC,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,2DAA2D;IAAEC,WAAW,EAAE;EAA+D,CAAC;EACzK,UAAU,EAAE;IAAEF,IAAI,EAAE,UAAU;IAAEC,GAAG,EAAE,6DAA6D;IAAEC,WAAW,EAAE;EAA4D,CAAC;EAC9K;EACA;EACA,OAAO,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,2DAA2D;IAAEC,WAAW,EAAE;EAAyB,CAAC;EACpI,OAAO,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,2DAA2D;IAAEC,WAAW,EAAE;EAAyB;EACnI;AACF,CAAC;AACD;AACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,GAAG,EAAEA,CAAC,EAAE,EAAE;EAC7BJ,gBAAgB,CAAC,OAAOI,CAAC,EAAE,CAAC,GAAG;IAC7BH,IAAI,EAAE,QAAQG,CAAC,EAAE;IACjBF,GAAG,EAAE,2DAA2D;IAChEC,WAAW,EAAE,wBAAwBC,CAAC;EACxC,CAAC;AACH;AACA,eAAeJ,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}