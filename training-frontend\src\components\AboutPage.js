import React from 'react';
import styled from 'styled-components';
import { 
  <PERSON>, 
  Users, 
  Award, 
  TrendingUp, 
  ArrowLeft,
  Cpu,
  Eye,
  Globe,
  Zap
} from 'lucide-react';

const AboutContainer = styled.div`
  min-height: 100vh;
  background: var(--bg-primary);
  position: relative;
  overflow-x: hidden;
  
  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
      radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.03) 0%, transparent 50%);
    pointer-events: none;
    z-index: 0;
  }
`;

const Navigation = styled.nav`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-neural);
  padding: var(--space-4) 0;
  transition: var(--transition-normal);
`;

const NavContainer = styled.div`
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--space-6);
  display: flex;
  align-items: center;
  justify-content: space-between;
  
  @media (max-width: 768px) {
    padding: 0 var(--space-4);
  }
`;

const Logo = styled.div`
  font-family: var(--font-primary);
  font-size: 1.5rem;
  font-weight: 700;
  background: var(--text-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: flex;
  align-items: center;
  gap: var(--space-3);
  
  @media (max-width: 768px) {
    font-size: 1.25rem;
    gap: var(--space-2);
  }
`;

const LogoIcon = styled.div`
  width: 40px;
  height: 40px;
  background: var(--bg-neural);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: var(--shadow-neural);
  
  @media (max-width: 768px) {
    width: 36px;
    height: 36px;
  }
`;

const BackButton = styled.button`
  background: var(--bg-glass);
  color: var(--text-secondary);
  border: 1px solid var(--border-neural);
  padding: var(--space-3) var(--space-5);
  border-radius: var(--radius-xl);
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-normal);
  display: flex;
  align-items: center;
  gap: var(--space-2);
  backdrop-filter: blur(10px);
  
  &:hover {
    background: var(--primary-50);
    color: var(--primary-600);
    border-color: var(--primary-300);
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
  }
`;

const MainContent = styled.main`
  padding: var(--space-24) var(--space-6) var(--space-20);
  max-width: 1400px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
  
  @media (max-width: 768px) {
    padding: var(--space-20) var(--space-4) var(--space-16);
  }
`;

const PageTitle = styled.h1`
  font-family: var(--font-primary);
  font-size: 3rem;
  font-weight: 700;
  background: var(--text-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-align: center;
  margin-bottom: var(--space-4);
  letter-spacing: -0.02em;
  
  @media (max-width: 768px) {
    font-size: 2.25rem;
  }
`;

const PageSubtitle = styled.p`
  font-size: 1.25rem;
  color: var(--text-secondary);
  text-align: center;
  margin-bottom: var(--space-16);
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
  
  @media (max-width: 768px) {
    font-size: 1.125rem;
    margin-bottom: var(--space-12);
  }
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-8);
  margin-bottom: var(--space-20);
  
  @media (max-width: 768px) {
    gap: var(--space-6);
    margin-bottom: var(--space-16);
  }
`;

const StatCard = styled.div`
  background: var(--bg-glass);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  border: 1px solid var(--border-neural);
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-lg);
  text-align: center;
  transition: var(--transition-normal);
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl), var(--shadow-glow);
    border-color: var(--primary-300);
  }
`;

const StatIcon = styled.div`
  width: 64px;
  height: 64px;
  background: var(--bg-neural);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--space-4);
  box-shadow: var(--shadow-neural);
`;

const StatNumber = styled.div`
  font-size: 2rem;
  font-weight: 700;
  background: var(--text-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: var(--space-2);
  font-family: var(--font-primary);
`;

const StatLabel = styled.div`
  font-size: 1rem;
  color: var(--text-secondary);
  font-weight: 500;
`;

const SectionTitle = styled.h2`
  font-family: var(--font-primary);
  font-size: 2rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-6);
  text-align: center;

  @media (max-width: 768px) {
    font-size: 1.75rem;
  }
`;

const AboutPage = ({ onBackToHome }) => {
  return (
    <AboutContainer>
      <Navigation>
        <NavContainer>
          <Logo>
            <LogoIcon>
              <Brain size={24} />
            </LogoIcon>
            ASL Neural
          </Logo>
          <BackButton onClick={onBackToHome}>
            <ArrowLeft size={18} />
            Back to Home
          </BackButton>
        </NavContainer>
      </Navigation>

      <MainContent>
        <PageTitle>About ASL Neural</PageTitle>
        <PageSubtitle>
          Pioneering the future of accessibility through advanced artificial intelligence 
          and computer vision technology for sign language education
        </PageSubtitle>

        <StatsGrid>
          <StatCard>
            <StatIcon>
              <Users size={28} color="white" />
            </StatIcon>
            <StatNumber>100K+</StatNumber>
            <StatLabel>Trained on videos</StatLabel>
          </StatCard>

          <StatCard>
            <StatIcon>
              <Cpu size={28} color="white" />
            </StatIcon>
            <StatNumber>2.5M</StatNumber>
            <StatLabel>Neural Patterns</StatLabel>
          </StatCard>

          <StatCard>
            <StatIcon>
              <Award size={28} color="white" />
            </StatIcon>
            <StatNumber>+88.7%</StatNumber>
            <StatLabel>AI Accuracy</StatLabel>
          </StatCard>

          <StatCard>
            <StatIcon>
              <TrendingUp size={28} color="white" />
            </StatIcon>
            <StatNumber>15k+</StatNumber>
            <StatLabel>Users</StatLabel>
          </StatCard>
        </StatsGrid>
      </MainContent>
    </AboutContainer>
  );
};

export default AboutPage;
