{"ast": null, "code": "var _jsxFileName = \"D:\\\\ASL\\\\training-frontend\\\\src\\\\components\\\\TrainingPage.js\",\n  _s = $RefreshSig$();\nimport { useState, useRef, useCallback, useEffect } from 'react';\nimport styled from 'styled-components';\nimport Webcam from 'react-webcam';\nimport { Brain, Camera, ArrowLeft, Play, Square, Download, Zap, Eye, Target, Wifi, WifiOff } from 'lucide-react';\nimport { useSignDetection } from '../hooks/useSignDetection';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst TrainingContainer = styled.div`\n  min-height: 100vh;\n  background: var(--bg-primary);\n  position: relative;\n  overflow-x: hidden;\n\n  &::before {\n    content: '';\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background:\n      radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),\n      radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.03) 0%, transparent 50%);\n    pointer-events: none;\n    z-index: 0;\n  }\n`;\n_c = TrainingContainer;\nconst Navigation = styled.nav`\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 50;\n  background: var(--bg-glass);\n  backdrop-filter: blur(20px);\n  border-bottom: 1px solid var(--border-neural);\n  padding: var(--space-4) 0;\n  transition: var(--transition-normal);\n`;\n_c2 = Navigation;\nconst NavContainer = styled.div`\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 0 var(--space-6);\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n\n  @media (max-width: 768px) {\n    padding: 0 var(--space-4);\n  }\n`;\n_c3 = NavContainer;\nconst Logo = styled.div`\n  font-family: var(--font-primary);\n  font-size: 1.5rem;\n  font-weight: 700;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n\n  @media (max-width: 768px) {\n    font-size: 1.25rem;\n    gap: var(--space-2);\n  }\n`;\n_c4 = Logo;\nconst LogoIcon = styled.div`\n  width: 40px;\n  height: 40px;\n  background: var(--bg-neural);\n  border-radius: var(--radius-lg);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  box-shadow: var(--shadow-neural);\n\n  @media (max-width: 768px) {\n    width: 36px;\n    height: 36px;\n  }\n`;\n_c5 = LogoIcon;\nconst BackButton = styled.button`\n  background: var(--bg-glass);\n  color: var(--text-secondary);\n  border: 1px solid var(--border-neural);\n  padding: var(--space-3) var(--space-5);\n  border-radius: var(--radius-xl);\n  font-size: 0.9rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: var(--transition-normal);\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  backdrop-filter: blur(10px);\n\n  &:hover {\n    background: var(--primary-50);\n    color: var(--primary-600);\n    border-color: var(--primary-300);\n    transform: translateY(-1px);\n    box-shadow: var(--shadow-lg);\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-2) var(--space-4);\n    font-size: 0.85rem;\n  }\n`;\n_c6 = BackButton;\nconst PageTitle = styled.h1`\n  font-family: var(--font-primary);\n  font-size: 2.5rem;\n  font-weight: 700;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  text-align: center;\n  margin-bottom: var(--space-4);\n  letter-spacing: -0.02em;\n\n  @media (max-width: 768px) {\n    font-size: 2rem;\n  }\n`;\n_c7 = PageTitle;\nconst PageSubtitle = styled.p`\n  font-size: 1.125rem;\n  color: var(--text-secondary);\n  text-align: center;\n  margin-bottom: var(--space-16);\n  max-width: 700px;\n  margin-left: auto;\n  margin-right: auto;\n  line-height: 1.6;\n\n  @media (max-width: 768px) {\n    margin-bottom: var(--space-12);\n    font-size: 1rem;\n  }\n`;\n_c8 = PageSubtitle;\nconst StatusBadge = styled.div`\n  display: inline-flex;\n  align-items: center;\n  gap: var(--space-2);\n  background: var(--bg-glass);\n  border: 1px solid var(--border-neural);\n  border-radius: var(--radius-full);\n  padding: var(--space-2) var(--space-4);\n  margin-bottom: var(--space-8);\n  font-size: 0.875rem;\n  font-weight: 500;\n  color: var(--text-accent);\n  backdrop-filter: blur(10px);\n  box-shadow: var(--shadow-glow);\n\n  @media (max-width: 768px) {\n    font-size: 0.8rem;\n    padding: var(--space-2) var(--space-3);\n  }\n`;\n_c9 = StatusBadge;\nconst MainContent = styled.main`\n  padding: var(--space-20) var(--space-4) var(--space-16);\n  max-width: 1200px;\n  margin: 0 auto;\n\n  @media (max-width: 768px) {\n    padding: var(--space-12) var(--space-3) var(--space-8);\n    max-width: 100%;\n  }\n`;\n_c0 = MainContent;\nconst TrainingGrid = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: var(--space-8);\n  max-width: 1200px;\n  margin: 0 auto var(--space-12);\n\n  @media (max-width: 1024px) {\n    grid-template-columns: 1fr;\n    gap: var(--space-4);\n  }\n\n  @media (max-width: 768px) {\n    gap: var(--space-3);\n    margin: 0 auto var(--space-6);\n  }\n`;\n_c1 = TrainingGrid;\nconst CameraSection = styled.div`\n  background: var(--bg-glass);\n  border-radius: var(--radius-2xl);\n  padding: var(--space-10);\n  border: 1px solid var(--border-neural);\n  backdrop-filter: blur(20px);\n  box-shadow: var(--shadow-lg);\n  transition: var(--transition-normal);\n  position: relative;\n  overflow: hidden;\n\n  @media (max-width: 768px) {\n    padding: var(--space-6);\n    border-radius: var(--radius-xl);\n  }\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 4px;\n    background: var(--bg-neural);\n    transform: scaleX(0);\n    transition: var(--transition-normal);\n  }\n\n  &:hover {\n    box-shadow: var(--shadow-xl), var(--shadow-glow);\n    border-color: var(--primary-300);\n\n    &::before {\n      transform: scaleX(1);\n    }\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-8);\n  }\n`;\n_c10 = CameraSection;\nconst SectionTitle = styled.h2`\n  font-family: var(--font-primary);\n  font-size: 1.25rem;\n  margin-bottom: var(--space-6);\n  color: var(--text-primary);\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n\n  @media (max-width: 768px) {\n    font-size: 1.125rem;\n    margin-bottom: var(--space-4);\n  }\n`;\n_c11 = SectionTitle;\nconst SectionIcon = styled.div`\n  width: 36px;\n  height: 36px;\n  background: var(--bg-neural);\n  border-radius: var(--radius-lg);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  box-shadow: var(--shadow-neural);\n\n  @media (max-width: 768px) {\n    width: 32px;\n    height: 32px;\n  }\n`;\n_c12 = SectionIcon;\nconst WebcamContainer = styled.div`\n  position: relative;\n  border-radius: var(--radius-2xl);\n  overflow: hidden;\n  background: var(--neural-100);\n  aspect-ratio: 4/3;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: 3px solid var(--border-neural);\n  margin-bottom: var(--space-6);\n  box-shadow: var(--shadow-lg);\n`;\n_c13 = WebcamContainer;\nconst StyledWebcam = styled(Webcam)`\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n`;\n_c14 = StyledWebcam;\nconst RecordingOverlay = styled.div`\n  position: absolute;\n  top: var(--space-4);\n  right: var(--space-4);\n  background: ${props => props.isRecording ? 'var(--error-500)' : 'var(--neural-600)'};\n  color: white;\n  padding: var(--space-3) var(--space-5);\n  border-radius: var(--radius-full);\n  font-size: 0.9rem;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  box-shadow: var(--shadow-lg);\n  animation: ${props => props.isRecording ? 'pulse 1.5s infinite' : 'none'};\n\n  @keyframes pulse {\n    0%, 100% { opacity: 1; transform: scale(1); }\n    50% { opacity: 0.8; transform: scale(1.05); }\n  }\n`;\n_c15 = RecordingOverlay;\nconst SignSection = styled.div`\n  background: var(--bg-primary);\n  border-radius: var(--radius-2xl);\n  padding: var(--space-8);\n  border: 1px solid var(--border-light);\n  box-shadow: var(--shadow-lg);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  transition: all 0.3s ease;\n\n  &:hover {\n    box-shadow: var(--shadow-xl);\n    border-color: var(--primary-200);\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-6);\n  }\n`;\n_c16 = SignSection;\nconst SignSelector = styled.select`\n  width: 100%;\n  padding: var(--space-3) var(--space-4);\n  border: 2px solid var(--border-light);\n  border-radius: var(--radius-lg);\n  background: var(--bg-primary);\n  color: var(--text-primary);\n  font-size: 1rem;\n  font-weight: 500;\n  margin-bottom: var(--space-4);\n  cursor: pointer;\n  transition: var(--transition-normal);\n\n  &:focus {\n    outline: none;\n    border-color: var(--primary-500);\n    box-shadow: 0 0 0 3px var(--primary-100);\n  }\n\n  &:hover {\n    border-color: var(--primary-300);\n  }\n\n  option {\n    padding: var(--space-2);\n    background: var(--bg-primary);\n    color: var(--text-primary);\n  }\n`;\n_c17 = SignSelector;\nconst SignDisplay = styled.div`\n  width: 300px;\n  height: 300px;\n  background: var(--primary-50);\n  border-radius: var(--radius-2xl);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: var(--space-6);\n  border: 2px solid var(--primary-200);\n  transition: all 0.3s ease;\n  overflow: hidden;\n\n  img {\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n    border-radius: var(--radius-xl);\n  }\n\n  &:hover {\n    transform: scale(1.02);\n    border-color: var(--primary-300);\n  }\n\n  @media (max-width: 768px) {\n    width: 250px;\n    height: 250px;\n  }\n`;\n_c18 = SignDisplay;\nconst SignName = styled.h3`\n  font-family: var(--font-primary);\n  font-size: 1.5rem;\n  margin-bottom: var(--space-3);\n  color: var(--text-primary);\n  font-weight: 700;\n\n  @media (max-width: 768px) {\n    font-size: 1.25rem;\n  }\n`;\n_c19 = SignName;\nconst SignDescription = styled.p`\n  text-align: center;\n  line-height: 1.6;\n  color: var(--text-secondary);\n  font-size: 0.9rem;\n  font-weight: 400;\n  max-width: 280px;\n`;\n_c20 = SignDescription;\nconst ControlsSection = styled.div`\n  display: flex;\n  justify-content: center;\n  gap: var(--space-4);\n  margin-top: var(--space-8);\n\n  @media (max-width: 768px) {\n    flex-direction: column;\n    align-items: center;\n    gap: var(--space-3);\n  }\n`;\n_c21 = ControlsSection;\nconst ControlButton = styled.button`\n  background: ${props => props.variant === 'primary' ? 'var(--primary-600)' : 'var(--bg-primary)'};\n  border: ${props => props.variant === 'primary' ? 'none' : '1px solid var(--border-medium)'};\n  color: ${props => props.variant === 'primary' ? 'white' : 'var(--text-primary)'};\n  padding: var(--space-3) var(--space-6);\n  border-radius: var(--radius-lg);\n  cursor: pointer;\n  font-size: 0.9rem;\n  font-weight: 600;\n  transition: all 0.2s ease;\n  min-width: 160px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: var(--space-2);\n  box-shadow: ${props => props.variant === 'primary' ? 'var(--shadow-lg)' : 'var(--shadow-sm)'};\n\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: ${props => props.variant === 'primary' ? 'var(--shadow-xl)' : 'var(--shadow-md)'};\n    background: ${props => props.variant === 'primary' ? 'var(--primary-700)' : 'var(--gray-50)'};\n  }\n\n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n    transform: none;\n  }\n\n  @media (max-width: 768px) {\n    width: 100%;\n    max-width: 280px;\n  }\n`;\n_c22 = ControlButton;\nconst StatusMessage = styled.div`\n  text-align: center;\n  margin-top: var(--space-6);\n  padding: var(--space-4) var(--space-6);\n  border-radius: var(--radius-lg);\n  background: ${props => props.type === 'success' ? 'var(--success-500)' : props.type === 'error' ? 'var(--error-500)' : 'var(--primary-600)'};\n  color: white;\n  font-weight: 500;\n  font-size: 0.875rem;\n  max-width: 400px;\n  margin-left: auto;\n  margin-right: auto;\n`;\n_c23 = StatusMessage;\nconst RecordingsSection = styled.div`\n  margin-top: var(--space-16);\n  background: var(--bg-secondary);\n  padding: var(--space-12) var(--space-4);\n  border-radius: var(--radius-2xl);\n  max-width: 1200px;\n  margin-left: auto;\n  margin-right: auto;\n`;\n_c24 = RecordingsSection;\nconst RecordingsTitle = styled.h3`\n  font-family: var(--font-primary);\n  color: var(--text-primary);\n  margin-bottom: var(--space-8);\n  font-size: 1.5rem;\n  font-weight: 600;\n  text-align: center;\n`;\n_c25 = RecordingsTitle;\nconst RecordingsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n  gap: var(--space-6);\n\n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n    gap: var(--space-4);\n  }\n`;\n_c26 = RecordingsGrid;\nconst RecordingCard = styled.div`\n  background: var(--bg-primary);\n  padding: var(--space-6);\n  border-radius: var(--radius-xl);\n  border: 1px solid var(--border-light);\n  box-shadow: var(--shadow-sm);\n  transition: all 0.3s ease;\n\n  &:hover {\n    transform: translateY(-2px);\n    border-color: var(--primary-200);\n    box-shadow: var(--shadow-lg);\n  }\n`;\n_c27 = RecordingCard;\nconst RecordingTitle = styled.p`\n  margin: 0 0 var(--space-2) 0;\n  color: var(--text-primary);\n  font-weight: 600;\n  font-size: 1rem;\n  font-family: var(--font-primary);\n`;\n_c28 = RecordingTitle;\nconst RecordingTime = styled.p`\n  margin: 0 0 var(--space-4) 0;\n  font-size: 0.8rem;\n  color: var(--text-tertiary);\n`;\n_c29 = RecordingTime;\nconst DownloadButton = styled.button`\n  background: var(--primary-600);\n  border: none;\n  border-radius: var(--radius-lg);\n  padding: var(--space-2) var(--space-4);\n  color: white;\n  cursor: pointer;\n  font-size: 0.8rem;\n  font-weight: 500;\n  transition: all 0.2s ease;\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  margin: 0 auto;\n\n  &:hover {\n    background: var(--primary-700);\n    transform: translateY(-1px);\n  }\n`;\n_c30 = DownloadButton;\nconst PredictionDisplay = styled.div`\n  background: var(--bg-glass);\n  border: 2px solid ${props => {\n  if (props.matched) return 'var(--success-400)';\n  if (props.isStale) return 'var(--warning-300)';\n  return 'var(--border-light)';\n}};\n  border-radius: var(--radius-xl);\n  padding: var(--space-4);\n  margin-bottom: var(--space-4);\n  text-align: center;\n  transition: var(--transition-normal);\n  backdrop-filter: blur(10px);\n  opacity: ${props => props.isStale ? 0.7 : 1};\n  min-height: 80px;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n\n  ${props => props.matched && `\n    background: var(--success-50);\n    box-shadow: 0 0 20px var(--success-200);\n    animation: pulse 1s ease-in-out;\n  `}\n\n  ${props => props.isStale && `\n    background: var(--warning-50);\n  `}\n\n  @keyframes pulse {\n    0%, 100% { transform: scale(1); }\n    50% { transform: scale(1.02); }\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-3);\n    margin-bottom: var(--space-3);\n    min-height: 70px;\n  }\n`;\n_c31 = PredictionDisplay;\nconst PredictionText = styled.div`\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: ${props => {\n  if (props.matched) return 'var(--success-700)';\n  if (props.isStale) return 'var(--warning-700)';\n  return 'var(--text-primary)';\n}};\n  margin-bottom: var(--space-2);\n\n  @media (max-width: 768px) {\n    font-size: 1.125rem;\n  }\n`;\n_c32 = PredictionText;\nconst ConfidenceBar = styled.div`\n  width: 100%;\n  height: 8px;\n  background: var(--bg-secondary);\n  border-radius: var(--radius-full);\n  overflow: hidden;\n  margin-top: var(--space-2);\n`;\n_c33 = ConfidenceBar;\nconst ConfidenceFill = styled.div`\n  height: 100%;\n  background: ${props => {\n  if (props.confidence > 0.8) return 'var(--success-500)';\n  if (props.confidence > 0.6) return 'var(--warning-500)';\n  return 'var(--error-500)';\n}};\n  width: ${props => props.confidence * 100}%;\n  transition: width 0.3s ease;\n`;\n_c34 = ConfidenceFill;\nconst ConnectionStatus = styled.div`\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  padding: var(--space-2) var(--space-3);\n  border-radius: var(--radius-lg);\n  font-size: 0.875rem;\n  font-weight: 500;\n  background: ${props => props.connected ? 'var(--success-50)' : 'var(--error-50)'};\n  color: ${props => props.connected ? 'var(--success-700)' : 'var(--error-700)'};\n  border: 1px solid ${props => props.connected ? 'var(--success-200)' : 'var(--error-200)'};\n`;\n\n// Sign language data with GIFs (matching Streamlit app)\n_c35 = ConnectionStatus;\nconst signLanguageData = {\n  \"after\": {\n    name: \"After\",\n    gif: \"https://lifeprint.com/asl101/gifs/a/after-over-across.gif\",\n    description: \"Move your dominant hand over and past your non-dominant hand\"\n  },\n  \"airplane\": {\n    name: \"Airplane\",\n    gif: \"https://www.lifeprint.com/asl101/gifs/a/airplane-flying.gif\",\n    description: \"Extend your hand like a plane and move it through the air\"\n  },\n  \"all\": {\n    name: \"All\",\n    gif: \"https://lifeprint.com/asl101/gifs/a/all-whole.gif\",\n    description: \"Circle your dominant hand around your non-dominant hand\"\n  },\n  \"alligator\": {\n    name: \"Alligator\",\n    gif: \"https://lifeprint.com/asl101/gifs/a/alligator.gif\",\n    description: \"Clap your hands together like an alligator's mouth\"\n  },\n  \"animal\": {\n    name: \"Animal\",\n    gif: \"https://www.lifeprint.com/asl101/gifs-animated/animal.gif\",\n    description: \"Place fingertips on chest and move hands back and forth\"\n  },\n  \"any\": {\n    name: \"Any\",\n    gif: \"https://lifeprint.com/asl101/gifs/a/any.gif\",\n    description: \"Point with index finger and twist your wrist\"\n  },\n  \"apple\": {\n    name: \"Apple\",\n    gif: \"https://media.giphy.com/media/l0HlHb4dtZZiMYEA8/giphy.gif\",\n    description: \"Twist your knuckle against your cheek\"\n  },\n  \"arm\": {\n    name: \"Arm\",\n    gif: \"https://th.bing.com/th/id/OIP.KkJLqfbp6XEHiIe-jOUb2AHaEc?w=251&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Pat your arm with your opposite hand\"\n  },\n  \"aunt\": {\n    name: \"Aunt\",\n    gif: \"https://th.bing.com/th/id/OIP.Yz5UUZdNTrVWXf72we_N6wHaHa?rs=1&pid=ImgDetMain\",\n    description: \"Make an 'A' handshape near your cheek and shake it\"\n  },\n  \"baby\": {\n    name: \"Baby\",\n    gif: \"https://media.giphy.com/media/l0HlHb4dtZZiMYEA8/giphy.gif\",\n    description: \"Rock your arms as if holding a baby\"\n  },\n  \"ball\": {\n    name: \"Ball\",\n    gif: \"https://lifeprint.com/asl101/gifs/b/ball.gif\",\n    description: \"Cup your hands as if holding a ball\"\n  },\n  \"banana\": {\n    name: \"Banana\",\n    gif: \"https://media.giphy.com/media/l0HlHb4dtZZiMYEA8/giphy.gif\",\n    description: \"Peel an imaginary banana with your fingers\"\n  },\n  \"bear\": {\n    name: \"Bear\",\n    gif: \"https://lifeprint.com/asl101/gifs/b/bear.gif\",\n    description: \"Cross your arms and scratch like a bear\"\n  },\n  \"beautiful\": {\n    name: \"Beautiful\",\n    gif: \"https://media.giphy.com/media/l0HlHb4dtZZiMYEA8/giphy.gif\",\n    description: \"Circle your face with your hand and close it into a fist\"\n  },\n  \"bed\": {\n    name: \"Bed\",\n    gif: \"https://lifeprint.com/asl101/gifs/b/bed.gif\",\n    description: \"Rest your head on your hands as if sleeping\"\n  },\n  \"bee\": {\n    name: \"Bee\",\n    gif: \"https://lifeprint.com/asl101/gifs/b/bee.gif\",\n    description: \"Pinch your cheek and brush away as if swatting a bee\"\n  },\n  \"bird\": {\n    name: \"Bird\",\n    gif: \"https://lifeprint.com/asl101/gifs/b/bird.gif\",\n    description: \"Pinch your fingers together near your mouth like a beak\"\n  },\n  \"black\": {\n    name: \"Black\",\n    gif: \"https://lifeprint.com/asl101/gifs/b/black.gif\",\n    description: \"Draw your index finger across your forehead\"\n  },\n  \"blue\": {\n    name: \"Blue\",\n    gif: \"https://lifeprint.com/asl101/gifs/b/blue.gif\",\n    description: \"Shake a 'B' handshape\"\n  },\n  \"book\": {\n    name: \"Book\",\n    gif: \"https://lifeprint.com/asl101/gifs/b/book.gif\",\n    description: \"Open your hands like opening a book\"\n  },\n  \"boy\": {\n    name: \"Boy\",\n    gif: \"https://lifeprint.com/asl101/gifs/b/boy.gif\",\n    description: \"Snap your fingers at your forehead\"\n  },\n  \"brother\": {\n    name: \"Brother\",\n    gif: \"https://lifeprint.com/asl101/gifs/b/brother.gif\",\n    description: \"Make an 'L' shape and point to your forehead, then point forward\"\n  },\n  \"brown\": {\n    name: \"Brown\",\n    gif: \"https://lifeprint.com/asl101/gifs/b/brown.gif\",\n    description: \"Slide your index finger down your cheek\"\n  },\n  \"bug\": {\n    name: \"Bug\",\n    gif: \"https://lifeprint.com/asl101/gifs/b/bug.gif\",\n    description: \"Pinch your nose with your thumb and index finger\"\n  },\n  \"butterfly\": {\n    name: \"Butterfly\",\n    gif: \"https://lifeprint.com/asl101/gifs/b/butterfly.gif\",\n    description: \"Cross your thumbs and flutter your fingers like wings\"\n  },\n  \"car\": {\n    name: \"Car\",\n    gif: \"https://lifeprint.com/asl101/gifs/c/car.gif\",\n    description: \"Pretend to steer a car with both hands\"\n  },\n  \"cat\": {\n    name: \"Cat\",\n    gif: \"https://lifeprint.com/asl101/gifs/c/cat.gif\",\n    description: \"Pinch your cheek and pull out like whiskers\"\n  },\n  \"chair\": {\n    name: \"Chair\",\n    gif: \"https://lifeprint.com/asl101/gifs/c/chair.gif\",\n    description: \"Tap your fingers on your other hand like sitting\"\n  },\n  \"clean\": {\n    name: \"Clean\",\n    gif: \"https://lifeprint.com/asl101/gifs/c/clean.gif\",\n    description: \"Wipe one palm with the other\"\n  },\n  \"cold\": {\n    name: \"Cold\",\n    gif: \"https://media.giphy.com/media/l0HlHb4dtZZiMYEA8/giphy.gif\",\n    description: \"Shiver with both hands in fists\"\n  },\n  \"cow\": {\n    name: \"Cow\",\n    gif: \"https://lifeprint.com/asl101/gifs/c/cow.gif\",\n    description: \"Twist your thumb at your temple like a horn\"\n  },\n  \"cry\": {\n    name: \"Cry\",\n    gif: \"https://lifeprint.com/asl101/gifs/c/cry.gif\",\n    description: \"Draw tears down your cheeks with your index fingers\"\n  },\n  \"cute\": {\n    name: \"Cute\",\n    gif: \"https://lifeprint.com/asl101/gifs/c/cute-sugar.gif\",\n    description: \"Brush your chin with your fingers\"\n  },\n  \"dad\": {\n    name: \"Dad\",\n    gif: \"https://media.giphy.com/media/l0MYQcLKwtl5v6H1S/giphy.gif\",\n    description: \"Tap your forehead with your thumb\"\n  },\n  \"dance\": {\n    name: \"Dance\",\n    gif: \"https://www.bing.com/th/id/OGC.********************************?pid=1.7&rurl=https%3a%2f%2fmedia.giphy.com%2fmedia%2f3o7TKMspYQjQTbOz2U%2fgiphy.gif&ehk=h%2bdBHCxuoOT89ovSy5uTk6MCL9acaBEV6ld9lrVDRF4%3d\",\n    description: \"Swing your fingers over your palm like dancing\"\n  },\n  \"dirty\": {\n    name: \"Dirty\",\n    gif: \"https://th.bing.com/th/id/OIP.wRA7r1OPPUuEoLL4Hds9jAHaHa?rs=1&pid=ImgDetMain\",\n    description: \"Wiggle your fingers under your chin\"\n  },\n  \"dog\": {\n    name: \"Dog\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=uVshGsOHXgldoQ&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fd%2fdog1.gif&ehk=Xnfrvg0xwy1u%2fae9vWdKYMT25%2bF6qoteW2FThCbVrOA%3d&risl=&pid=ImgRaw&r=0\",\n    description: \"Pat your leg and snap your fingers\"\n  },\n  \"eat\": {\n    name: \"Eat\",\n    gif: \"https://lifeprint.com/asl101/gifs/e/eat.gif\",\n    description: \"Bring your fingers to your mouth as if eating\"\n  },\n  \"elephant\": {\n    name: \"Elephant\",\n    gif: \"https://lifeprint.com/asl101/gifs/e/elephant.gif\",\n    description: \"Trace the shape of an elephant's trunk with your hand\"\n  },\n  \"fish\": {\n    name: \"Fish\",\n    gif: \"https://lifeprint.com/asl101/gifs/f/fish.gif\",\n    description: \"Move your hand like a fish swimming\"\n  },\n  \"flower\": {\n    name: \"Flower\",\n    gif: \"https://lifeprint.com/asl101/gifs/f/flower.gif\",\n    description: \"Touch your nose with your fingertips\"\n  },\n  \"friend\": {\n    name: \"Friend\",\n    gif: \"https://lifeprint.com/asl101/gifs/f/friend.gif\",\n    description: \"Hook your index fingers together\"\n  },\n  \"girl\": {\n    name: \"Girl\",\n    gif: \"https://lifeprint.com/asl101/gifs/g/girl.gif\",\n    description: \"Trace your jawline with your thumb\"\n  },\n  \"go\": {\n    name: \"Go\",\n    gif: \"https://lifeprint.com/asl101/gifs/g/go.gif\",\n    description: \"Point both index fingers forward and bend them\"\n  },\n  \"good\": {\n    name: \"Good\",\n    gif: \"https://lifeprint.com/asl101/gifs/g/good.gif\",\n    description: \"Touch your chin and move your hand forward\"\n  },\n  \"green\": {\n    name: \"Green\",\n    gif: \"https://lifeprint.com/asl101/gifs/g/green.gif\",\n    description: \"Shake a 'G' handshape\"\n  },\n  \"hair\": {\n    name: \"Hair\",\n    gif: \"https://www.lifeprint.com/asl101/gifs/h/hair-g-version.gif\",\n    description: \"Pinch a strand of your hair\"\n  },\n  \"happy\": {\n    name: \"Happy\",\n    gif: \"https://media0.giphy.com/media/3o7TKFpahYpUp4g0N2/giphy.gif?cid=790b7611bca188a92e2e759876756ef62ba95b7cdd337c77&rid=giphy.gif&ct=g\",\n    description: \"Brush your chest upward with both hands\"\n  },\n  \"hello\": {\n    name: \"Hello\",\n    gif: \"https://media.giphy.com/media/3o7TKNKOfKlIhbD3gY/giphy.gif\",\n    description: \"Wave your hand from side to side with palm facing forward\"\n  },\n  \"home\": {\n    name: \"Home\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=%2bnBd%2foQjxnoPfg&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fh%2fhome-2.gif&ehk=7yD%2f%2fh6JN1Y4D4BOrUjgKW4Jccy2Y4GVYLf%2fzyk%2b5YY%3d&risl=&pid=ImgRaw&r=0\",\n    description: \"Touch your mouth then your cheek\"\n  },\n  \"horse\": {\n    name: \"Horse\",\n    gif: \"https://media.giphy.com/media/l0HlM5HffraiQaHUk/giphy.gif\",\n    description: \"Extend your thumb and fingers at your temple like ears\"\n  },\n  \"hot\": {\n    name: \"Hot\",\n    gif: \"https://media.giphy.com/media/3o6Zt99k5aDok347bG/giphy.gif\",\n    description: \"Touch your mouth and quickly move your hand away\"\n  },\n  \"hungry\": {\n    name: \"Hungry\",\n    gif: \"https://media.giphy.com/media/l3vR0xkdFEz4tnfTq/giphy.gif\",\n    description: \"Move your hand down your chest like food going down\"\n  },\n  \"jump\": {\n    name: \"Jump\",\n    gif: \"https://lifeprint.com/asl101/gifs-animated/jump.gif\",\n    description: \"Bounce your fingers on your palm\"\n  },\n  \"like\": {\n    name: \"Like\",\n    gif: \"https://lifeprint.com/asl101/gifs/l/like.gif\",\n    description: \"Pull your thumb and middle finger from your chest\"\n  },\n  \"look\": {\n    name: \"Look\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=pYhzip7LqNs7qw&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fl%2flook-at-1.gif&ehk=rFJ7dBrMGFDK0nHLzrOPAzROVE7yqyDEcb%2btLqKqYOI%3d&risl=&pid=ImgRaw&r=0\",\n    description: \"Point your fingers from your eyes forward\"\n  },\n  \"love\": {\n    name: \"Love\",\n    gif: \"https://lifeprint.com/asl101/gifs/l/love.gif\",\n    description: \"Cross your arms over your chest\"\n  },\n  \"mom\": {\n    name: \"Mom\",\n    gif: \"https://media.giphy.com/media/l0MYQcLKwtl5v6H1S/giphy.gif\",\n    description: \"Tap your chin with your thumb\"\n  },\n  \"more\": {\n    name: \"More\",\n    gif: \"https://lifeprint.com/asl101/gifs/m/more.gif\",\n    description: \"Tap your fingertips together\"\n  },\n  \"no\": {\n    name: \"No\",\n    gif: \"https://lifeprint.com/asl101/gifs/n/no.gif\",\n    description: \"Snap your fingers together\"\n  },\n  \"orange\": {\n    name: \"Orange\",\n    gif: \"https://lifeprint.com/asl101/gifs/o/orange.gif\",\n    description: \"Squeeze your hand at your mouth like squeezing an orange\"\n  },\n  \"please\": {\n    name: \"Please\",\n    gif: \"https://lifeprint.com/asl101/gifs/p/please.gif\",\n    description: \"Rub your chest in a circular motion\"\n  },\n  \"red\": {\n    name: \"Red\",\n    gif: \"https://lifeprint.com/asl101/gifs/r/red.gif\",\n    description: \"Brush your lips with your index finger\"\n  },\n  \"run\": {\n    name: \"Run\",\n    gif: \"https://lifeprint.com/asl101/gifs/r/run.gif\",\n    description: \"Hook your thumbs and wiggle your fingers\"\n  },\n  \"sad\": {\n    name: \"Sad\",\n    gif: \"https://lifeprint.com/asl101/gifs/s/sad.gif\",\n    description: \"Drop both hands down your face\"\n  },\n  \"see\": {\n    name: \"See\",\n    gif: \"https://lifeprint.com/asl101/gifs/l/look-at-2.gif\",\n    description: \"Point from your eyes forward\"\n  },\n  \"sleep\": {\n    name: \"Sleep\",\n    gif: \"https://media4.giphy.com/media/3o7TKnRuBdakLslcaI/200.gif?cid=790b76110d8f185a9713f36dd65a0df801576e01b403c95c&rid=200.gif&ct=g\",\n    description: \"Rest your head on your hands\"\n  },\n  \"sorry\": {\n    name: \"Sorry\",\n    gif: \"https://lifeprint.com/asl101/gifs/s/sorry.gif\",\n    description: \"Rub your fist in a circle on your chest\"\n  },\n  \"thank\": {\n    name: \"Thank You\",\n    gif: \"https://lifeprint.com/asl101/gifs/t/thank-you.gif\",\n    description: \"Touch your chin and move your hand forward\"\n  },\n  \"water\": {\n    name: \"Water\",\n    gif: \"https://lifeprint.com/asl101/gifs/w/water.gif\",\n    description: \"Tap your chin with a 'W' handshape\"\n  },\n  \"white\": {\n    name: \"White\",\n    gif: \"https://lifeprint.com/asl101/gifs/w/white.gif\",\n    description: \"Touch your chest and pull your hand away\"\n  },\n  \"yellow\": {\n    name: \"Yellow\",\n    gif: \"https://lifeprint.com/asl101/gifs/y/yellow.gif\",\n    description: \"Shake a 'Y' handshape\"\n  },\n  \"yes\": {\n    name: \"Yes\",\n    gif: \"https://media.tenor.com/oYIirlyIih0AAAAC/yes-asl.gif\",\n    description: \"Nod your fist up and down\"\n  }\n};\nconst TrainingPage = ({\n  onBackToHome\n}) => {\n  _s();\n  const [currentSign, setCurrentSign] = useState('hello');\n  const [status, setStatus] = useState('');\n  const [recordedVideos, setRecordedVideos] = useState([]);\n  const [isCapturing, setIsCapturing] = useState(false);\n  const webcamRef = useRef(null);\n  const mediaRecorderRef = useRef(null);\n  const recordedChunksRef = useRef([]);\n\n  // Use sign detection hook\n  const {\n    isConnected,\n    prediction,\n    isRecording: isAIRecording,\n    recordingStatus,\n    processedFrame,\n    signMatched,\n    targetSign,\n    startRecording: startAIRecording,\n    stopRecording: stopAIRecording,\n    startFrameCapture,\n    stopFrameCapture\n  } = useSignDetection();\n  const handleSignChange = useCallback(event => {\n    setCurrentSign(event.target.value);\n  }, []);\n  const startDetection = useCallback(() => {\n    if (!webcamRef.current) {\n      setStatus('Camera not available');\n      return;\n    }\n    setIsCapturing(true);\n    startFrameCapture(webcamRef, 100); // Send frame every 100ms\n    setStatus('AI detection started');\n  }, [startFrameCapture]);\n  const stopDetection = useCallback(() => {\n    setIsCapturing(false);\n    stopFrameCapture();\n    setStatus('AI detection stopped');\n  }, [stopFrameCapture]);\n  const startRecording = useCallback(() => {\n    if (!isConnected) {\n      setStatus('AI backend not connected');\n      return;\n    }\n    if (!webcamRef.current) {\n      setStatus('Camera not available');\n      return;\n    }\n\n    // Start AI recording for the selected sign\n    startAIRecording(signLanguageData[currentSign].name);\n\n    // Also start frame capture if not already started\n    if (!isCapturing) {\n      startDetection();\n    }\n    setStatus(`Recording started for: ${signLanguageData[currentSign].name}`);\n  }, [currentSign, isConnected, startAIRecording, isCapturing, startDetection]);\n  const stopRecording = useCallback(() => {\n    // Stop AI recording\n    stopAIRecording();\n    setStatus('Recording stopped');\n  }, [stopAIRecording]);\n  const downloadRecording = video => {\n    const a = document.createElement('a');\n    a.href = video.url;\n    a.download = `sign_${video.sign}_${video.timestamp}.webm`;\n    a.click();\n  };\n\n  // Auto-start detection when connected\n  useEffect(() => {\n    if (isConnected && webcamRef.current && !isCapturing) {\n      startDetection();\n    }\n  }, [isConnected, startDetection, isCapturing]);\n  return /*#__PURE__*/_jsxDEV(TrainingContainer, {\n    children: [/*#__PURE__*/_jsxDEV(Navigation, {\n      children: /*#__PURE__*/_jsxDEV(NavContainer, {\n        children: [/*#__PURE__*/_jsxDEV(Logo, {\n          children: [/*#__PURE__*/_jsxDEV(LogoIcon, {\n            children: /*#__PURE__*/_jsxDEV(Brain, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1148,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1147,\n            columnNumber: 13\n          }, this), \"ASL Neural\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1146,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(BackButton, {\n          onClick: onBackToHome,\n          children: [/*#__PURE__*/_jsxDEV(ArrowLeft, {\n            size: 18\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1153,\n            columnNumber: 13\n          }, this), \"Back to Home\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1152,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1145,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1144,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(MainContent, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          marginBottom: 'var(--space-12)'\n        },\n        children: /*#__PURE__*/_jsxDEV(StatusBadge, {\n          children: [/*#__PURE__*/_jsxDEV(Eye, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1162,\n            columnNumber: 13\n          }, this), \"Neural Vision Active\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1161,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1160,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(PageTitle, {\n        children: \"AI Training Session\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1167,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(PageSubtitle, {\n        children: \"Experience real-time neural network analysis as our AI learns from your sign language practice\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1168,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TrainingGrid, {\n        children: [/*#__PURE__*/_jsxDEV(CameraSection, {\n          children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n            children: [/*#__PURE__*/_jsxDEV(SectionIcon, {\n              children: /*#__PURE__*/_jsxDEV(Camera, {\n                size: 24\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1176,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1175,\n              columnNumber: 15\n            }, this), \"Neural Vision Feed\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1174,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ConnectionStatus, {\n            connected: isConnected,\n            children: [isConnected ? /*#__PURE__*/_jsxDEV(Wifi, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1182,\n              columnNumber: 30\n            }, this) : /*#__PURE__*/_jsxDEV(WifiOff, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1182,\n              columnNumber: 51\n            }, this), isConnected ? 'AI Connected' : 'AI Disconnected']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1181,\n            columnNumber: 13\n          }, this), prediction && /*#__PURE__*/_jsxDEV(PredictionDisplay, {\n            matched: signMatched,\n            children: [/*#__PURE__*/_jsxDEV(PredictionText, {\n              matched: signMatched,\n              children: [\"Detected: \", prediction.sign]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1188,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ConfidenceBar, {\n              children: /*#__PURE__*/_jsxDEV(ConfidenceFill, {\n                confidence: prediction.confidence\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1192,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1191,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '0.875rem',\n                marginTop: '8px',\n                color: 'var(--text-secondary)'\n              },\n              children: [\"Confidence: \", Math.round(prediction.confidence * 100), \"%\", signMatched && targetSign && /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: 'var(--success-600)',\n                  marginLeft: '8px'\n                },\n                children: \"\\u2713 Match! Recording...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1197,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1194,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1187,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(WebcamContainer, {\n            children: [/*#__PURE__*/_jsxDEV(StyledWebcam, {\n              ref: webcamRef,\n              audio: false,\n              screenshotFormat: \"image/jpeg\",\n              videoConstraints: {\n                width: 640,\n                height: 480,\n                facingMode: \"user\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1205,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(RecordingOverlay, {\n              isRecording: isAIRecording,\n              children: isAIRecording ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    width: '8px',\n                    height: '8px',\n                    borderRadius: '50%',\n                    backgroundColor: 'white',\n                    marginRight: '4px'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1218,\n                  columnNumber: 21\n                }, this), \"Recording\"]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Eye, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1229,\n                  columnNumber: 21\n                }, this), \"Ready\"]\n              }, void 0, true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1215,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1204,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1173,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SignSection, {\n          children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n            children: [/*#__PURE__*/_jsxDEV(SectionIcon, {\n              children: /*#__PURE__*/_jsxDEV(Target, {\n                size: 24\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1240,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1239,\n              columnNumber: 15\n            }, this), \"Select a Sign\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1238,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SignSelector, {\n            value: currentSign,\n            onChange: handleSignChange,\n            disabled: isAIRecording,\n            children: Object.keys(signLanguageData).map(signKey => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: signKey,\n              children: signLanguageData[signKey].name\n            }, signKey, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1250,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1244,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SignDisplay, {\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: signLanguageData[currentSign].gif,\n              alt: signLanguageData[currentSign].name,\n              onError: e => {\n                e.target.style.display = 'none';\n                e.target.nextSibling.style.display = 'flex';\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1256,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'none',\n                fontSize: '3rem'\n              },\n              children: \"\\uD83D\\uDCF7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1264,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1255,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SignName, {\n            children: signLanguageData[currentSign].name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1268,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SignDescription, {\n            children: signLanguageData[currentSign].description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1269,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1237,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1172,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ControlsSection, {\n        children: /*#__PURE__*/_jsxDEV(ControlButton, {\n          variant: \"primary\",\n          onClick: isAIRecording ? stopRecording : startRecording,\n          children: isAIRecording ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Square, {\n              size: 18\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1282,\n              columnNumber: 17\n            }, this), \"Stop Neural Recording\"]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Play, {\n              size: 18\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1287,\n              columnNumber: 17\n            }, this), \"Start Neural Recording\"]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1276,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1275,\n        columnNumber: 9\n      }, this), (status || recordingStatus) && /*#__PURE__*/_jsxDEV(StatusMessage, {\n        type: (status || recordingStatus).includes('error') ? 'error' : (status || recordingStatus).includes('success') ? 'success' : 'info',\n        children: recordingStatus || status\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1295,\n        columnNumber: 11\n      }, this), recordedVideos.length > 0 && /*#__PURE__*/_jsxDEV(RecordingsSection, {\n        children: [/*#__PURE__*/_jsxDEV(RecordingsTitle, {\n          children: \"Your Practice Recordings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1302,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(RecordingsGrid, {\n          children: recordedVideos.map(video => /*#__PURE__*/_jsxDEV(RecordingCard, {\n            children: [/*#__PURE__*/_jsxDEV(RecordingTitle, {\n              children: video.sign\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1306,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(RecordingTime, {\n              children: new Date(video.timestamp).toLocaleString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1307,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(DownloadButton, {\n              onClick: () => downloadRecording(video),\n              children: [/*#__PURE__*/_jsxDEV(Download, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1311,\n                columnNumber: 21\n              }, this), \"Download\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1310,\n              columnNumber: 19\n            }, this)]\n          }, video.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1305,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1303,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1301,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1159,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1143,\n    columnNumber: 5\n  }, this);\n};\n_s(TrainingPage, \"Ldk0SZFyuIEl03i2VdWefAuAHpg=\", false, function () {\n  return [useSignDetection];\n});\n_c36 = TrainingPage;\nexport default TrainingPage;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22, _c23, _c24, _c25, _c26, _c27, _c28, _c29, _c30, _c31, _c32, _c33, _c34, _c35, _c36;\n$RefreshReg$(_c, \"TrainingContainer\");\n$RefreshReg$(_c2, \"Navigation\");\n$RefreshReg$(_c3, \"NavContainer\");\n$RefreshReg$(_c4, \"Logo\");\n$RefreshReg$(_c5, \"LogoIcon\");\n$RefreshReg$(_c6, \"BackButton\");\n$RefreshReg$(_c7, \"PageTitle\");\n$RefreshReg$(_c8, \"PageSubtitle\");\n$RefreshReg$(_c9, \"StatusBadge\");\n$RefreshReg$(_c0, \"MainContent\");\n$RefreshReg$(_c1, \"TrainingGrid\");\n$RefreshReg$(_c10, \"CameraSection\");\n$RefreshReg$(_c11, \"SectionTitle\");\n$RefreshReg$(_c12, \"SectionIcon\");\n$RefreshReg$(_c13, \"WebcamContainer\");\n$RefreshReg$(_c14, \"StyledWebcam\");\n$RefreshReg$(_c15, \"RecordingOverlay\");\n$RefreshReg$(_c16, \"SignSection\");\n$RefreshReg$(_c17, \"SignSelector\");\n$RefreshReg$(_c18, \"SignDisplay\");\n$RefreshReg$(_c19, \"SignName\");\n$RefreshReg$(_c20, \"SignDescription\");\n$RefreshReg$(_c21, \"ControlsSection\");\n$RefreshReg$(_c22, \"ControlButton\");\n$RefreshReg$(_c23, \"StatusMessage\");\n$RefreshReg$(_c24, \"RecordingsSection\");\n$RefreshReg$(_c25, \"RecordingsTitle\");\n$RefreshReg$(_c26, \"RecordingsGrid\");\n$RefreshReg$(_c27, \"RecordingCard\");\n$RefreshReg$(_c28, \"RecordingTitle\");\n$RefreshReg$(_c29, \"RecordingTime\");\n$RefreshReg$(_c30, \"DownloadButton\");\n$RefreshReg$(_c31, \"PredictionDisplay\");\n$RefreshReg$(_c32, \"PredictionText\");\n$RefreshReg$(_c33, \"ConfidenceBar\");\n$RefreshReg$(_c34, \"ConfidenceFill\");\n$RefreshReg$(_c35, \"ConnectionStatus\");\n$RefreshReg$(_c36, \"TrainingPage\");", "map": {"version": 3, "names": ["useState", "useRef", "useCallback", "useEffect", "styled", "Webcam", "Brain", "Camera", "ArrowLeft", "Play", "Square", "Download", "Zap", "Eye", "Target", "Wifi", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useSignDetection", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "TrainingContainer", "div", "_c", "Navigation", "nav", "_c2", "NavContainer", "_c3", "Logo", "_c4", "LogoIcon", "_c5", "BackButton", "button", "_c6", "Page<PERSON><PERSON>le", "h1", "_c7", "PageSubtitle", "p", "_c8", "StatusBadge", "_c9", "MainContent", "main", "_c0", "TrainingGrid", "_c1", "CameraSection", "_c10", "SectionTitle", "h2", "_c11", "SectionIcon", "_c12", "WebcamContainer", "_c13", "StyledWebcam", "_c14", "RecordingOverlay", "props", "isRecording", "_c15", "SignSection", "_c16", "SignSelector", "select", "_c17", "SignDisplay", "_c18", "SignName", "h3", "_c19", "SignDescription", "_c20", "ControlsSection", "_c21", "ControlButton", "variant", "_c22", "StatusMessage", "type", "_c23", "RecordingsSection", "_c24", "RecordingsTitle", "_c25", "RecordingsGrid", "_c26", "RecordingCard", "_c27", "RecordingTitle", "_c28", "RecordingTime", "_c29", "DownloadButton", "_c30", "PredictionDisplay", "matched", "isStale", "_c31", "PredictionText", "_c32", "ConfidenceBar", "_c33", "ConfidenceFill", "confidence", "_c34", "ConnectionStatus", "connected", "_c35", "signLanguageData", "name", "gif", "description", "TrainingPage", "onBackToHome", "_s", "currentSign", "setCurrentSign", "status", "setStatus", "recordedVideos", "setRecordedVideos", "isCapturing", "setIsCapturing", "webcamRef", "mediaRecorderRef", "recordedChunksRef", "isConnected", "prediction", "isAIRecording", "recordingStatus", "processedFrame", "signMatched", "targetSign", "startRecording", "startAIRecording", "stopRecording", "stopAIRecording", "startFrameCapture", "stopFrameCapture", "handleSignChange", "event", "target", "value", "startDetection", "current", "stopDetection", "downloadRecording", "video", "a", "document", "createElement", "href", "url", "download", "sign", "timestamp", "click", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "style", "textAlign", "marginBottom", "fontSize", "marginTop", "color", "Math", "round", "marginLeft", "ref", "audio", "screenshotFormat", "videoConstraints", "width", "height", "facingMode", "borderRadius", "backgroundColor", "marginRight", "onChange", "disabled", "Object", "keys", "map", "sign<PERSON><PERSON>", "src", "alt", "onError", "e", "display", "nextS<PERSON>ling", "includes", "length", "Date", "toLocaleString", "id", "_c36", "$RefreshReg$"], "sources": ["D:/ASL/training-frontend/src/components/TrainingPage.js"], "sourcesContent": ["import { useState, useRef, useCallback, useEffect } from 'react';\r\nimport styled from 'styled-components';\r\nimport Webcam from 'react-webcam';\r\nimport {\r\n  Brain,\r\n  Camera,\r\n  ArrowLeft,\r\n  Play,\r\n  Square,\r\n  Download,\r\n  Zap,\r\n  Eye,\r\n  Target,\r\n  Wifi,\r\n  WifiOff\r\n} from 'lucide-react';\r\nimport { useSignDetection } from '../hooks/useSignDetection';\r\n\r\nconst TrainingContainer = styled.div`\r\n  min-height: 100vh;\r\n  background: var(--bg-primary);\r\n  position: relative;\r\n  overflow-x: hidden;\r\n\r\n  &::before {\r\n    content: '';\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background:\r\n      radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),\r\n      radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.03) 0%, transparent 50%);\r\n    pointer-events: none;\r\n    z-index: 0;\r\n  }\r\n`;\r\n\r\nconst Navigation = styled.nav`\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  z-index: 50;\r\n  background: var(--bg-glass);\r\n  backdrop-filter: blur(20px);\r\n  border-bottom: 1px solid var(--border-neural);\r\n  padding: var(--space-4) 0;\r\n  transition: var(--transition-normal);\r\n`;\r\n\r\nconst NavContainer = styled.div`\r\n  max-width: 1400px;\r\n  margin: 0 auto;\r\n  padding: 0 var(--space-6);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n\r\n  @media (max-width: 768px) {\r\n    padding: 0 var(--space-4);\r\n  }\r\n`;\r\n\r\nconst Logo = styled.div`\r\n  font-family: var(--font-primary);\r\n  font-size: 1.5rem;\r\n  font-weight: 700;\r\n  background: var(--text-gradient);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--space-3);\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 1.25rem;\r\n    gap: var(--space-2);\r\n  }\r\n`;\r\n\r\nconst LogoIcon = styled.div`\r\n  width: 40px;\r\n  height: 40px;\r\n  background: var(--bg-neural);\r\n  border-radius: var(--radius-lg);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  box-shadow: var(--shadow-neural);\r\n\r\n  @media (max-width: 768px) {\r\n    width: 36px;\r\n    height: 36px;\r\n  }\r\n`;\r\n\r\nconst BackButton = styled.button`\r\n  background: var(--bg-glass);\r\n  color: var(--text-secondary);\r\n  border: 1px solid var(--border-neural);\r\n  padding: var(--space-3) var(--space-5);\r\n  border-radius: var(--radius-xl);\r\n  font-size: 0.9rem;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  transition: var(--transition-normal);\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--space-2);\r\n  backdrop-filter: blur(10px);\r\n\r\n  &:hover {\r\n    background: var(--primary-50);\r\n    color: var(--primary-600);\r\n    border-color: var(--primary-300);\r\n    transform: translateY(-1px);\r\n    box-shadow: var(--shadow-lg);\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    padding: var(--space-2) var(--space-4);\r\n    font-size: 0.85rem;\r\n  }\r\n`;\r\n\r\nconst PageTitle = styled.h1`\r\n  font-family: var(--font-primary);\r\n  font-size: 2.5rem;\r\n  font-weight: 700;\r\n  background: var(--text-gradient);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n  text-align: center;\r\n  margin-bottom: var(--space-4);\r\n  letter-spacing: -0.02em;\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 2rem;\r\n  }\r\n`;\r\n\r\nconst PageSubtitle = styled.p`\r\n  font-size: 1.125rem;\r\n  color: var(--text-secondary);\r\n  text-align: center;\r\n  margin-bottom: var(--space-16);\r\n  max-width: 700px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n  line-height: 1.6;\r\n\r\n  @media (max-width: 768px) {\r\n    margin-bottom: var(--space-12);\r\n    font-size: 1rem;\r\n  }\r\n`;\r\n\r\nconst StatusBadge = styled.div`\r\n  display: inline-flex;\r\n  align-items: center;\r\n  gap: var(--space-2);\r\n  background: var(--bg-glass);\r\n  border: 1px solid var(--border-neural);\r\n  border-radius: var(--radius-full);\r\n  padding: var(--space-2) var(--space-4);\r\n  margin-bottom: var(--space-8);\r\n  font-size: 0.875rem;\r\n  font-weight: 500;\r\n  color: var(--text-accent);\r\n  backdrop-filter: blur(10px);\r\n  box-shadow: var(--shadow-glow);\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 0.8rem;\r\n    padding: var(--space-2) var(--space-3);\r\n  }\r\n`;\r\n\r\nconst MainContent = styled.main`\r\n  padding: var(--space-20) var(--space-4) var(--space-16);\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n\r\n  @media (max-width: 768px) {\r\n    padding: var(--space-12) var(--space-3) var(--space-8);\r\n    max-width: 100%;\r\n  }\r\n`;\r\n\r\nconst TrainingGrid = styled.div`\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: var(--space-8);\r\n  max-width: 1200px;\r\n  margin: 0 auto var(--space-12);\r\n\r\n  @media (max-width: 1024px) {\r\n    grid-template-columns: 1fr;\r\n    gap: var(--space-4);\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    gap: var(--space-3);\r\n    margin: 0 auto var(--space-6);\r\n  }\r\n`;\r\n\r\nconst CameraSection = styled.div`\r\n  background: var(--bg-glass);\r\n  border-radius: var(--radius-2xl);\r\n  padding: var(--space-10);\r\n  border: 1px solid var(--border-neural);\r\n  backdrop-filter: blur(20px);\r\n  box-shadow: var(--shadow-lg);\r\n  transition: var(--transition-normal);\r\n  position: relative;\r\n  overflow: hidden;\r\n\r\n  @media (max-width: 768px) {\r\n    padding: var(--space-6);\r\n    border-radius: var(--radius-xl);\r\n  }\r\n\r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    height: 4px;\r\n    background: var(--bg-neural);\r\n    transform: scaleX(0);\r\n    transition: var(--transition-normal);\r\n  }\r\n\r\n  &:hover {\r\n    box-shadow: var(--shadow-xl), var(--shadow-glow);\r\n    border-color: var(--primary-300);\r\n\r\n    &::before {\r\n      transform: scaleX(1);\r\n    }\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    padding: var(--space-8);\r\n  }\r\n`;\r\n\r\nconst SectionTitle = styled.h2`\r\n  font-family: var(--font-primary);\r\n  font-size: 1.25rem;\r\n  margin-bottom: var(--space-6);\r\n  color: var(--text-primary);\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--space-3);\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 1.125rem;\r\n    margin-bottom: var(--space-4);\r\n  }\r\n`;\r\n\r\nconst SectionIcon = styled.div`\r\n  width: 36px;\r\n  height: 36px;\r\n  background: var(--bg-neural);\r\n  border-radius: var(--radius-lg);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  box-shadow: var(--shadow-neural);\r\n\r\n  @media (max-width: 768px) {\r\n    width: 32px;\r\n    height: 32px;\r\n  }\r\n`;\r\n\r\nconst WebcamContainer = styled.div`\r\n  position: relative;\r\n  border-radius: var(--radius-2xl);\r\n  overflow: hidden;\r\n  background: var(--neural-100);\r\n  aspect-ratio: 4/3;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border: 3px solid var(--border-neural);\r\n  margin-bottom: var(--space-6);\r\n  box-shadow: var(--shadow-lg);\r\n`;\r\n\r\nconst StyledWebcam = styled(Webcam)`\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n`;\r\n\r\nconst RecordingOverlay = styled.div`\r\n  position: absolute;\r\n  top: var(--space-4);\r\n  right: var(--space-4);\r\n  background: ${props => props.isRecording ?\r\n    'var(--error-500)' :\r\n    'var(--neural-600)'\r\n  };\r\n  color: white;\r\n  padding: var(--space-3) var(--space-5);\r\n  border-radius: var(--radius-full);\r\n  font-size: 0.9rem;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--space-2);\r\n  box-shadow: var(--shadow-lg);\r\n  animation: ${props => props.isRecording ? 'pulse 1.5s infinite' : 'none'};\r\n\r\n  @keyframes pulse {\r\n    0%, 100% { opacity: 1; transform: scale(1); }\r\n    50% { opacity: 0.8; transform: scale(1.05); }\r\n  }\r\n`;\r\n\r\nconst SignSection = styled.div`\r\n  background: var(--bg-primary);\r\n  border-radius: var(--radius-2xl);\r\n  padding: var(--space-8);\r\n  border: 1px solid var(--border-light);\r\n  box-shadow: var(--shadow-lg);\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  text-align: center;\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    box-shadow: var(--shadow-xl);\r\n    border-color: var(--primary-200);\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    padding: var(--space-6);\r\n  }\r\n`;\r\n\r\nconst SignSelector = styled.select`\r\n  width: 100%;\r\n  padding: var(--space-3) var(--space-4);\r\n  border: 2px solid var(--border-light);\r\n  border-radius: var(--radius-lg);\r\n  background: var(--bg-primary);\r\n  color: var(--text-primary);\r\n  font-size: 1rem;\r\n  font-weight: 500;\r\n  margin-bottom: var(--space-4);\r\n  cursor: pointer;\r\n  transition: var(--transition-normal);\r\n\r\n  &:focus {\r\n    outline: none;\r\n    border-color: var(--primary-500);\r\n    box-shadow: 0 0 0 3px var(--primary-100);\r\n  }\r\n\r\n  &:hover {\r\n    border-color: var(--primary-300);\r\n  }\r\n\r\n  option {\r\n    padding: var(--space-2);\r\n    background: var(--bg-primary);\r\n    color: var(--text-primary);\r\n  }\r\n`;\r\n\r\nconst SignDisplay = styled.div`\r\n  width: 300px;\r\n  height: 300px;\r\n  background: var(--primary-50);\r\n  border-radius: var(--radius-2xl);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-bottom: var(--space-6);\r\n  border: 2px solid var(--primary-200);\r\n  transition: all 0.3s ease;\r\n  overflow: hidden;\r\n\r\n  img {\r\n    width: 100%;\r\n    height: 100%;\r\n    object-fit: cover;\r\n    border-radius: var(--radius-xl);\r\n  }\r\n\r\n  &:hover {\r\n    transform: scale(1.02);\r\n    border-color: var(--primary-300);\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    width: 250px;\r\n    height: 250px;\r\n  }\r\n`;\r\n\r\nconst SignName = styled.h3`\r\n  font-family: var(--font-primary);\r\n  font-size: 1.5rem;\r\n  margin-bottom: var(--space-3);\r\n  color: var(--text-primary);\r\n  font-weight: 700;\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 1.25rem;\r\n  }\r\n`;\r\n\r\nconst SignDescription = styled.p`\r\n  text-align: center;\r\n  line-height: 1.6;\r\n  color: var(--text-secondary);\r\n  font-size: 0.9rem;\r\n  font-weight: 400;\r\n  max-width: 280px;\r\n`;\r\n\r\nconst ControlsSection = styled.div`\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: var(--space-4);\r\n  margin-top: var(--space-8);\r\n\r\n  @media (max-width: 768px) {\r\n    flex-direction: column;\r\n    align-items: center;\r\n    gap: var(--space-3);\r\n  }\r\n`;\r\n\r\nconst ControlButton = styled.button`\r\n  background: ${props => props.variant === 'primary'\r\n    ? 'var(--primary-600)'\r\n    : 'var(--bg-primary)'};\r\n  border: ${props => props.variant === 'primary'\r\n    ? 'none'\r\n    : '1px solid var(--border-medium)'};\r\n  color: ${props => props.variant === 'primary'\r\n    ? 'white'\r\n    : 'var(--text-primary)'};\r\n  padding: var(--space-3) var(--space-6);\r\n  border-radius: var(--radius-lg);\r\n  cursor: pointer;\r\n  font-size: 0.9rem;\r\n  font-weight: 600;\r\n  transition: all 0.2s ease;\r\n  min-width: 160px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: var(--space-2);\r\n  box-shadow: ${props => props.variant === 'primary'\r\n    ? 'var(--shadow-lg)'\r\n    : 'var(--shadow-sm)'};\r\n\r\n  &:hover {\r\n    transform: translateY(-2px);\r\n    box-shadow: ${props => props.variant === 'primary'\r\n      ? 'var(--shadow-xl)'\r\n      : 'var(--shadow-md)'};\r\n    background: ${props => props.variant === 'primary'\r\n      ? 'var(--primary-700)'\r\n      : 'var(--gray-50)'};\r\n  }\r\n\r\n  &:disabled {\r\n    opacity: 0.5;\r\n    cursor: not-allowed;\r\n    transform: none;\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    width: 100%;\r\n    max-width: 280px;\r\n  }\r\n`;\r\n\r\nconst StatusMessage = styled.div`\r\n  text-align: center;\r\n  margin-top: var(--space-6);\r\n  padding: var(--space-4) var(--space-6);\r\n  border-radius: var(--radius-lg);\r\n  background: ${props =>\r\n    props.type === 'success' ? 'var(--success-500)' :\r\n    props.type === 'error' ? 'var(--error-500)' :\r\n    'var(--primary-600)'\r\n  };\r\n  color: white;\r\n  font-weight: 500;\r\n  font-size: 0.875rem;\r\n  max-width: 400px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n`;\r\n\r\nconst RecordingsSection = styled.div`\r\n  margin-top: var(--space-16);\r\n  background: var(--bg-secondary);\r\n  padding: var(--space-12) var(--space-4);\r\n  border-radius: var(--radius-2xl);\r\n  max-width: 1200px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n`;\r\n\r\nconst RecordingsTitle = styled.h3`\r\n  font-family: var(--font-primary);\r\n  color: var(--text-primary);\r\n  margin-bottom: var(--space-8);\r\n  font-size: 1.5rem;\r\n  font-weight: 600;\r\n  text-align: center;\r\n`;\r\n\r\nconst RecordingsGrid = styled.div`\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\r\n  gap: var(--space-6);\r\n\r\n  @media (max-width: 768px) {\r\n    grid-template-columns: 1fr;\r\n    gap: var(--space-4);\r\n  }\r\n`;\r\n\r\nconst RecordingCard = styled.div`\r\n  background: var(--bg-primary);\r\n  padding: var(--space-6);\r\n  border-radius: var(--radius-xl);\r\n  border: 1px solid var(--border-light);\r\n  box-shadow: var(--shadow-sm);\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    transform: translateY(-2px);\r\n    border-color: var(--primary-200);\r\n    box-shadow: var(--shadow-lg);\r\n  }\r\n`;\r\n\r\nconst RecordingTitle = styled.p`\r\n  margin: 0 0 var(--space-2) 0;\r\n  color: var(--text-primary);\r\n  font-weight: 600;\r\n  font-size: 1rem;\r\n  font-family: var(--font-primary);\r\n`;\r\n\r\nconst RecordingTime = styled.p`\r\n  margin: 0 0 var(--space-4) 0;\r\n  font-size: 0.8rem;\r\n  color: var(--text-tertiary);\r\n`;\r\n\r\nconst DownloadButton = styled.button`\r\n  background: var(--primary-600);\r\n  border: none;\r\n  border-radius: var(--radius-lg);\r\n  padding: var(--space-2) var(--space-4);\r\n  color: white;\r\n  cursor: pointer;\r\n  font-size: 0.8rem;\r\n  font-weight: 500;\r\n  transition: all 0.2s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--space-2);\r\n  margin: 0 auto;\r\n\r\n  &:hover {\r\n    background: var(--primary-700);\r\n    transform: translateY(-1px);\r\n  }\r\n`;\r\n\r\nconst PredictionDisplay = styled.div`\r\n  background: var(--bg-glass);\r\n  border: 2px solid ${props => {\r\n    if (props.matched) return 'var(--success-400)';\r\n    if (props.isStale) return 'var(--warning-300)';\r\n    return 'var(--border-light)';\r\n  }};\r\n  border-radius: var(--radius-xl);\r\n  padding: var(--space-4);\r\n  margin-bottom: var(--space-4);\r\n  text-align: center;\r\n  transition: var(--transition-normal);\r\n  backdrop-filter: blur(10px);\r\n  opacity: ${props => props.isStale ? 0.7 : 1};\r\n  min-height: 80px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n\r\n  ${props => props.matched && `\r\n    background: var(--success-50);\r\n    box-shadow: 0 0 20px var(--success-200);\r\n    animation: pulse 1s ease-in-out;\r\n  `}\r\n\r\n  ${props => props.isStale && `\r\n    background: var(--warning-50);\r\n  `}\r\n\r\n  @keyframes pulse {\r\n    0%, 100% { transform: scale(1); }\r\n    50% { transform: scale(1.02); }\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    padding: var(--space-3);\r\n    margin-bottom: var(--space-3);\r\n    min-height: 70px;\r\n  }\r\n`;\r\n\r\nconst PredictionText = styled.div`\r\n  font-size: 1.25rem;\r\n  font-weight: 600;\r\n  color: ${props => {\r\n    if (props.matched) return 'var(--success-700)';\r\n    if (props.isStale) return 'var(--warning-700)';\r\n    return 'var(--text-primary)';\r\n  }};\r\n  margin-bottom: var(--space-2);\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 1.125rem;\r\n  }\r\n`;\r\n\r\nconst ConfidenceBar = styled.div`\r\n  width: 100%;\r\n  height: 8px;\r\n  background: var(--bg-secondary);\r\n  border-radius: var(--radius-full);\r\n  overflow: hidden;\r\n  margin-top: var(--space-2);\r\n`;\r\n\r\nconst ConfidenceFill = styled.div`\r\n  height: 100%;\r\n  background: ${props => {\r\n    if (props.confidence > 0.8) return 'var(--success-500)';\r\n    if (props.confidence > 0.6) return 'var(--warning-500)';\r\n    return 'var(--error-500)';\r\n  }};\r\n  width: ${props => (props.confidence * 100)}%;\r\n  transition: width 0.3s ease;\r\n`;\r\n\r\nconst ConnectionStatus = styled.div`\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--space-2);\r\n  padding: var(--space-2) var(--space-3);\r\n  border-radius: var(--radius-lg);\r\n  font-size: 0.875rem;\r\n  font-weight: 500;\r\n  background: ${props => props.connected ? 'var(--success-50)' : 'var(--error-50)'};\r\n  color: ${props => props.connected ? 'var(--success-700)' : 'var(--error-700)'};\r\n  border: 1px solid ${props => props.connected ? 'var(--success-200)' : 'var(--error-200)'};\r\n`;\r\n\r\n// Sign language data with GIFs (matching Streamlit app)\r\nconst signLanguageData = {\r\n  \"after\": {\r\n    name: \"After\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/a/after-over-across.gif\",\r\n    description: \"Move your dominant hand over and past your non-dominant hand\"\r\n  },\r\n  \"airplane\": {\r\n    name: \"Airplane\",\r\n    gif: \"https://www.lifeprint.com/asl101/gifs/a/airplane-flying.gif\",\r\n    description: \"Extend your hand like a plane and move it through the air\"\r\n  },\r\n  \"all\": {\r\n    name: \"All\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/a/all-whole.gif\",\r\n    description: \"Circle your dominant hand around your non-dominant hand\"\r\n  },\r\n  \"alligator\": {\r\n    name: \"Alligator\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/a/alligator.gif\",\r\n    description: \"Clap your hands together like an alligator's mouth\"\r\n  },\r\n  \"animal\": {\r\n    name: \"Animal\",\r\n    gif: \"https://www.lifeprint.com/asl101/gifs-animated/animal.gif\",\r\n    description: \"Place fingertips on chest and move hands back and forth\"\r\n  },\r\n  \"any\": {\r\n    name: \"Any\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/a/any.gif\",\r\n    description: \"Point with index finger and twist your wrist\"\r\n  },\r\n  \"apple\": {\r\n    name: \"Apple\",\r\n    gif: \"https://media.giphy.com/media/l0HlHb4dtZZiMYEA8/giphy.gif\",\r\n    description: \"Twist your knuckle against your cheek\"\r\n  },\r\n  \"arm\": {\r\n    name: \"Arm\",\r\n    gif: \"https://th.bing.com/th/id/OIP.KkJLqfbp6XEHiIe-jOUb2AHaEc?w=251&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\r\n    description: \"Pat your arm with your opposite hand\"\r\n  },\r\n  \"aunt\": {\r\n    name: \"Aunt\",\r\n    gif: \"https://th.bing.com/th/id/OIP.Yz5UUZdNTrVWXf72we_N6wHaHa?rs=1&pid=ImgDetMain\",\r\n    description: \"Make an 'A' handshape near your cheek and shake it\"\r\n  },\r\n  \"baby\": {\r\n    name: \"Baby\",\r\n    gif: \"https://media.giphy.com/media/l0HlHb4dtZZiMYEA8/giphy.gif\",\r\n    description: \"Rock your arms as if holding a baby\"\r\n  },\r\n  \"ball\": {\r\n    name: \"Ball\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/b/ball.gif\",\r\n    description: \"Cup your hands as if holding a ball\"\r\n  },\r\n  \"banana\": {\r\n    name: \"Banana\",\r\n    gif: \"https://media.giphy.com/media/l0HlHb4dtZZiMYEA8/giphy.gif\",\r\n    description: \"Peel an imaginary banana with your fingers\"\r\n  },\r\n  \"bear\": {\r\n    name: \"Bear\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/b/bear.gif\",\r\n    description: \"Cross your arms and scratch like a bear\"\r\n  },\r\n  \"beautiful\": {\r\n    name: \"Beautiful\",\r\n    gif: \"https://media.giphy.com/media/l0HlHb4dtZZiMYEA8/giphy.gif\",\r\n    description: \"Circle your face with your hand and close it into a fist\"\r\n  },\r\n  \"bed\": {\r\n    name: \"Bed\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/b/bed.gif\",\r\n    description: \"Rest your head on your hands as if sleeping\"\r\n  },\r\n  \"bee\": {\r\n    name: \"Bee\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/b/bee.gif\",\r\n    description: \"Pinch your cheek and brush away as if swatting a bee\"\r\n  },\r\n  \"bird\": {\r\n    name: \"Bird\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/b/bird.gif\",\r\n    description: \"Pinch your fingers together near your mouth like a beak\"\r\n  },\r\n  \"black\": {\r\n    name: \"Black\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/b/black.gif\",\r\n    description: \"Draw your index finger across your forehead\"\r\n  },\r\n  \"blue\": {\r\n    name: \"Blue\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/b/blue.gif\",\r\n    description: \"Shake a 'B' handshape\"\r\n  },\r\n  \"book\": {\r\n    name: \"Book\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/b/book.gif\",\r\n    description: \"Open your hands like opening a book\"\r\n  },\r\n  \"boy\": {\r\n    name: \"Boy\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/b/boy.gif\",\r\n    description: \"Snap your fingers at your forehead\"\r\n  },\r\n  \"brother\": {\r\n    name: \"Brother\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/b/brother.gif\",\r\n    description: \"Make an 'L' shape and point to your forehead, then point forward\"\r\n  },\r\n  \"brown\": {\r\n    name: \"Brown\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/b/brown.gif\",\r\n    description: \"Slide your index finger down your cheek\"\r\n  },\r\n  \"bug\": {\r\n    name: \"Bug\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/b/bug.gif\",\r\n    description: \"Pinch your nose with your thumb and index finger\"\r\n  },\r\n  \"butterfly\": {\r\n    name: \"Butterfly\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/b/butterfly.gif\",\r\n    description: \"Cross your thumbs and flutter your fingers like wings\"\r\n  },\r\n  \"car\": {\r\n    name: \"Car\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/c/car.gif\",\r\n    description: \"Pretend to steer a car with both hands\"\r\n  },\r\n  \"cat\": {\r\n    name: \"Cat\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/c/cat.gif\",\r\n    description: \"Pinch your cheek and pull out like whiskers\"\r\n  },\r\n  \"chair\": {\r\n    name: \"Chair\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/c/chair.gif\",\r\n    description: \"Tap your fingers on your other hand like sitting\"\r\n  },\r\n  \"clean\": {\r\n    name: \"Clean\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/c/clean.gif\",\r\n    description: \"Wipe one palm with the other\"\r\n  },\r\n  \"cold\": {\r\n    name: \"Cold\",\r\n    gif: \"https://media.giphy.com/media/l0HlHb4dtZZiMYEA8/giphy.gif\",\r\n    description: \"Shiver with both hands in fists\"\r\n  },\r\n  \"cow\": {\r\n    name: \"Cow\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/c/cow.gif\",\r\n    description: \"Twist your thumb at your temple like a horn\"\r\n  },\r\n  \"cry\": {\r\n    name: \"Cry\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/c/cry.gif\",\r\n    description: \"Draw tears down your cheeks with your index fingers\"\r\n  },\r\n  \"cute\": {\r\n    name: \"Cute\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/c/cute-sugar.gif\",\r\n    description: \"Brush your chin with your fingers\"\r\n  },\r\n  \"dad\": {\r\n    name: \"Dad\",\r\n    gif: \"https://media.giphy.com/media/l0MYQcLKwtl5v6H1S/giphy.gif\",\r\n    description: \"Tap your forehead with your thumb\"\r\n  },\r\n  \"dance\": {\r\n    name: \"Dance\",\r\n    gif: \"https://www.bing.com/th/id/OGC.********************************?pid=1.7&rurl=https%3a%2f%2fmedia.giphy.com%2fmedia%2f3o7TKMspYQjQTbOz2U%2fgiphy.gif&ehk=h%2bdBHCxuoOT89ovSy5uTk6MCL9acaBEV6ld9lrVDRF4%3d\",\r\n    description: \"Swing your fingers over your palm like dancing\"\r\n  },\r\n  \"dirty\": {\r\n    name: \"Dirty\",\r\n    gif: \"https://th.bing.com/th/id/OIP.wRA7r1OPPUuEoLL4Hds9jAHaHa?rs=1&pid=ImgDetMain\",\r\n    description: \"Wiggle your fingers under your chin\"\r\n  },\r\n  \"dog\": {\r\n    name: \"Dog\",\r\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=uVshGsOHXgldoQ&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fd%2fdog1.gif&ehk=Xnfrvg0xwy1u%2fae9vWdKYMT25%2bF6qoteW2FThCbVrOA%3d&risl=&pid=ImgRaw&r=0\",\r\n    description: \"Pat your leg and snap your fingers\"\r\n  },\r\n  \"eat\": {\r\n    name: \"Eat\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/e/eat.gif\",\r\n    description: \"Bring your fingers to your mouth as if eating\"\r\n  },\r\n  \"elephant\": {\r\n    name: \"Elephant\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/e/elephant.gif\",\r\n    description: \"Trace the shape of an elephant's trunk with your hand\"\r\n  },\r\n  \"fish\": {\r\n    name: \"Fish\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/f/fish.gif\",\r\n    description: \"Move your hand like a fish swimming\"\r\n  },\r\n  \"flower\": {\r\n    name: \"Flower\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/f/flower.gif\",\r\n    description: \"Touch your nose with your fingertips\"\r\n  },\r\n  \"friend\": {\r\n    name: \"Friend\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/f/friend.gif\",\r\n    description: \"Hook your index fingers together\"\r\n  },\r\n  \"girl\": {\r\n    name: \"Girl\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/g/girl.gif\",\r\n    description: \"Trace your jawline with your thumb\"\r\n  },\r\n  \"go\": {\r\n    name: \"Go\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/g/go.gif\",\r\n    description: \"Point both index fingers forward and bend them\"\r\n  },\r\n  \"good\": {\r\n    name: \"Good\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/g/good.gif\",\r\n    description: \"Touch your chin and move your hand forward\"\r\n  },\r\n  \"green\": {\r\n    name: \"Green\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/g/green.gif\",\r\n    description: \"Shake a 'G' handshape\"\r\n  },\r\n  \"hair\": {\r\n    name: \"Hair\",\r\n    gif: \"https://www.lifeprint.com/asl101/gifs/h/hair-g-version.gif\",\r\n    description: \"Pinch a strand of your hair\"\r\n  },\r\n  \"happy\": {\r\n    name: \"Happy\",\r\n    gif: \"https://media0.giphy.com/media/3o7TKFpahYpUp4g0N2/giphy.gif?cid=790b7611bca188a92e2e759876756ef62ba95b7cdd337c77&rid=giphy.gif&ct=g\",\r\n    description: \"Brush your chest upward with both hands\"\r\n  },\r\n  \"hello\": {\r\n    name: \"Hello\",\r\n    gif: \"https://media.giphy.com/media/3o7TKNKOfKlIhbD3gY/giphy.gif\",\r\n    description: \"Wave your hand from side to side with palm facing forward\"\r\n  },\r\n  \"home\": {\r\n    name: \"Home\",\r\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=%2bnBd%2foQjxnoPfg&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fh%2fhome-2.gif&ehk=7yD%2f%2fh6JN1Y4D4BOrUjgKW4Jccy2Y4GVYLf%2fzyk%2b5YY%3d&risl=&pid=ImgRaw&r=0\",\r\n    description: \"Touch your mouth then your cheek\"\r\n  },\r\n  \"horse\": {\r\n    name: \"Horse\",\r\n    gif: \"https://media.giphy.com/media/l0HlM5HffraiQaHUk/giphy.gif\",\r\n    description: \"Extend your thumb and fingers at your temple like ears\"\r\n  },\r\n  \"hot\": {\r\n    name: \"Hot\",\r\n    gif: \"https://media.giphy.com/media/3o6Zt99k5aDok347bG/giphy.gif\",\r\n    description: \"Touch your mouth and quickly move your hand away\"\r\n  },\r\n  \"hungry\": {\r\n    name: \"Hungry\",\r\n    gif: \"https://media.giphy.com/media/l3vR0xkdFEz4tnfTq/giphy.gif\",\r\n    description: \"Move your hand down your chest like food going down\"\r\n  },\r\n  \"jump\": {\r\n    name: \"Jump\",\r\n    gif: \"https://lifeprint.com/asl101/gifs-animated/jump.gif\",\r\n    description: \"Bounce your fingers on your palm\"\r\n  },\r\n  \"like\": {\r\n    name: \"Like\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/l/like.gif\",\r\n    description: \"Pull your thumb and middle finger from your chest\"\r\n  },\r\n  \"look\": {\r\n    name: \"Look\",\r\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=pYhzip7LqNs7qw&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fl%2flook-at-1.gif&ehk=rFJ7dBrMGFDK0nHLzrOPAzROVE7yqyDEcb%2btLqKqYOI%3d&risl=&pid=ImgRaw&r=0\",\r\n    description: \"Point your fingers from your eyes forward\"\r\n  },\r\n  \"love\": {\r\n    name: \"Love\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/l/love.gif\",\r\n    description: \"Cross your arms over your chest\"\r\n  },\r\n  \"mom\": {\r\n    name: \"Mom\",\r\n    gif: \"https://media.giphy.com/media/l0MYQcLKwtl5v6H1S/giphy.gif\",\r\n    description: \"Tap your chin with your thumb\"\r\n  },\r\n  \"more\": {\r\n    name: \"More\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/m/more.gif\",\r\n    description: \"Tap your fingertips together\"\r\n  },\r\n  \"no\": {\r\n    name: \"No\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/n/no.gif\",\r\n    description: \"Snap your fingers together\"\r\n  },\r\n  \"orange\": {\r\n    name: \"Orange\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/o/orange.gif\",\r\n    description: \"Squeeze your hand at your mouth like squeezing an orange\"\r\n  },\r\n  \"please\": {\r\n    name: \"Please\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/p/please.gif\",\r\n    description: \"Rub your chest in a circular motion\"\r\n  },\r\n  \"red\": {\r\n    name: \"Red\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/r/red.gif\",\r\n    description: \"Brush your lips with your index finger\"\r\n  },\r\n  \"run\": {\r\n    name: \"Run\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/r/run.gif\",\r\n    description: \"Hook your thumbs and wiggle your fingers\"\r\n  },\r\n  \"sad\": {\r\n    name: \"Sad\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/s/sad.gif\",\r\n    description: \"Drop both hands down your face\"\r\n  },\r\n  \"see\": {\r\n    name: \"See\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/l/look-at-2.gif\",\r\n    description: \"Point from your eyes forward\"\r\n  },\r\n  \"sleep\": {\r\n    name: \"Sleep\",\r\n    gif: \"https://media4.giphy.com/media/3o7TKnRuBdakLslcaI/200.gif?cid=790b76110d8f185a9713f36dd65a0df801576e01b403c95c&rid=200.gif&ct=g\",\r\n    description: \"Rest your head on your hands\"\r\n  },\r\n  \"sorry\": {\r\n    name: \"Sorry\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/s/sorry.gif\",\r\n    description: \"Rub your fist in a circle on your chest\"\r\n  },\r\n  \"thank\": {\r\n    name: \"Thank You\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/t/thank-you.gif\",\r\n    description: \"Touch your chin and move your hand forward\"\r\n  },\r\n  \"water\": {\r\n    name: \"Water\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/w/water.gif\",\r\n    description: \"Tap your chin with a 'W' handshape\"\r\n  },\r\n  \"white\": {\r\n    name: \"White\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/w/white.gif\",\r\n    description: \"Touch your chest and pull your hand away\"\r\n  },\r\n  \"yellow\": {\r\n    name: \"Yellow\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/y/yellow.gif\",\r\n    description: \"Shake a 'Y' handshape\"\r\n  },\r\n  \"yes\": {\r\n    name: \"Yes\",\r\n    gif: \"https://media.tenor.com/oYIirlyIih0AAAAC/yes-asl.gif\",\r\n    description: \"Nod your fist up and down\"\r\n  }\r\n};\r\n\r\nconst TrainingPage = ({ onBackToHome }) => {\r\n  const [currentSign, setCurrentSign] = useState('hello');\r\n  const [status, setStatus] = useState('');\r\n  const [recordedVideos, setRecordedVideos] = useState([]);\r\n  const [isCapturing, setIsCapturing] = useState(false);\r\n  const webcamRef = useRef(null);\r\n  const mediaRecorderRef = useRef(null);\r\n  const recordedChunksRef = useRef([]);\r\n\r\n  // Use sign detection hook\r\n  const {\r\n    isConnected,\r\n    prediction,\r\n    isRecording: isAIRecording,\r\n    recordingStatus,\r\n    processedFrame,\r\n    signMatched,\r\n    targetSign,\r\n    startRecording: startAIRecording,\r\n    stopRecording: stopAIRecording,\r\n    startFrameCapture,\r\n    stopFrameCapture\r\n  } = useSignDetection();\r\n\r\n  const handleSignChange = useCallback((event) => {\r\n    setCurrentSign(event.target.value);\r\n  }, []);\r\n\r\n  const startDetection = useCallback(() => {\r\n    if (!webcamRef.current) {\r\n      setStatus('Camera not available');\r\n      return;\r\n    }\r\n\r\n    setIsCapturing(true);\r\n    startFrameCapture(webcamRef, 100); // Send frame every 100ms\r\n    setStatus('AI detection started');\r\n  }, [startFrameCapture]);\r\n\r\n  const stopDetection = useCallback(() => {\r\n    setIsCapturing(false);\r\n    stopFrameCapture();\r\n    setStatus('AI detection stopped');\r\n  }, [stopFrameCapture]);\r\n\r\n  const startRecording = useCallback(() => {\r\n    if (!isConnected) {\r\n      setStatus('AI backend not connected');\r\n      return;\r\n    }\r\n\r\n    if (!webcamRef.current) {\r\n      setStatus('Camera not available');\r\n      return;\r\n    }\r\n\r\n    // Start AI recording for the selected sign\r\n    startAIRecording(signLanguageData[currentSign].name);\r\n\r\n    // Also start frame capture if not already started\r\n    if (!isCapturing) {\r\n      startDetection();\r\n    }\r\n\r\n    setStatus(`Recording started for: ${signLanguageData[currentSign].name}`);\r\n  }, [currentSign, isConnected, startAIRecording, isCapturing, startDetection]);\r\n\r\n  const stopRecording = useCallback(() => {\r\n    // Stop AI recording\r\n    stopAIRecording();\r\n    setStatus('Recording stopped');\r\n  }, [stopAIRecording]);\r\n\r\n  const downloadRecording = (video) => {\r\n    const a = document.createElement('a');\r\n    a.href = video.url;\r\n    a.download = `sign_${video.sign}_${video.timestamp}.webm`;\r\n    a.click();\r\n  };\r\n\r\n  // Auto-start detection when connected\r\n  useEffect(() => {\r\n    if (isConnected && webcamRef.current && !isCapturing) {\r\n      startDetection();\r\n    }\r\n  }, [isConnected, startDetection, isCapturing]);\r\n\r\n\r\n\r\n  return (\r\n    <TrainingContainer>\r\n      <Navigation>\r\n        <NavContainer>\r\n          <Logo>\r\n            <LogoIcon>\r\n              <Brain size={24} />\r\n            </LogoIcon>\r\n            ASL Neural\r\n          </Logo>\r\n          <BackButton onClick={onBackToHome}>\r\n            <ArrowLeft size={18} />\r\n            Back to Home\r\n          </BackButton>\r\n        </NavContainer>\r\n      </Navigation>\r\n\r\n      <MainContent>\r\n        <div style={{ textAlign: 'center', marginBottom: 'var(--space-12)' }}>\r\n          <StatusBadge>\r\n            <Eye size={16} />\r\n            Neural Vision Active\r\n          </StatusBadge>\r\n        </div>\r\n\r\n        <PageTitle>AI Training Session</PageTitle>\r\n        <PageSubtitle>\r\n          Experience real-time neural network analysis as our AI learns from your sign language practice\r\n        </PageSubtitle>\r\n\r\n        <TrainingGrid>\r\n          <CameraSection>\r\n            <SectionTitle>\r\n              <SectionIcon>\r\n                <Camera size={24} />\r\n              </SectionIcon>\r\n              Neural Vision Feed\r\n            </SectionTitle>\r\n\r\n            <ConnectionStatus connected={isConnected}>\r\n              {isConnected ? <Wifi size={16} /> : <WifiOff size={16} />}\r\n              {isConnected ? 'AI Connected' : 'AI Disconnected'}\r\n            </ConnectionStatus>\r\n\r\n            {prediction && (\r\n              <PredictionDisplay matched={signMatched}>\r\n                <PredictionText matched={signMatched}>\r\n                  Detected: {prediction.sign}\r\n                </PredictionText>\r\n                <ConfidenceBar>\r\n                  <ConfidenceFill confidence={prediction.confidence} />\r\n                </ConfidenceBar>\r\n                <div style={{ fontSize: '0.875rem', marginTop: '8px', color: 'var(--text-secondary)' }}>\r\n                  Confidence: {Math.round(prediction.confidence * 100)}%\r\n                  {signMatched && targetSign && (\r\n                    <span style={{ color: 'var(--success-600)', marginLeft: '8px' }}>\r\n                      ✓ Match! Recording...\r\n                    </span>\r\n                  )}\r\n                </div>\r\n              </PredictionDisplay>\r\n            )}\r\n            <WebcamContainer>\r\n              <StyledWebcam\r\n                ref={webcamRef}\r\n                audio={false}\r\n                screenshotFormat=\"image/jpeg\"\r\n                videoConstraints={{\r\n                  width: 640,\r\n                  height: 480,\r\n                  facingMode: \"user\"\r\n                }}\r\n              />\r\n              <RecordingOverlay isRecording={isAIRecording}>\r\n                {isAIRecording ? (\r\n                  <>\r\n                    <div style={{\r\n                      width: '8px',\r\n                      height: '8px',\r\n                      borderRadius: '50%',\r\n                      backgroundColor: 'white',\r\n                      marginRight: '4px'\r\n                    }} />\r\n                    Recording\r\n                  </>\r\n                ) : (\r\n                  <>\r\n                    <Eye size={16} />\r\n                    Ready\r\n                  </>\r\n                )}\r\n              </RecordingOverlay>\r\n            </WebcamContainer>\r\n          </CameraSection>\r\n\r\n          <SignSection>\r\n            <SectionTitle>\r\n              <SectionIcon>\r\n                <Target size={24} />\r\n              </SectionIcon>\r\n              Select a Sign\r\n            </SectionTitle>\r\n            <SignSelector\r\n              value={currentSign}\r\n              onChange={handleSignChange}\r\n              disabled={isAIRecording}\r\n            >\r\n              {Object.keys(signLanguageData).map(signKey => (\r\n                <option key={signKey} value={signKey}>\r\n                  {signLanguageData[signKey].name}\r\n                </option>\r\n              ))}\r\n            </SignSelector>\r\n            <SignDisplay>\r\n              <img\r\n                src={signLanguageData[currentSign].gif}\r\n                alt={signLanguageData[currentSign].name}\r\n                onError={(e) => {\r\n                  e.target.style.display = 'none';\r\n                  e.target.nextSibling.style.display = 'flex';\r\n                }}\r\n              />\r\n              <div style={{display: 'none', fontSize: '3rem'}}>\r\n                📷\r\n              </div>\r\n            </SignDisplay>\r\n            <SignName>{signLanguageData[currentSign].name}</SignName>\r\n            <SignDescription>\r\n              {signLanguageData[currentSign].description}\r\n            </SignDescription>\r\n          </SignSection>\r\n        </TrainingGrid>\r\n\r\n        <ControlsSection>\r\n          <ControlButton\r\n            variant=\"primary\"\r\n            onClick={isAIRecording ? stopRecording : startRecording}\r\n          >\r\n            {isAIRecording ? (\r\n              <>\r\n                <Square size={18} />\r\n                Stop Neural Recording\r\n              </>\r\n            ) : (\r\n              <>\r\n                <Play size={18} />\r\n                Start Neural Recording\r\n              </>\r\n            )}\r\n          </ControlButton>\r\n        </ControlsSection>\r\n\r\n        {(status || recordingStatus) && (\r\n          <StatusMessage type={(status || recordingStatus).includes('error') ? 'error' : (status || recordingStatus).includes('success') ? 'success' : 'info'}>\r\n            {recordingStatus || status}\r\n          </StatusMessage>\r\n        )}\r\n\r\n        {recordedVideos.length > 0 && (\r\n          <RecordingsSection>\r\n            <RecordingsTitle>Your Practice Recordings</RecordingsTitle>\r\n            <RecordingsGrid>\r\n              {recordedVideos.map((video) => (\r\n                <RecordingCard key={video.id}>\r\n                  <RecordingTitle>{video.sign}</RecordingTitle>\r\n                  <RecordingTime>\r\n                    {new Date(video.timestamp).toLocaleString()}\r\n                  </RecordingTime>\r\n                  <DownloadButton onClick={() => downloadRecording(video)}>\r\n                    <Download size={16} />\r\n                    Download\r\n                  </DownloadButton>\r\n                </RecordingCard>\r\n              ))}\r\n            </RecordingsGrid>\r\n          </RecordingsSection>\r\n        )}\r\n      </MainContent>\r\n    </TrainingContainer>\r\n  );\r\n};\r\n\r\nexport default TrainingPage; "], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,MAAM,EAAEC,WAAW,EAAEC,SAAS,QAAQ,OAAO;AAChE,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,MAAM,MAAM,cAAc;AACjC,SACEC,KAAK,EACLC,MAAM,EACNC,SAAS,EACTC,IAAI,EACJC,MAAM,EACNC,QAAQ,EACRC,GAAG,EACHC,GAAG,EACHC,MAAM,EACNC,IAAI,EACJC,OAAO,QACF,cAAc;AACrB,SAASC,gBAAgB,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE7D,MAAMC,iBAAiB,GAAGlB,MAAM,CAACmB,GAAG;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAnBIF,iBAAiB;AAqBvB,MAAMG,UAAU,GAAGrB,MAAM,CAACsB,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAXIF,UAAU;AAahB,MAAMG,YAAY,GAAGxB,MAAM,CAACmB,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACM,GAAA,GAXID,YAAY;AAalB,MAAME,IAAI,GAAG1B,MAAM,CAACmB,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACQ,GAAA,GAhBID,IAAI;AAkBV,MAAME,QAAQ,GAAG5B,MAAM,CAACmB,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACU,GAAA,GAfID,QAAQ;AAiBd,MAAME,UAAU,GAAG9B,MAAM,CAAC+B,MAAM;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GA3BIF,UAAU;AA6BhB,MAAMG,SAAS,GAAGjC,MAAM,CAACkC,EAAE;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAfIF,SAAS;AAiBf,MAAMG,YAAY,GAAGpC,MAAM,CAACqC,CAAC;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAdIF,YAAY;AAgBlB,MAAMG,WAAW,GAAGvC,MAAM,CAACmB,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACqB,GAAA,GAnBID,WAAW;AAqBjB,MAAME,WAAW,GAAGzC,MAAM,CAAC0C,IAAI;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GATIF,WAAW;AAWjB,MAAMG,YAAY,GAAG5C,MAAM,CAACmB,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC0B,GAAA,GAhBID,YAAY;AAkBlB,MAAME,aAAa,GAAG9C,MAAM,CAACmB,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC4B,IAAA,GAxCID,aAAa;AA0CnB,MAAME,YAAY,GAAGhD,MAAM,CAACiD,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAdIF,YAAY;AAgBlB,MAAMG,WAAW,GAAGnD,MAAM,CAACmB,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACiC,IAAA,GAfID,WAAW;AAiBjB,MAAME,eAAe,GAAGrD,MAAM,CAACmB,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACmC,IAAA,GAZID,eAAe;AAcrB,MAAME,YAAY,GAAGvD,MAAM,CAACC,MAAM,CAAC;AACnC;AACA;AACA;AACA,CAAC;AAACuD,IAAA,GAJID,YAAY;AAMlB,MAAME,gBAAgB,GAAGzD,MAAM,CAACmB,GAAG;AACnC;AACA;AACA;AACA,gBAAgBuC,KAAK,IAAIA,KAAK,CAACC,WAAW,GACtC,kBAAkB,GAClB,mBAAmB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eACeD,KAAK,IAAIA,KAAK,CAACC,WAAW,GAAG,qBAAqB,GAAG,MAAM;AAC1E;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAvBIH,gBAAgB;AAyBtB,MAAMI,WAAW,GAAG7D,MAAM,CAACmB,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC2C,IAAA,GApBID,WAAW;AAsBjB,MAAME,YAAY,GAAG/D,MAAM,CAACgE,MAAM;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GA5BIF,YAAY;AA8BlB,MAAMG,WAAW,GAAGlE,MAAM,CAACmB,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACgD,IAAA,GA7BID,WAAW;AA+BjB,MAAME,QAAQ,GAAGpE,MAAM,CAACqE,EAAE;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAVIF,QAAQ;AAYd,MAAMG,eAAe,GAAGvE,MAAM,CAACqC,CAAC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACmC,IAAA,GAPID,eAAe;AASrB,MAAME,eAAe,GAAGzE,MAAM,CAACmB,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACuD,IAAA,GAXID,eAAe;AAarB,MAAME,aAAa,GAAG3E,MAAM,CAAC+B,MAAM;AACnC,gBAAgB2B,KAAK,IAAIA,KAAK,CAACkB,OAAO,KAAK,SAAS,GAC9C,oBAAoB,GACpB,mBAAmB;AACzB,YAAYlB,KAAK,IAAIA,KAAK,CAACkB,OAAO,KAAK,SAAS,GAC1C,MAAM,GACN,gCAAgC;AACtC,WAAWlB,KAAK,IAAIA,KAAK,CAACkB,OAAO,KAAK,SAAS,GACzC,OAAO,GACP,qBAAqB;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgBlB,KAAK,IAAIA,KAAK,CAACkB,OAAO,KAAK,SAAS,GAC9C,kBAAkB,GAClB,kBAAkB;AACxB;AACA;AACA;AACA,kBAAkBlB,KAAK,IAAIA,KAAK,CAACkB,OAAO,KAAK,SAAS,GAC9C,kBAAkB,GAClB,kBAAkB;AAC1B,kBAAkBlB,KAAK,IAAIA,KAAK,CAACkB,OAAO,KAAK,SAAS,GAC9C,oBAAoB,GACpB,gBAAgB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GA7CIF,aAAa;AA+CnB,MAAMG,aAAa,GAAG9E,MAAM,CAACmB,GAAG;AAChC;AACA;AACA;AACA;AACA,gBAAgBuC,KAAK,IACjBA,KAAK,CAACqB,IAAI,KAAK,SAAS,GAAG,oBAAoB,GAC/CrB,KAAK,CAACqB,IAAI,KAAK,OAAO,GAAG,kBAAkB,GAC3C,oBAAoB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA,CACC;AAACC,IAAA,GAhBIF,aAAa;AAkBnB,MAAMG,iBAAiB,GAAGjF,MAAM,CAACmB,GAAG;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC+D,IAAA,GARID,iBAAiB;AAUvB,MAAME,eAAe,GAAGnF,MAAM,CAACqE,EAAE;AACjC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACe,IAAA,GAPID,eAAe;AASrB,MAAME,cAAc,GAAGrF,MAAM,CAACmB,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACmE,IAAA,GATID,cAAc;AAWpB,MAAME,aAAa,GAAGvF,MAAM,CAACmB,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACqE,IAAA,GAbID,aAAa;AAenB,MAAME,cAAc,GAAGzF,MAAM,CAACqC,CAAC;AAC/B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACqD,IAAA,GANID,cAAc;AAQpB,MAAME,aAAa,GAAG3F,MAAM,CAACqC,CAAC;AAC9B;AACA;AACA;AACA,CAAC;AAACuD,IAAA,GAJID,aAAa;AAMnB,MAAME,cAAc,GAAG7F,MAAM,CAAC+B,MAAM;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC+D,IAAA,GAnBID,cAAc;AAqBpB,MAAME,iBAAiB,GAAG/F,MAAM,CAACmB,GAAG;AACpC;AACA,sBAAsBuC,KAAK,IAAI;EAC3B,IAAIA,KAAK,CAACsC,OAAO,EAAE,OAAO,oBAAoB;EAC9C,IAAItC,KAAK,CAACuC,OAAO,EAAE,OAAO,oBAAoB;EAC9C,OAAO,qBAAqB;AAC9B,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA,aAAavC,KAAK,IAAIA,KAAK,CAACuC,OAAO,GAAG,GAAG,GAAG,CAAC;AAC7C;AACA;AACA;AACA;AACA;AACA,IAAIvC,KAAK,IAAIA,KAAK,CAACsC,OAAO,IAAI;AAC9B;AACA;AACA;AACA,GAAG;AACH;AACA,IAAItC,KAAK,IAAIA,KAAK,CAACuC,OAAO,IAAI;AAC9B;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAvCIH,iBAAiB;AAyCvB,MAAMI,cAAc,GAAGnG,MAAM,CAACmB,GAAG;AACjC;AACA;AACA,WAAWuC,KAAK,IAAI;EAChB,IAAIA,KAAK,CAACsC,OAAO,EAAE,OAAO,oBAAoB;EAC9C,IAAItC,KAAK,CAACuC,OAAO,EAAE,OAAO,oBAAoB;EAC9C,OAAO,qBAAqB;AAC9B,CAAC;AACH;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,IAAA,GAbID,cAAc;AAepB,MAAME,aAAa,GAAGrG,MAAM,CAACmB,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACmF,IAAA,GAPID,aAAa;AASnB,MAAME,cAAc,GAAGvG,MAAM,CAACmB,GAAG;AACjC;AACA,gBAAgBuC,KAAK,IAAI;EACrB,IAAIA,KAAK,CAAC8C,UAAU,GAAG,GAAG,EAAE,OAAO,oBAAoB;EACvD,IAAI9C,KAAK,CAAC8C,UAAU,GAAG,GAAG,EAAE,OAAO,oBAAoB;EACvD,OAAO,kBAAkB;AAC3B,CAAC;AACH,WAAW9C,KAAK,IAAKA,KAAK,CAAC8C,UAAU,GAAG,GAAI;AAC5C;AACA,CAAC;AAACC,IAAA,GATIF,cAAc;AAWpB,MAAMG,gBAAgB,GAAG1G,MAAM,CAACmB,GAAG;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgBuC,KAAK,IAAIA,KAAK,CAACiD,SAAS,GAAG,mBAAmB,GAAG,iBAAiB;AAClF,WAAWjD,KAAK,IAAIA,KAAK,CAACiD,SAAS,GAAG,oBAAoB,GAAG,kBAAkB;AAC/E,sBAAsBjD,KAAK,IAAIA,KAAK,CAACiD,SAAS,GAAG,oBAAoB,GAAG,kBAAkB;AAC1F,CAAC;;AAED;AAAAC,IAAA,GAbMF,gBAAgB;AActB,MAAMG,gBAAgB,GAAG;EACvB,OAAO,EAAE;IACPC,IAAI,EAAE,OAAO;IACbC,GAAG,EAAE,2DAA2D;IAChEC,WAAW,EAAE;EACf,CAAC;EACD,UAAU,EAAE;IACVF,IAAI,EAAE,UAAU;IAChBC,GAAG,EAAE,6DAA6D;IAClEC,WAAW,EAAE;EACf,CAAC;EACD,KAAK,EAAE;IACLF,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,mDAAmD;IACxDC,WAAW,EAAE;EACf,CAAC;EACD,WAAW,EAAE;IACXF,IAAI,EAAE,WAAW;IACjBC,GAAG,EAAE,mDAAmD;IACxDC,WAAW,EAAE;EACf,CAAC;EACD,QAAQ,EAAE;IACRF,IAAI,EAAE,QAAQ;IACdC,GAAG,EAAE,2DAA2D;IAChEC,WAAW,EAAE;EACf,CAAC;EACD,KAAK,EAAE;IACLF,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,6CAA6C;IAClDC,WAAW,EAAE;EACf,CAAC;EACD,OAAO,EAAE;IACPF,IAAI,EAAE,OAAO;IACbC,GAAG,EAAE,2DAA2D;IAChEC,WAAW,EAAE;EACf,CAAC;EACD,KAAK,EAAE;IACLF,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,kGAAkG;IACvGC,WAAW,EAAE;EACf,CAAC;EACD,MAAM,EAAE;IACNF,IAAI,EAAE,MAAM;IACZC,GAAG,EAAE,8EAA8E;IACnFC,WAAW,EAAE;EACf,CAAC;EACD,MAAM,EAAE;IACNF,IAAI,EAAE,MAAM;IACZC,GAAG,EAAE,2DAA2D;IAChEC,WAAW,EAAE;EACf,CAAC;EACD,MAAM,EAAE;IACNF,IAAI,EAAE,MAAM;IACZC,GAAG,EAAE,8CAA8C;IACnDC,WAAW,EAAE;EACf,CAAC;EACD,QAAQ,EAAE;IACRF,IAAI,EAAE,QAAQ;IACdC,GAAG,EAAE,2DAA2D;IAChEC,WAAW,EAAE;EACf,CAAC;EACD,MAAM,EAAE;IACNF,IAAI,EAAE,MAAM;IACZC,GAAG,EAAE,8CAA8C;IACnDC,WAAW,EAAE;EACf,CAAC;EACD,WAAW,EAAE;IACXF,IAAI,EAAE,WAAW;IACjBC,GAAG,EAAE,2DAA2D;IAChEC,WAAW,EAAE;EACf,CAAC;EACD,KAAK,EAAE;IACLF,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,6CAA6C;IAClDC,WAAW,EAAE;EACf,CAAC;EACD,KAAK,EAAE;IACLF,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,6CAA6C;IAClDC,WAAW,EAAE;EACf,CAAC;EACD,MAAM,EAAE;IACNF,IAAI,EAAE,MAAM;IACZC,GAAG,EAAE,8CAA8C;IACnDC,WAAW,EAAE;EACf,CAAC;EACD,OAAO,EAAE;IACPF,IAAI,EAAE,OAAO;IACbC,GAAG,EAAE,+CAA+C;IACpDC,WAAW,EAAE;EACf,CAAC;EACD,MAAM,EAAE;IACNF,IAAI,EAAE,MAAM;IACZC,GAAG,EAAE,8CAA8C;IACnDC,WAAW,EAAE;EACf,CAAC;EACD,MAAM,EAAE;IACNF,IAAI,EAAE,MAAM;IACZC,GAAG,EAAE,8CAA8C;IACnDC,WAAW,EAAE;EACf,CAAC;EACD,KAAK,EAAE;IACLF,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,6CAA6C;IAClDC,WAAW,EAAE;EACf,CAAC;EACD,SAAS,EAAE;IACTF,IAAI,EAAE,SAAS;IACfC,GAAG,EAAE,iDAAiD;IACtDC,WAAW,EAAE;EACf,CAAC;EACD,OAAO,EAAE;IACPF,IAAI,EAAE,OAAO;IACbC,GAAG,EAAE,+CAA+C;IACpDC,WAAW,EAAE;EACf,CAAC;EACD,KAAK,EAAE;IACLF,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,6CAA6C;IAClDC,WAAW,EAAE;EACf,CAAC;EACD,WAAW,EAAE;IACXF,IAAI,EAAE,WAAW;IACjBC,GAAG,EAAE,mDAAmD;IACxDC,WAAW,EAAE;EACf,CAAC;EACD,KAAK,EAAE;IACLF,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,6CAA6C;IAClDC,WAAW,EAAE;EACf,CAAC;EACD,KAAK,EAAE;IACLF,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,6CAA6C;IAClDC,WAAW,EAAE;EACf,CAAC;EACD,OAAO,EAAE;IACPF,IAAI,EAAE,OAAO;IACbC,GAAG,EAAE,+CAA+C;IACpDC,WAAW,EAAE;EACf,CAAC;EACD,OAAO,EAAE;IACPF,IAAI,EAAE,OAAO;IACbC,GAAG,EAAE,+CAA+C;IACpDC,WAAW,EAAE;EACf,CAAC;EACD,MAAM,EAAE;IACNF,IAAI,EAAE,MAAM;IACZC,GAAG,EAAE,2DAA2D;IAChEC,WAAW,EAAE;EACf,CAAC;EACD,KAAK,EAAE;IACLF,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,6CAA6C;IAClDC,WAAW,EAAE;EACf,CAAC;EACD,KAAK,EAAE;IACLF,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,6CAA6C;IAClDC,WAAW,EAAE;EACf,CAAC;EACD,MAAM,EAAE;IACNF,IAAI,EAAE,MAAM;IACZC,GAAG,EAAE,oDAAoD;IACzDC,WAAW,EAAE;EACf,CAAC;EACD,KAAK,EAAE;IACLF,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,2DAA2D;IAChEC,WAAW,EAAE;EACf,CAAC;EACD,OAAO,EAAE;IACPF,IAAI,EAAE,OAAO;IACbC,GAAG,EAAE,0MAA0M;IAC/MC,WAAW,EAAE;EACf,CAAC;EACD,OAAO,EAAE;IACPF,IAAI,EAAE,OAAO;IACbC,GAAG,EAAE,8EAA8E;IACnFC,WAAW,EAAE;EACf,CAAC;EACD,KAAK,EAAE;IACLF,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,2NAA2N;IAChOC,WAAW,EAAE;EACf,CAAC;EACD,KAAK,EAAE;IACLF,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,6CAA6C;IAClDC,WAAW,EAAE;EACf,CAAC;EACD,UAAU,EAAE;IACVF,IAAI,EAAE,UAAU;IAChBC,GAAG,EAAE,kDAAkD;IACvDC,WAAW,EAAE;EACf,CAAC;EACD,MAAM,EAAE;IACNF,IAAI,EAAE,MAAM;IACZC,GAAG,EAAE,8CAA8C;IACnDC,WAAW,EAAE;EACf,CAAC;EACD,QAAQ,EAAE;IACRF,IAAI,EAAE,QAAQ;IACdC,GAAG,EAAE,gDAAgD;IACrDC,WAAW,EAAE;EACf,CAAC;EACD,QAAQ,EAAE;IACRF,IAAI,EAAE,QAAQ;IACdC,GAAG,EAAE,gDAAgD;IACrDC,WAAW,EAAE;EACf,CAAC;EACD,MAAM,EAAE;IACNF,IAAI,EAAE,MAAM;IACZC,GAAG,EAAE,8CAA8C;IACnDC,WAAW,EAAE;EACf,CAAC;EACD,IAAI,EAAE;IACJF,IAAI,EAAE,IAAI;IACVC,GAAG,EAAE,4CAA4C;IACjDC,WAAW,EAAE;EACf,CAAC;EACD,MAAM,EAAE;IACNF,IAAI,EAAE,MAAM;IACZC,GAAG,EAAE,8CAA8C;IACnDC,WAAW,EAAE;EACf,CAAC;EACD,OAAO,EAAE;IACPF,IAAI,EAAE,OAAO;IACbC,GAAG,EAAE,+CAA+C;IACpDC,WAAW,EAAE;EACf,CAAC;EACD,MAAM,EAAE;IACNF,IAAI,EAAE,MAAM;IACZC,GAAG,EAAE,4DAA4D;IACjEC,WAAW,EAAE;EACf,CAAC;EACD,OAAO,EAAE;IACPF,IAAI,EAAE,OAAO;IACbC,GAAG,EAAE,qIAAqI;IAC1IC,WAAW,EAAE;EACf,CAAC;EACD,OAAO,EAAE;IACPF,IAAI,EAAE,OAAO;IACbC,GAAG,EAAE,4DAA4D;IACjEC,WAAW,EAAE;EACf,CAAC;EACD,MAAM,EAAE;IACNF,IAAI,EAAE,MAAM;IACZC,GAAG,EAAE,qOAAqO;IAC1OC,WAAW,EAAE;EACf,CAAC;EACD,OAAO,EAAE;IACPF,IAAI,EAAE,OAAO;IACbC,GAAG,EAAE,2DAA2D;IAChEC,WAAW,EAAE;EACf,CAAC;EACD,KAAK,EAAE;IACLF,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,4DAA4D;IACjEC,WAAW,EAAE;EACf,CAAC;EACD,QAAQ,EAAE;IACRF,IAAI,EAAE,QAAQ;IACdC,GAAG,EAAE,2DAA2D;IAChEC,WAAW,EAAE;EACf,CAAC;EACD,MAAM,EAAE;IACNF,IAAI,EAAE,MAAM;IACZC,GAAG,EAAE,qDAAqD;IAC1DC,WAAW,EAAE;EACf,CAAC;EACD,MAAM,EAAE;IACNF,IAAI,EAAE,MAAM;IACZC,GAAG,EAAE,8CAA8C;IACnDC,WAAW,EAAE;EACf,CAAC;EACD,MAAM,EAAE;IACNF,IAAI,EAAE,MAAM;IACZC,GAAG,EAAE,8NAA8N;IACnOC,WAAW,EAAE;EACf,CAAC;EACD,MAAM,EAAE;IACNF,IAAI,EAAE,MAAM;IACZC,GAAG,EAAE,8CAA8C;IACnDC,WAAW,EAAE;EACf,CAAC;EACD,KAAK,EAAE;IACLF,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,2DAA2D;IAChEC,WAAW,EAAE;EACf,CAAC;EACD,MAAM,EAAE;IACNF,IAAI,EAAE,MAAM;IACZC,GAAG,EAAE,8CAA8C;IACnDC,WAAW,EAAE;EACf,CAAC;EACD,IAAI,EAAE;IACJF,IAAI,EAAE,IAAI;IACVC,GAAG,EAAE,4CAA4C;IACjDC,WAAW,EAAE;EACf,CAAC;EACD,QAAQ,EAAE;IACRF,IAAI,EAAE,QAAQ;IACdC,GAAG,EAAE,gDAAgD;IACrDC,WAAW,EAAE;EACf,CAAC;EACD,QAAQ,EAAE;IACRF,IAAI,EAAE,QAAQ;IACdC,GAAG,EAAE,gDAAgD;IACrDC,WAAW,EAAE;EACf,CAAC;EACD,KAAK,EAAE;IACLF,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,6CAA6C;IAClDC,WAAW,EAAE;EACf,CAAC;EACD,KAAK,EAAE;IACLF,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,6CAA6C;IAClDC,WAAW,EAAE;EACf,CAAC;EACD,KAAK,EAAE;IACLF,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,6CAA6C;IAClDC,WAAW,EAAE;EACf,CAAC;EACD,KAAK,EAAE;IACLF,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,mDAAmD;IACxDC,WAAW,EAAE;EACf,CAAC;EACD,OAAO,EAAE;IACPF,IAAI,EAAE,OAAO;IACbC,GAAG,EAAE,iIAAiI;IACtIC,WAAW,EAAE;EACf,CAAC;EACD,OAAO,EAAE;IACPF,IAAI,EAAE,OAAO;IACbC,GAAG,EAAE,+CAA+C;IACpDC,WAAW,EAAE;EACf,CAAC;EACD,OAAO,EAAE;IACPF,IAAI,EAAE,WAAW;IACjBC,GAAG,EAAE,mDAAmD;IACxDC,WAAW,EAAE;EACf,CAAC;EACD,OAAO,EAAE;IACPF,IAAI,EAAE,OAAO;IACbC,GAAG,EAAE,+CAA+C;IACpDC,WAAW,EAAE;EACf,CAAC;EACD,OAAO,EAAE;IACPF,IAAI,EAAE,OAAO;IACbC,GAAG,EAAE,+CAA+C;IACpDC,WAAW,EAAE;EACf,CAAC;EACD,QAAQ,EAAE;IACRF,IAAI,EAAE,QAAQ;IACdC,GAAG,EAAE,gDAAgD;IACrDC,WAAW,EAAE;EACf,CAAC;EACD,KAAK,EAAE;IACLF,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,sDAAsD;IAC3DC,WAAW,EAAE;EACf;AACF,CAAC;AAED,MAAMC,YAAY,GAAGA,CAAC;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACzC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGzH,QAAQ,CAAC,OAAO,CAAC;EACvD,MAAM,CAAC0H,MAAM,EAAEC,SAAS,CAAC,GAAG3H,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC4H,cAAc,EAAEC,iBAAiB,CAAC,GAAG7H,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC8H,WAAW,EAAEC,cAAc,CAAC,GAAG/H,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAMgI,SAAS,GAAG/H,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMgI,gBAAgB,GAAGhI,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMiI,iBAAiB,GAAGjI,MAAM,CAAC,EAAE,CAAC;;EAEpC;EACA,MAAM;IACJkI,WAAW;IACXC,UAAU;IACVrE,WAAW,EAAEsE,aAAa;IAC1BC,eAAe;IACfC,cAAc;IACdC,WAAW;IACXC,UAAU;IACVC,cAAc,EAAEC,gBAAgB;IAChCC,aAAa,EAAEC,eAAe;IAC9BC,iBAAiB;IACjBC;EACF,CAAC,GAAG9H,gBAAgB,CAAC,CAAC;EAEtB,MAAM+H,gBAAgB,GAAG9I,WAAW,CAAE+I,KAAK,IAAK;IAC9CxB,cAAc,CAACwB,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC;EACpC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,cAAc,GAAGlJ,WAAW,CAAC,MAAM;IACvC,IAAI,CAAC8H,SAAS,CAACqB,OAAO,EAAE;MACtB1B,SAAS,CAAC,sBAAsB,CAAC;MACjC;IACF;IAEAI,cAAc,CAAC,IAAI,CAAC;IACpBe,iBAAiB,CAACd,SAAS,EAAE,GAAG,CAAC,CAAC,CAAC;IACnCL,SAAS,CAAC,sBAAsB,CAAC;EACnC,CAAC,EAAE,CAACmB,iBAAiB,CAAC,CAAC;EAEvB,MAAMQ,aAAa,GAAGpJ,WAAW,CAAC,MAAM;IACtC6H,cAAc,CAAC,KAAK,CAAC;IACrBgB,gBAAgB,CAAC,CAAC;IAClBpB,SAAS,CAAC,sBAAsB,CAAC;EACnC,CAAC,EAAE,CAACoB,gBAAgB,CAAC,CAAC;EAEtB,MAAML,cAAc,GAAGxI,WAAW,CAAC,MAAM;IACvC,IAAI,CAACiI,WAAW,EAAE;MAChBR,SAAS,CAAC,0BAA0B,CAAC;MACrC;IACF;IAEA,IAAI,CAACK,SAAS,CAACqB,OAAO,EAAE;MACtB1B,SAAS,CAAC,sBAAsB,CAAC;MACjC;IACF;;IAEA;IACAgB,gBAAgB,CAAC1B,gBAAgB,CAACO,WAAW,CAAC,CAACN,IAAI,CAAC;;IAEpD;IACA,IAAI,CAACY,WAAW,EAAE;MAChBsB,cAAc,CAAC,CAAC;IAClB;IAEAzB,SAAS,CAAC,0BAA0BV,gBAAgB,CAACO,WAAW,CAAC,CAACN,IAAI,EAAE,CAAC;EAC3E,CAAC,EAAE,CAACM,WAAW,EAAEW,WAAW,EAAEQ,gBAAgB,EAAEb,WAAW,EAAEsB,cAAc,CAAC,CAAC;EAE7E,MAAMR,aAAa,GAAG1I,WAAW,CAAC,MAAM;IACtC;IACA2I,eAAe,CAAC,CAAC;IACjBlB,SAAS,CAAC,mBAAmB,CAAC;EAChC,CAAC,EAAE,CAACkB,eAAe,CAAC,CAAC;EAErB,MAAMU,iBAAiB,GAAIC,KAAK,IAAK;IACnC,MAAMC,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACrCF,CAAC,CAACG,IAAI,GAAGJ,KAAK,CAACK,GAAG;IAClBJ,CAAC,CAACK,QAAQ,GAAG,QAAQN,KAAK,CAACO,IAAI,IAAIP,KAAK,CAACQ,SAAS,OAAO;IACzDP,CAAC,CAACQ,KAAK,CAAC,CAAC;EACX,CAAC;;EAED;EACA9J,SAAS,CAAC,MAAM;IACd,IAAIgI,WAAW,IAAIH,SAAS,CAACqB,OAAO,IAAI,CAACvB,WAAW,EAAE;MACpDsB,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAACjB,WAAW,EAAEiB,cAAc,EAAEtB,WAAW,CAAC,CAAC;EAI9C,oBACE3G,OAAA,CAACG,iBAAiB;IAAA4I,QAAA,gBAChB/I,OAAA,CAACM,UAAU;MAAAyI,QAAA,eACT/I,OAAA,CAACS,YAAY;QAAAsI,QAAA,gBACX/I,OAAA,CAACW,IAAI;UAAAoI,QAAA,gBACH/I,OAAA,CAACa,QAAQ;YAAAkI,QAAA,eACP/I,OAAA,CAACb,KAAK;cAAC6J,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,cAEb;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPpJ,OAAA,CAACe,UAAU;UAACsI,OAAO,EAAElD,YAAa;UAAA4C,QAAA,gBAChC/I,OAAA,CAACX,SAAS;YAAC2J,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEzB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAEbpJ,OAAA,CAAC0B,WAAW;MAAAqH,QAAA,gBACV/I,OAAA;QAAKsJ,KAAK,EAAE;UAAEC,SAAS,EAAE,QAAQ;UAAEC,YAAY,EAAE;QAAkB,CAAE;QAAAT,QAAA,eACnE/I,OAAA,CAACwB,WAAW;UAAAuH,QAAA,gBACV/I,OAAA,CAACN,GAAG;YAACsJ,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,wBAEnB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC,eAENpJ,OAAA,CAACkB,SAAS;QAAA6H,QAAA,EAAC;MAAmB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC,eAC1CpJ,OAAA,CAACqB,YAAY;QAAA0H,QAAA,EAAC;MAEd;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC,eAEfpJ,OAAA,CAAC6B,YAAY;QAAAkH,QAAA,gBACX/I,OAAA,CAAC+B,aAAa;UAAAgH,QAAA,gBACZ/I,OAAA,CAACiC,YAAY;YAAA8G,QAAA,gBACX/I,OAAA,CAACoC,WAAW;cAAA2G,QAAA,eACV/I,OAAA,CAACZ,MAAM;gBAAC4J,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,sBAEhB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,eAEfpJ,OAAA,CAAC2F,gBAAgB;YAACC,SAAS,EAAEoB,WAAY;YAAA+B,QAAA,GACtC/B,WAAW,gBAAGhH,OAAA,CAACJ,IAAI;cAACoJ,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGpJ,OAAA,CAACH,OAAO;cAACmJ,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EACxDpC,WAAW,GAAG,cAAc,GAAG,iBAAiB;UAAA;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,EAElBnC,UAAU,iBACTjH,OAAA,CAACgF,iBAAiB;YAACC,OAAO,EAAEoC,WAAY;YAAA0B,QAAA,gBACtC/I,OAAA,CAACoF,cAAc;cAACH,OAAO,EAAEoC,WAAY;cAAA0B,QAAA,GAAC,YAC1B,EAAC9B,UAAU,CAAC2B,IAAI;YAAA;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACjBpJ,OAAA,CAACsF,aAAa;cAAAyD,QAAA,eACZ/I,OAAA,CAACwF,cAAc;gBAACC,UAAU,EAAEwB,UAAU,CAACxB;cAAW;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eAChBpJ,OAAA;cAAKsJ,KAAK,EAAE;gBAAEG,QAAQ,EAAE,UAAU;gBAAEC,SAAS,EAAE,KAAK;gBAAEC,KAAK,EAAE;cAAwB,CAAE;cAAAZ,QAAA,GAAC,cAC1E,EAACa,IAAI,CAACC,KAAK,CAAC5C,UAAU,CAACxB,UAAU,GAAG,GAAG,CAAC,EAAC,GACrD,EAAC4B,WAAW,IAAIC,UAAU,iBACxBtH,OAAA;gBAAMsJ,KAAK,EAAE;kBAAEK,KAAK,EAAE,oBAAoB;kBAAEG,UAAU,EAAE;gBAAM,CAAE;gBAAAf,QAAA,EAAC;cAEjE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACW,CACpB,eACDpJ,OAAA,CAACsC,eAAe;YAAAyG,QAAA,gBACd/I,OAAA,CAACwC,YAAY;cACXuH,GAAG,EAAElD,SAAU;cACfmD,KAAK,EAAE,KAAM;cACbC,gBAAgB,EAAC,YAAY;cAC7BC,gBAAgB,EAAE;gBAChBC,KAAK,EAAE,GAAG;gBACVC,MAAM,EAAE,GAAG;gBACXC,UAAU,EAAE;cACd;YAAE;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFpJ,OAAA,CAAC0C,gBAAgB;cAACE,WAAW,EAAEsE,aAAc;cAAA6B,QAAA,EAC1C7B,aAAa,gBACZlH,OAAA,CAAAE,SAAA;gBAAA6I,QAAA,gBACE/I,OAAA;kBAAKsJ,KAAK,EAAE;oBACVa,KAAK,EAAE,KAAK;oBACZC,MAAM,EAAE,KAAK;oBACbE,YAAY,EAAE,KAAK;oBACnBC,eAAe,EAAE,OAAO;oBACxBC,WAAW,EAAE;kBACf;gBAAE;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,aAEP;cAAA,eAAE,CAAC,gBAEHpJ,OAAA,CAAAE,SAAA;gBAAA6I,QAAA,gBACE/I,OAAA,CAACN,GAAG;kBAACsJ,IAAI,EAAE;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,SAEnB;cAAA,eAAE;YACH;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEhBpJ,OAAA,CAAC8C,WAAW;UAAAiG,QAAA,gBACV/I,OAAA,CAACiC,YAAY;YAAA8G,QAAA,gBACX/I,OAAA,CAACoC,WAAW;cAAA2G,QAAA,eACV/I,OAAA,CAACL,MAAM;gBAACqJ,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,iBAEhB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,eACfpJ,OAAA,CAACgD,YAAY;YACXgF,KAAK,EAAE3B,WAAY;YACnBoE,QAAQ,EAAE5C,gBAAiB;YAC3B6C,QAAQ,EAAExD,aAAc;YAAA6B,QAAA,EAEvB4B,MAAM,CAACC,IAAI,CAAC9E,gBAAgB,CAAC,CAAC+E,GAAG,CAACC,OAAO,iBACxC9K,OAAA;cAAsBgI,KAAK,EAAE8C,OAAQ;cAAA/B,QAAA,EAClCjD,gBAAgB,CAACgF,OAAO,CAAC,CAAC/E;YAAI,GADpB+E,OAAO;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEZ,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU,CAAC,eACfpJ,OAAA,CAACmD,WAAW;YAAA4F,QAAA,gBACV/I,OAAA;cACE+K,GAAG,EAAEjF,gBAAgB,CAACO,WAAW,CAAC,CAACL,GAAI;cACvCgF,GAAG,EAAElF,gBAAgB,CAACO,WAAW,CAAC,CAACN,IAAK;cACxCkF,OAAO,EAAGC,CAAC,IAAK;gBACdA,CAAC,CAACnD,MAAM,CAACuB,KAAK,CAAC6B,OAAO,GAAG,MAAM;gBAC/BD,CAAC,CAACnD,MAAM,CAACqD,WAAW,CAAC9B,KAAK,CAAC6B,OAAO,GAAG,MAAM;cAC7C;YAAE;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFpJ,OAAA;cAAKsJ,KAAK,EAAE;gBAAC6B,OAAO,EAAE,MAAM;gBAAE1B,QAAQ,EAAE;cAAM,CAAE;cAAAV,QAAA,EAAC;YAEjD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC,eACdpJ,OAAA,CAACqD,QAAQ;YAAA0F,QAAA,EAAEjD,gBAAgB,CAACO,WAAW,CAAC,CAACN;UAAI;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eACzDpJ,OAAA,CAACwD,eAAe;YAAAuF,QAAA,EACbjD,gBAAgB,CAACO,WAAW,CAAC,CAACJ;UAAW;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAEfpJ,OAAA,CAAC0D,eAAe;QAAAqF,QAAA,eACd/I,OAAA,CAAC4D,aAAa;UACZC,OAAO,EAAC,SAAS;UACjBwF,OAAO,EAAEnC,aAAa,GAAGO,aAAa,GAAGF,cAAe;UAAAwB,QAAA,EAEvD7B,aAAa,gBACZlH,OAAA,CAAAE,SAAA;YAAA6I,QAAA,gBACE/I,OAAA,CAACT,MAAM;cAACyJ,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,yBAEtB;UAAA,eAAE,CAAC,gBAEHpJ,OAAA,CAAAE,SAAA;YAAA6I,QAAA,gBACE/I,OAAA,CAACV,IAAI;cAAC0J,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,0BAEpB;UAAA,eAAE;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,EAEjB,CAAC7C,MAAM,IAAIY,eAAe,kBACzBnH,OAAA,CAAC+D,aAAa;QAACC,IAAI,EAAE,CAACuC,MAAM,IAAIY,eAAe,EAAEkE,QAAQ,CAAC,OAAO,CAAC,GAAG,OAAO,GAAG,CAAC9E,MAAM,IAAIY,eAAe,EAAEkE,QAAQ,CAAC,SAAS,CAAC,GAAG,SAAS,GAAG,MAAO;QAAAtC,QAAA,EACjJ5B,eAAe,IAAIZ;MAAM;QAAA0C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAChB,EAEA3C,cAAc,CAAC6E,MAAM,GAAG,CAAC,iBACxBtL,OAAA,CAACkE,iBAAiB;QAAA6E,QAAA,gBAChB/I,OAAA,CAACoE,eAAe;UAAA2E,QAAA,EAAC;QAAwB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAiB,CAAC,eAC3DpJ,OAAA,CAACsE,cAAc;UAAAyE,QAAA,EACZtC,cAAc,CAACoE,GAAG,CAAExC,KAAK,iBACxBrI,OAAA,CAACwE,aAAa;YAAAuE,QAAA,gBACZ/I,OAAA,CAAC0E,cAAc;cAAAqE,QAAA,EAAEV,KAAK,CAACO;YAAI;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAiB,CAAC,eAC7CpJ,OAAA,CAAC4E,aAAa;cAAAmE,QAAA,EACX,IAAIwC,IAAI,CAAClD,KAAK,CAACQ,SAAS,CAAC,CAAC2C,cAAc,CAAC;YAAC;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eAChBpJ,OAAA,CAAC8E,cAAc;cAACuE,OAAO,EAAEA,CAAA,KAAMjB,iBAAiB,CAACC,KAAK,CAAE;cAAAU,QAAA,gBACtD/I,OAAA,CAACR,QAAQ;gBAACwJ,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,YAExB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAgB,CAAC;UAAA,GARCf,KAAK,CAACoD,EAAE;YAAAxC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OASb,CAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CACpB;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACU,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAExB,CAAC;AAAChD,EAAA,CA7QIF,YAAY;EAAA,QAsBZpG,gBAAgB;AAAA;AAAA4L,IAAA,GAtBhBxF,YAAY;AA+QlB,eAAeA,YAAY;AAAC,IAAA7F,EAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAI,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAI,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAG,IAAA,EAAA6F,IAAA;AAAAC,YAAA,CAAAtL,EAAA;AAAAsL,YAAA,CAAAnL,GAAA;AAAAmL,YAAA,CAAAjL,GAAA;AAAAiL,YAAA,CAAA/K,GAAA;AAAA+K,YAAA,CAAA7K,GAAA;AAAA6K,YAAA,CAAA1K,GAAA;AAAA0K,YAAA,CAAAvK,GAAA;AAAAuK,YAAA,CAAApK,GAAA;AAAAoK,YAAA,CAAAlK,GAAA;AAAAkK,YAAA,CAAA/J,GAAA;AAAA+J,YAAA,CAAA7J,GAAA;AAAA6J,YAAA,CAAA3J,IAAA;AAAA2J,YAAA,CAAAxJ,IAAA;AAAAwJ,YAAA,CAAAtJ,IAAA;AAAAsJ,YAAA,CAAApJ,IAAA;AAAAoJ,YAAA,CAAAlJ,IAAA;AAAAkJ,YAAA,CAAA9I,IAAA;AAAA8I,YAAA,CAAA5I,IAAA;AAAA4I,YAAA,CAAAzI,IAAA;AAAAyI,YAAA,CAAAvI,IAAA;AAAAuI,YAAA,CAAApI,IAAA;AAAAoI,YAAA,CAAAlI,IAAA;AAAAkI,YAAA,CAAAhI,IAAA;AAAAgI,YAAA,CAAA7H,IAAA;AAAA6H,YAAA,CAAA1H,IAAA;AAAA0H,YAAA,CAAAxH,IAAA;AAAAwH,YAAA,CAAAtH,IAAA;AAAAsH,YAAA,CAAApH,IAAA;AAAAoH,YAAA,CAAAlH,IAAA;AAAAkH,YAAA,CAAAhH,IAAA;AAAAgH,YAAA,CAAA9G,IAAA;AAAA8G,YAAA,CAAA5G,IAAA;AAAA4G,YAAA,CAAAxG,IAAA;AAAAwG,YAAA,CAAAtG,IAAA;AAAAsG,YAAA,CAAApG,IAAA;AAAAoG,YAAA,CAAAjG,IAAA;AAAAiG,YAAA,CAAA9F,IAAA;AAAA8F,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}