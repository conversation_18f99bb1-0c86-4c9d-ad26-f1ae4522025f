#!/usr/bin/env python3
"""
Clean up old recordings and show current files
"""
import os
import glob

def clean_recordings():
    """Clean up old recordings and show current files"""
    recordings_dir = "recordings"
    
    if not os.path.exists(recordings_dir):
        print("No recordings directory found")
        return
    
    # List all recording files
    mp4_files = glob.glob(os.path.join(recordings_dir, "*.mp4"))
    avi_files = glob.glob(os.path.join(recordings_dir, "*.avi"))
    
    print("Current recording files:")
    print("=" * 50)
    
    all_files = mp4_files + avi_files
    if not all_files:
        print("No recording files found")
        return
    
    for filepath in all_files:
        filename = os.path.basename(filepath)
        size = os.path.getsize(filepath)
        size_mb = size / (1024 * 1024)
        print(f"📁 {filename} ({size_mb:.2f} MB)")
    
    print(f"\nTotal files: {len(all_files)}")
    print(f"MP4 files: {len(mp4_files)}")
    print(f"AVI files: {len(avi_files)}")
    
    # Ask if user wants to clean up
    response = input("\nDo you want to delete all recording files? (y/N): ")
    if response.lower() == 'y':
        for filepath in all_files:
            try:
                os.remove(filepath)
                print(f"🗑️ Deleted: {os.path.basename(filepath)}")
            except Exception as e:
                print(f"❌ Error deleting {filepath}: {e}")
        print("✅ Cleanup complete!")
    else:
        print("No files deleted")

if __name__ == "__main__":
    clean_recordings() 