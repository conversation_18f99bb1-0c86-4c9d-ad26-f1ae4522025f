<!DOCTYPE html>
<html>
<head>
    <title>WebSocket Test</title>
</head>
<body>
    <h1>WebSocket Test</h1>
    <div id="status">Connecting...</div>
    <div id="messages"></div>
    
    <script>
        const ws = new WebSocket('ws://localhost:8001/ws/detect');
        const status = document.getElementById('status');
        const messages = document.getElementById('messages');
        
        ws.onopen = function(event) {
            status.textContent = 'Connected!';
            console.log('WebSocket connected');
            
            // Send a test frame message
            const testMessage = {
                type: 'frame',
                frame: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k='
            };
            ws.send(JSON.stringify(testMessage));
        };
        
        ws.onmessage = function(event) {
            const data = JSON.parse(event.data);
            console.log('Received:', data);
            messages.innerHTML += '<div>Received: ' + JSON.stringify(data) + '</div>';
        };
        
        ws.onerror = function(error) {
            status.textContent = 'Error: ' + error;
            console.error('WebSocket error:', error);
        };
        
        ws.onclose = function(event) {
            status.textContent = 'Disconnected';
            console.log('WebSocket disconnected');
        };
    </script>
</body>
</html>
