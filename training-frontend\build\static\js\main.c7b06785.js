/*! For license information please see main.c7b06785.js.LICENSE.txt */
(()=>{var e={4:(e,n,t)=>{"use strict";var r=t(853),a=t(43),i=t(950);function o(e){var n="https://react.dev/errors/"+e;if(1<arguments.length){n+="?args[]="+encodeURIComponent(arguments[1]);for(var t=2;t<arguments.length;t++)n+="&args[]="+encodeURIComponent(arguments[t])}return"Minified React error #"+e+"; visit "+n+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function l(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function s(e){var n=e,t=e;if(e.alternate)for(;n.return;)n=n.return;else{e=n;do{0!==(4098&(n=e).flags)&&(t=n.return),e=n.return}while(e)}return 3===n.tag?t:null}function u(e){if(13===e.tag){var n=e.memoizedState;if(null===n&&(null!==(e=e.alternate)&&(n=e.memoizedState)),null!==n)return n.dehydrated}return null}function c(e){if(s(e)!==e)throw Error(o(188))}function d(e){var n=e.tag;if(5===n||26===n||27===n||6===n)return e;for(e=e.child;null!==e;){if(null!==(n=d(e)))return n;e=e.sibling}return null}var f=Object.assign,p=Symbol.for("react.element"),h=Symbol.for("react.transitional.element"),m=Symbol.for("react.portal"),g=Symbol.for("react.fragment"),v=Symbol.for("react.strict_mode"),y=Symbol.for("react.profiler"),b=Symbol.for("react.provider"),w=Symbol.for("react.consumer"),x=Symbol.for("react.context"),k=Symbol.for("react.forward_ref"),S=Symbol.for("react.suspense"),E=Symbol.for("react.suspense_list"),C=Symbol.for("react.memo"),_=Symbol.for("react.lazy");Symbol.for("react.scope");var z=Symbol.for("react.activity");Symbol.for("react.legacy_hidden"),Symbol.for("react.tracing_marker");var P=Symbol.for("react.memo_cache_sentinel");Symbol.for("react.view_transition");var j=Symbol.iterator;function T(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=j&&e[j]||e["@@iterator"])?e:null}var O=Symbol.for("react.client.reference");function N(e){if(null==e)return null;if("function"===typeof e)return e.$$typeof===O?null:e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case g:return"Fragment";case y:return"Profiler";case v:return"StrictMode";case S:return"Suspense";case E:return"SuspenseList";case z:return"Activity"}if("object"===typeof e)switch(e.$$typeof){case m:return"Portal";case x:return(e.displayName||"Context")+".Provider";case w:return(e._context.displayName||"Context")+".Consumer";case k:var n=e.render;return(e=e.displayName)||(e=""!==(e=n.displayName||n.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case C:return null!==(n=e.displayName||null)?n:N(e.type)||"Memo";case _:n=e._payload,e=e._init;try{return N(e(n))}catch(t){}}return null}var A=Array.isArray,R=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,L=i.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,M={pending:!1,data:null,method:null,action:null},D=[],I=-1;function F(e){return{current:e}}function U(e){0>I||(e.current=D[I],D[I]=null,I--)}function H(e,n){I++,D[I]=e.current,e.current=n}var W=F(null),$=F(null),B=F(null),V=F(null);function q(e,n){switch(H(B,n),H($,e),H(W,null),n.nodeType){case 9:case 11:e=(e=n.documentElement)&&(e=e.namespaceURI)?ad(e):0;break;default:if(e=n.tagName,n=n.namespaceURI)e=id(n=ad(n),e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}U(W),H(W,e)}function K(){U(W),U($),U(B)}function Q(e){null!==e.memoizedState&&H(V,e);var n=W.current,t=id(n,e.type);n!==t&&(H($,e),H(W,t))}function Y(e){$.current===e&&(U(W),U($)),V.current===e&&(U(V),Qd._currentValue=M)}var G=Object.prototype.hasOwnProperty,X=r.unstable_scheduleCallback,Z=r.unstable_cancelCallback,J=r.unstable_shouldYield,ee=r.unstable_requestPaint,ne=r.unstable_now,te=r.unstable_getCurrentPriorityLevel,re=r.unstable_ImmediatePriority,ae=r.unstable_UserBlockingPriority,ie=r.unstable_NormalPriority,oe=r.unstable_LowPriority,le=r.unstable_IdlePriority,se=r.log,ue=r.unstable_setDisableYieldValue,ce=null,de=null;function fe(e){if("function"===typeof se&&ue(e),de&&"function"===typeof de.setStrictMode)try{de.setStrictMode(ce,e)}catch(n){}}var pe=Math.clz32?Math.clz32:function(e){return 0===(e>>>=0)?32:31-(he(e)/me|0)|0},he=Math.log,me=Math.LN2;var ge=256,ve=4194304;function ye(e){var n=42&e;if(0!==n)return n;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194048&e;case 4194304:case 8388608:case 16777216:case 33554432:return 62914560&e;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function be(e,n,t){var r=e.pendingLanes;if(0===r)return 0;var a=0,i=e.suspendedLanes,o=e.pingedLanes;e=e.warmLanes;var l=134217727&r;return 0!==l?0!==(r=l&~i)?a=ye(r):0!==(o&=l)?a=ye(o):t||0!==(t=l&~e)&&(a=ye(t)):0!==(l=r&~i)?a=ye(l):0!==o?a=ye(o):t||0!==(t=r&~e)&&(a=ye(t)),0===a?0:0!==n&&n!==a&&0===(n&i)&&((i=a&-a)>=(t=n&-n)||32===i&&0!==(4194048&t))?n:a}function we(e,n){return 0===(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&n)}function xe(e,n){switch(e){case 1:case 2:case 4:case 8:case 64:return n+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return n+5e3;default:return-1}}function ke(){var e=ge;return 0===(4194048&(ge<<=1))&&(ge=256),e}function Se(){var e=ve;return 0===(62914560&(ve<<=1))&&(ve=4194304),e}function Ee(e){for(var n=[],t=0;31>t;t++)n.push(e);return n}function Ce(e,n){e.pendingLanes|=n,268435456!==n&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function _e(e,n,t){e.pendingLanes|=n,e.suspendedLanes&=~n;var r=31-pe(n);e.entangledLanes|=n,e.entanglements[r]=1073741824|e.entanglements[r]|4194090&t}function ze(e,n){var t=e.entangledLanes|=n;for(e=e.entanglements;t;){var r=31-pe(t),a=1<<r;a&n|e[r]&n&&(e[r]|=n),t&=~a}}function Pe(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function je(e){return 2<(e&=-e)?8<e?0!==(134217727&e)?32:268435456:8:2}function Te(){var e=L.p;return 0!==e?e:void 0===(e=window.event)?32:cf(e.type)}var Oe=Math.random().toString(36).slice(2),Ne="__reactFiber$"+Oe,Ae="__reactProps$"+Oe,Re="__reactContainer$"+Oe,Le="__reactEvents$"+Oe,Me="__reactListeners$"+Oe,De="__reactHandles$"+Oe,Ie="__reactResources$"+Oe,Fe="__reactMarker$"+Oe;function Ue(e){delete e[Ne],delete e[Ae],delete e[Le],delete e[Me],delete e[De]}function He(e){var n=e[Ne];if(n)return n;for(var t=e.parentNode;t;){if(n=t[Re]||t[Ne]){if(t=n.alternate,null!==n.child||null!==t&&null!==t.child)for(e=bd(e);null!==e;){if(t=e[Ne])return t;e=bd(e)}return n}t=(e=t).parentNode}return null}function We(e){if(e=e[Ne]||e[Re]){var n=e.tag;if(5===n||6===n||13===n||26===n||27===n||3===n)return e}return null}function $e(e){var n=e.tag;if(5===n||26===n||27===n||6===n)return e.stateNode;throw Error(o(33))}function Be(e){var n=e[Ie];return n||(n=e[Ie]={hoistableStyles:new Map,hoistableScripts:new Map}),n}function Ve(e){e[Fe]=!0}var qe=new Set,Ke={};function Qe(e,n){Ye(e,n),Ye(e+"Capture",n)}function Ye(e,n){for(Ke[e]=n,e=0;e<n.length;e++)qe.add(n[e])}var Ge,Xe,Ze=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Je={},en={};function nn(e,n,t){if(a=n,G.call(en,a)||!G.call(Je,a)&&(Ze.test(a)?en[a]=!0:(Je[a]=!0,0)))if(null===t)e.removeAttribute(n);else{switch(typeof t){case"undefined":case"function":case"symbol":return void e.removeAttribute(n);case"boolean":var r=n.toLowerCase().slice(0,5);if("data-"!==r&&"aria-"!==r)return void e.removeAttribute(n)}e.setAttribute(n,""+t)}var a}function tn(e,n,t){if(null===t)e.removeAttribute(n);else{switch(typeof t){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(n)}e.setAttribute(n,""+t)}}function rn(e,n,t,r){if(null===r)e.removeAttribute(t);else{switch(typeof r){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(t)}e.setAttributeNS(n,t,""+r)}}function an(e){if(void 0===Ge)try{throw Error()}catch(t){var n=t.stack.trim().match(/\n( *(at )?)/);Ge=n&&n[1]||"",Xe=-1<t.stack.indexOf("\n    at")?" (<anonymous>)":-1<t.stack.indexOf("@")?"@unknown:0:0":""}return"\n"+Ge+e+Xe}var on=!1;function ln(e,n){if(!e||on)return"";on=!0;var t=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var r={DetermineComponentFrameRoot:function(){try{if(n){var t=function(){throw Error()};if(Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(a){var r=a}Reflect.construct(e,[],t)}else{try{t.call()}catch(i){r=i}e.call(t.prototype)}}else{try{throw Error()}catch(o){r=o}(t=e())&&"function"===typeof t.catch&&t.catch(function(){})}}catch(l){if(l&&r&&"string"===typeof l.stack)return[l.stack,r.stack]}return[null,null]}};r.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var a=Object.getOwnPropertyDescriptor(r.DetermineComponentFrameRoot,"name");a&&a.configurable&&Object.defineProperty(r.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var i=r.DetermineComponentFrameRoot(),o=i[0],l=i[1];if(o&&l){var s=o.split("\n"),u=l.split("\n");for(a=r=0;r<s.length&&!s[r].includes("DetermineComponentFrameRoot");)r++;for(;a<u.length&&!u[a].includes("DetermineComponentFrameRoot");)a++;if(r===s.length||a===u.length)for(r=s.length-1,a=u.length-1;1<=r&&0<=a&&s[r]!==u[a];)a--;for(;1<=r&&0<=a;r--,a--)if(s[r]!==u[a]){if(1!==r||1!==a)do{if(r--,0>--a||s[r]!==u[a]){var c="\n"+s[r].replace(" at new "," at ");return e.displayName&&c.includes("<anonymous>")&&(c=c.replace("<anonymous>",e.displayName)),c}}while(1<=r&&0<=a);break}}}finally{on=!1,Error.prepareStackTrace=t}return(t=e?e.displayName||e.name:"")?an(t):""}function sn(e){switch(e.tag){case 26:case 27:case 5:return an(e.type);case 16:return an("Lazy");case 13:return an("Suspense");case 19:return an("SuspenseList");case 0:case 15:return ln(e.type,!1);case 11:return ln(e.type.render,!1);case 1:return ln(e.type,!0);case 31:return an("Activity");default:return""}}function un(e){try{var n="";do{n+=sn(e),e=e.return}while(e);return n}catch(t){return"\nError generating stack: "+t.message+"\n"+t.stack}}function cn(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function dn(e){var n=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===n||"radio"===n)}function fn(e){e._valueTracker||(e._valueTracker=function(e){var n=dn(e)?"checked":"value",t=Object.getOwnPropertyDescriptor(e.constructor.prototype,n),r=""+e[n];if(!e.hasOwnProperty(n)&&"undefined"!==typeof t&&"function"===typeof t.get&&"function"===typeof t.set){var a=t.get,i=t.set;return Object.defineProperty(e,n,{configurable:!0,get:function(){return a.call(this)},set:function(e){r=""+e,i.call(this,e)}}),Object.defineProperty(e,n,{enumerable:t.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[n]}}}}(e))}function pn(e){if(!e)return!1;var n=e._valueTracker;if(!n)return!0;var t=n.getValue(),r="";return e&&(r=dn(e)?e.checked?"true":"false":e.value),(e=r)!==t&&(n.setValue(e),!0)}function hn(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(n){return e.body}}var mn=/[\n"\\]/g;function gn(e){return e.replace(mn,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function vn(e,n,t,r,a,i,o,l){e.name="",null!=o&&"function"!==typeof o&&"symbol"!==typeof o&&"boolean"!==typeof o?e.type=o:e.removeAttribute("type"),null!=n?"number"===o?(0===n&&""===e.value||e.value!=n)&&(e.value=""+cn(n)):e.value!==""+cn(n)&&(e.value=""+cn(n)):"submit"!==o&&"reset"!==o||e.removeAttribute("value"),null!=n?bn(e,o,cn(n)):null!=t?bn(e,o,cn(t)):null!=r&&e.removeAttribute("value"),null==a&&null!=i&&(e.defaultChecked=!!i),null!=a&&(e.checked=a&&"function"!==typeof a&&"symbol"!==typeof a),null!=l&&"function"!==typeof l&&"symbol"!==typeof l&&"boolean"!==typeof l?e.name=""+cn(l):e.removeAttribute("name")}function yn(e,n,t,r,a,i,o,l){if(null!=i&&"function"!==typeof i&&"symbol"!==typeof i&&"boolean"!==typeof i&&(e.type=i),null!=n||null!=t){if(!("submit"!==i&&"reset"!==i||void 0!==n&&null!==n))return;t=null!=t?""+cn(t):"",n=null!=n?""+cn(n):t,l||n===e.value||(e.value=n),e.defaultValue=n}r="function"!==typeof(r=null!=r?r:a)&&"symbol"!==typeof r&&!!r,e.checked=l?e.checked:!!r,e.defaultChecked=!!r,null!=o&&"function"!==typeof o&&"symbol"!==typeof o&&"boolean"!==typeof o&&(e.name=o)}function bn(e,n,t){"number"===n&&hn(e.ownerDocument)===e||e.defaultValue===""+t||(e.defaultValue=""+t)}function wn(e,n,t,r){if(e=e.options,n){n={};for(var a=0;a<t.length;a++)n["$"+t[a]]=!0;for(t=0;t<e.length;t++)a=n.hasOwnProperty("$"+e[t].value),e[t].selected!==a&&(e[t].selected=a),a&&r&&(e[t].defaultSelected=!0)}else{for(t=""+cn(t),n=null,a=0;a<e.length;a++){if(e[a].value===t)return e[a].selected=!0,void(r&&(e[a].defaultSelected=!0));null!==n||e[a].disabled||(n=e[a])}null!==n&&(n.selected=!0)}}function xn(e,n,t){null==n||((n=""+cn(n))!==e.value&&(e.value=n),null!=t)?e.defaultValue=null!=t?""+cn(t):"":e.defaultValue!==n&&(e.defaultValue=n)}function kn(e,n,t,r){if(null==n){if(null!=r){if(null!=t)throw Error(o(92));if(A(r)){if(1<r.length)throw Error(o(93));r=r[0]}t=r}null==t&&(t=""),n=t}t=cn(n),e.defaultValue=t,(r=e.textContent)===t&&""!==r&&null!==r&&(e.value=r)}function Sn(e,n){if(n){var t=e.firstChild;if(t&&t===e.lastChild&&3===t.nodeType)return void(t.nodeValue=n)}e.textContent=n}var En=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Cn(e,n,t){var r=0===n.indexOf("--");null==t||"boolean"===typeof t||""===t?r?e.setProperty(n,""):"float"===n?e.cssFloat="":e[n]="":r?e.setProperty(n,t):"number"!==typeof t||0===t||En.has(n)?"float"===n?e.cssFloat=t:e[n]=(""+t).trim():e[n]=t+"px"}function _n(e,n,t){if(null!=n&&"object"!==typeof n)throw Error(o(62));if(e=e.style,null!=t){for(var r in t)!t.hasOwnProperty(r)||null!=n&&n.hasOwnProperty(r)||(0===r.indexOf("--")?e.setProperty(r,""):"float"===r?e.cssFloat="":e[r]="");for(var a in n)r=n[a],n.hasOwnProperty(a)&&t[a]!==r&&Cn(e,a,r)}else for(var i in n)n.hasOwnProperty(i)&&Cn(e,i,n[i])}function zn(e){if(-1===e.indexOf("-"))return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Pn=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),jn=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Tn(e){return jn.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var On=null;function Nn(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var An=null,Rn=null;function Ln(e){var n=We(e);if(n&&(e=n.stateNode)){var t=e[Ae]||null;e:switch(e=n.stateNode,n.type){case"input":if(vn(e,t.value,t.defaultValue,t.defaultValue,t.checked,t.defaultChecked,t.type,t.name),n=t.name,"radio"===t.type&&null!=n){for(t=e;t.parentNode;)t=t.parentNode;for(t=t.querySelectorAll('input[name="'+gn(""+n)+'"][type="radio"]'),n=0;n<t.length;n++){var r=t[n];if(r!==e&&r.form===e.form){var a=r[Ae]||null;if(!a)throw Error(o(90));vn(r,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name)}}for(n=0;n<t.length;n++)(r=t[n]).form===e.form&&pn(r)}break e;case"textarea":xn(e,t.value,t.defaultValue);break e;case"select":null!=(n=t.value)&&wn(e,!!t.multiple,n,!1)}}}var Mn=!1;function Dn(e,n,t){if(Mn)return e(n,t);Mn=!0;try{return e(n)}finally{if(Mn=!1,(null!==An||null!==Rn)&&(Hu(),An&&(n=An,e=Rn,Rn=An=null,Ln(n),e)))for(n=0;n<e.length;n++)Ln(e[n])}}function In(e,n){var t=e.stateNode;if(null===t)return null;var r=t[Ae]||null;if(null===r)return null;t=r[n];e:switch(n){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(t&&"function"!==typeof t)throw Error(o(231,n,typeof t));return t}var Fn=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),Un=!1;if(Fn)try{var Hn={};Object.defineProperty(Hn,"passive",{get:function(){Un=!0}}),window.addEventListener("test",Hn,Hn),window.removeEventListener("test",Hn,Hn)}catch(Rf){Un=!1}var Wn=null,$n=null,Bn=null;function Vn(){if(Bn)return Bn;var e,n,t=$n,r=t.length,a="value"in Wn?Wn.value:Wn.textContent,i=a.length;for(e=0;e<r&&t[e]===a[e];e++);var o=r-e;for(n=1;n<=o&&t[r-n]===a[i-n];n++);return Bn=a.slice(e,1<n?1-n:void 0)}function qn(e){var n=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===n&&(e=13):e=n,10===e&&(e=13),32<=e||13===e?e:0}function Kn(){return!0}function Qn(){return!1}function Yn(e){function n(n,t,r,a,i){for(var o in this._reactName=n,this._targetInst=r,this.type=t,this.nativeEvent=a,this.target=i,this.currentTarget=null,e)e.hasOwnProperty(o)&&(n=e[o],this[o]=n?n(a):a[o]);return this.isDefaultPrevented=(null!=a.defaultPrevented?a.defaultPrevented:!1===a.returnValue)?Kn:Qn,this.isPropagationStopped=Qn,this}return f(n.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=Kn)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=Kn)},persist:function(){},isPersistent:Kn}),n}var Gn,Xn,Zn,Jn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},et=Yn(Jn),nt=f({},Jn,{view:0,detail:0}),tt=Yn(nt),rt=f({},nt,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:ht,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Zn&&(Zn&&"mousemove"===e.type?(Gn=e.screenX-Zn.screenX,Xn=e.screenY-Zn.screenY):Xn=Gn=0,Zn=e),Gn)},movementY:function(e){return"movementY"in e?e.movementY:Xn}}),at=Yn(rt),it=Yn(f({},rt,{dataTransfer:0})),ot=Yn(f({},nt,{relatedTarget:0})),lt=Yn(f({},Jn,{animationName:0,elapsedTime:0,pseudoElement:0})),st=Yn(f({},Jn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}})),ut=Yn(f({},Jn,{data:0})),ct={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},dt={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},ft={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function pt(e){var n=this.nativeEvent;return n.getModifierState?n.getModifierState(e):!!(e=ft[e])&&!!n[e]}function ht(){return pt}var mt=Yn(f({},nt,{key:function(e){if(e.key){var n=ct[e.key]||e.key;if("Unidentified"!==n)return n}return"keypress"===e.type?13===(e=qn(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?dt[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:ht,charCode:function(e){return"keypress"===e.type?qn(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?qn(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}})),gt=Yn(f({},rt,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),vt=Yn(f({},nt,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:ht})),yt=Yn(f({},Jn,{propertyName:0,elapsedTime:0,pseudoElement:0})),bt=Yn(f({},rt,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0})),wt=Yn(f({},Jn,{newState:0,oldState:0})),xt=[9,13,27,32],kt=Fn&&"CompositionEvent"in window,St=null;Fn&&"documentMode"in document&&(St=document.documentMode);var Et=Fn&&"TextEvent"in window&&!St,Ct=Fn&&(!kt||St&&8<St&&11>=St),_t=String.fromCharCode(32),zt=!1;function Pt(e,n){switch(e){case"keyup":return-1!==xt.indexOf(n.keyCode);case"keydown":return 229!==n.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function jt(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var Tt=!1;var Ot={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Nt(e){var n=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===n?!!Ot[e.type]:"textarea"===n}function At(e,n,t,r){An?Rn?Rn.push(r):Rn=[r]:An=r,0<(n=Bc(n,"onChange")).length&&(t=new et("onChange","change",null,t,r),e.push({event:t,listeners:n}))}var Rt=null,Lt=null;function Mt(e){Mc(e,0)}function Dt(e){if(pn($e(e)))return e}function It(e,n){if("change"===e)return n}var Ft=!1;if(Fn){var Ut;if(Fn){var Ht="oninput"in document;if(!Ht){var Wt=document.createElement("div");Wt.setAttribute("oninput","return;"),Ht="function"===typeof Wt.oninput}Ut=Ht}else Ut=!1;Ft=Ut&&(!document.documentMode||9<document.documentMode)}function $t(){Rt&&(Rt.detachEvent("onpropertychange",Bt),Lt=Rt=null)}function Bt(e){if("value"===e.propertyName&&Dt(Lt)){var n=[];At(n,Lt,e,Nn(e)),Dn(Mt,n)}}function Vt(e,n,t){"focusin"===e?($t(),Lt=t,(Rt=n).attachEvent("onpropertychange",Bt)):"focusout"===e&&$t()}function qt(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Dt(Lt)}function Kt(e,n){if("click"===e)return Dt(n)}function Qt(e,n){if("input"===e||"change"===e)return Dt(n)}var Yt="function"===typeof Object.is?Object.is:function(e,n){return e===n&&(0!==e||1/e===1/n)||e!==e&&n!==n};function Gt(e,n){if(Yt(e,n))return!0;if("object"!==typeof e||null===e||"object"!==typeof n||null===n)return!1;var t=Object.keys(e),r=Object.keys(n);if(t.length!==r.length)return!1;for(r=0;r<t.length;r++){var a=t[r];if(!G.call(n,a)||!Yt(e[a],n[a]))return!1}return!0}function Xt(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Zt(e,n){var t,r=Xt(e);for(e=0;r;){if(3===r.nodeType){if(t=e+r.textContent.length,e<=n&&t>=n)return{node:r,offset:n-e};e=t}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=Xt(r)}}function Jt(e,n){return!(!e||!n)&&(e===n||(!e||3!==e.nodeType)&&(n&&3===n.nodeType?Jt(e,n.parentNode):"contains"in e?e.contains(n):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(n))))}function er(e){for(var n=hn((e=null!=e&&null!=e.ownerDocument&&null!=e.ownerDocument.defaultView?e.ownerDocument.defaultView:window).document);n instanceof e.HTMLIFrameElement;){try{var t="string"===typeof n.contentWindow.location.href}catch(r){t=!1}if(!t)break;n=hn((e=n.contentWindow).document)}return n}function nr(e){var n=e&&e.nodeName&&e.nodeName.toLowerCase();return n&&("input"===n&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===n||"true"===e.contentEditable)}var tr=Fn&&"documentMode"in document&&11>=document.documentMode,rr=null,ar=null,ir=null,or=!1;function lr(e,n,t){var r=t.window===t?t.document:9===t.nodeType?t:t.ownerDocument;or||null==rr||rr!==hn(r)||("selectionStart"in(r=rr)&&nr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},ir&&Gt(ir,r)||(ir=r,0<(r=Bc(ar,"onSelect")).length&&(n=new et("onSelect","select",null,n,t),e.push({event:n,listeners:r}),n.target=rr)))}function sr(e,n){var t={};return t[e.toLowerCase()]=n.toLowerCase(),t["Webkit"+e]="webkit"+n,t["Moz"+e]="moz"+n,t}var ur={animationend:sr("Animation","AnimationEnd"),animationiteration:sr("Animation","AnimationIteration"),animationstart:sr("Animation","AnimationStart"),transitionrun:sr("Transition","TransitionRun"),transitionstart:sr("Transition","TransitionStart"),transitioncancel:sr("Transition","TransitionCancel"),transitionend:sr("Transition","TransitionEnd")},cr={},dr={};function fr(e){if(cr[e])return cr[e];if(!ur[e])return e;var n,t=ur[e];for(n in t)if(t.hasOwnProperty(n)&&n in dr)return cr[e]=t[n];return e}Fn&&(dr=document.createElement("div").style,"AnimationEvent"in window||(delete ur.animationend.animation,delete ur.animationiteration.animation,delete ur.animationstart.animation),"TransitionEvent"in window||delete ur.transitionend.transition);var pr=fr("animationend"),hr=fr("animationiteration"),mr=fr("animationstart"),gr=fr("transitionrun"),vr=fr("transitionstart"),yr=fr("transitioncancel"),br=fr("transitionend"),wr=new Map,xr="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function kr(e,n){wr.set(e,n),Qe(n,[e])}xr.push("scrollEnd");var Sr=new WeakMap;function Er(e,n){if("object"===typeof e&&null!==e){var t=Sr.get(e);return void 0!==t?t:(n={value:e,source:n,stack:un(n)},Sr.set(e,n),n)}return{value:e,source:n,stack:un(n)}}var Cr=[],_r=0,zr=0;function Pr(){for(var e=_r,n=zr=_r=0;n<e;){var t=Cr[n];Cr[n++]=null;var r=Cr[n];Cr[n++]=null;var a=Cr[n];Cr[n++]=null;var i=Cr[n];if(Cr[n++]=null,null!==r&&null!==a){var o=r.pending;null===o?a.next=a:(a.next=o.next,o.next=a),r.pending=a}0!==i&&Nr(t,a,i)}}function jr(e,n,t,r){Cr[_r++]=e,Cr[_r++]=n,Cr[_r++]=t,Cr[_r++]=r,zr|=r,e.lanes|=r,null!==(e=e.alternate)&&(e.lanes|=r)}function Tr(e,n,t,r){return jr(e,n,t,r),Ar(e)}function Or(e,n){return jr(e,null,null,n),Ar(e)}function Nr(e,n,t){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t);for(var a=!1,i=e.return;null!==i;)i.childLanes|=t,null!==(r=i.alternate)&&(r.childLanes|=t),22===i.tag&&(null===(e=i.stateNode)||1&e._visibility||(a=!0)),e=i,i=i.return;return 3===e.tag?(i=e.stateNode,a&&null!==n&&(a=31-pe(t),null===(r=(e=i.hiddenUpdates)[a])?e[a]=[n]:r.push(n),n.lane=536870912|t),i):null}function Ar(e){if(50<Nu)throw Nu=0,Au=null,Error(o(185));for(var n=e.return;null!==n;)n=(e=n).return;return 3===e.tag?e.stateNode:null}var Rr={};function Lr(e,n,t,r){this.tag=e,this.key=t,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=n,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Mr(e,n,t,r){return new Lr(e,n,t,r)}function Dr(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Ir(e,n){var t=e.alternate;return null===t?((t=Mr(e.tag,n,e.key,e.mode)).elementType=e.elementType,t.type=e.type,t.stateNode=e.stateNode,t.alternate=e,e.alternate=t):(t.pendingProps=n,t.type=e.type,t.flags=0,t.subtreeFlags=0,t.deletions=null),t.flags=65011712&e.flags,t.childLanes=e.childLanes,t.lanes=e.lanes,t.child=e.child,t.memoizedProps=e.memoizedProps,t.memoizedState=e.memoizedState,t.updateQueue=e.updateQueue,n=e.dependencies,t.dependencies=null===n?null:{lanes:n.lanes,firstContext:n.firstContext},t.sibling=e.sibling,t.index=e.index,t.ref=e.ref,t.refCleanup=e.refCleanup,t}function Fr(e,n){e.flags&=65011714;var t=e.alternate;return null===t?(e.childLanes=0,e.lanes=n,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=t.childLanes,e.lanes=t.lanes,e.child=t.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=t.memoizedProps,e.memoizedState=t.memoizedState,e.updateQueue=t.updateQueue,e.type=t.type,n=t.dependencies,e.dependencies=null===n?null:{lanes:n.lanes,firstContext:n.firstContext}),e}function Ur(e,n,t,r,a,i){var l=0;if(r=e,"function"===typeof e)Dr(e)&&(l=1);else if("string"===typeof e)l=function(e,n,t){if(1===t||null!=n.itemProp)return!1;switch(e){case"meta":case"title":return!0;case"style":if("string"!==typeof n.precedence||"string"!==typeof n.href||""===n.href)break;return!0;case"link":if("string"!==typeof n.rel||"string"!==typeof n.href||""===n.href||n.onLoad||n.onError)break;return"stylesheet"!==n.rel||(e=n.disabled,"string"===typeof n.precedence&&null==e);case"script":if(n.async&&"function"!==typeof n.async&&"symbol"!==typeof n.async&&!n.onLoad&&!n.onError&&n.src&&"string"===typeof n.src)return!0}return!1}(e,t,W.current)?26:"html"===e||"head"===e||"body"===e?27:5;else e:switch(e){case z:return(e=Mr(31,t,n,a)).elementType=z,e.lanes=i,e;case g:return Hr(t.children,a,i,n);case v:l=8,a|=24;break;case y:return(e=Mr(12,t,n,2|a)).elementType=y,e.lanes=i,e;case S:return(e=Mr(13,t,n,a)).elementType=S,e.lanes=i,e;case E:return(e=Mr(19,t,n,a)).elementType=E,e.lanes=i,e;default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case b:case x:l=10;break e;case w:l=9;break e;case k:l=11;break e;case C:l=14;break e;case _:l=16,r=null;break e}l=29,t=Error(o(130,null===e?"null":typeof e,"")),r=null}return(n=Mr(l,t,n,a)).elementType=e,n.type=r,n.lanes=i,n}function Hr(e,n,t,r){return(e=Mr(7,e,r,n)).lanes=t,e}function Wr(e,n,t){return(e=Mr(6,e,null,n)).lanes=t,e}function $r(e,n,t){return(n=Mr(4,null!==e.children?e.children:[],e.key,n)).lanes=t,n.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},n}var Br=[],Vr=0,qr=null,Kr=0,Qr=[],Yr=0,Gr=null,Xr=1,Zr="";function Jr(e,n){Br[Vr++]=Kr,Br[Vr++]=qr,qr=e,Kr=n}function ea(e,n,t){Qr[Yr++]=Xr,Qr[Yr++]=Zr,Qr[Yr++]=Gr,Gr=e;var r=Xr;e=Zr;var a=32-pe(r)-1;r&=~(1<<a),t+=1;var i=32-pe(n)+a;if(30<i){var o=a-a%5;i=(r&(1<<o)-1).toString(32),r>>=o,a-=o,Xr=1<<32-pe(n)+a|t<<a|r,Zr=i+e}else Xr=1<<i|t<<a|r,Zr=e}function na(e){null!==e.return&&(Jr(e,1),ea(e,1,0))}function ta(e){for(;e===qr;)qr=Br[--Vr],Br[Vr]=null,Kr=Br[--Vr],Br[Vr]=null;for(;e===Gr;)Gr=Qr[--Yr],Qr[Yr]=null,Zr=Qr[--Yr],Qr[Yr]=null,Xr=Qr[--Yr],Qr[Yr]=null}var ra=null,aa=null,ia=!1,oa=null,la=!1,sa=Error(o(519));function ua(e){throw ma(Er(Error(o(418,"")),e)),sa}function ca(e){var n=e.stateNode,t=e.type,r=e.memoizedProps;switch(n[Ne]=e,n[Ae]=r,t){case"dialog":Dc("cancel",n),Dc("close",n);break;case"iframe":case"object":case"embed":Dc("load",n);break;case"video":case"audio":for(t=0;t<Rc.length;t++)Dc(Rc[t],n);break;case"source":Dc("error",n);break;case"img":case"image":case"link":Dc("error",n),Dc("load",n);break;case"details":Dc("toggle",n);break;case"input":Dc("invalid",n),yn(n,r.value,r.defaultValue,r.checked,r.defaultChecked,r.type,r.name,!0),fn(n);break;case"select":Dc("invalid",n);break;case"textarea":Dc("invalid",n),kn(n,r.value,r.defaultValue,r.children),fn(n)}"string"!==typeof(t=r.children)&&"number"!==typeof t&&"bigint"!==typeof t||n.textContent===""+t||!0===r.suppressHydrationWarning||Gc(n.textContent,t)?(null!=r.popover&&(Dc("beforetoggle",n),Dc("toggle",n)),null!=r.onScroll&&Dc("scroll",n),null!=r.onScrollEnd&&Dc("scrollend",n),null!=r.onClick&&(n.onclick=Xc),n=!0):n=!1,n||ua(e)}function da(e){for(ra=e.return;ra;)switch(ra.tag){case 5:case 13:return void(la=!1);case 27:case 3:return void(la=!0);default:ra=ra.return}}function fa(e){if(e!==ra)return!1;if(!ia)return da(e),ia=!0,!1;var n,t=e.tag;if((n=3!==t&&27!==t)&&((n=5===t)&&(n=!("form"!==(n=e.type)&&"button"!==n)||od(e.type,e.memoizedProps)),n=!n),n&&aa&&ua(e),da(e),13===t){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(o(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType)if("/$"===(n=e.data)){if(0===t){aa=vd(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++;e=e.nextSibling}aa=null}}else 27===t?(t=aa,pd(e.type)?(e=yd,yd=null,aa=e):aa=t):aa=ra?vd(e.stateNode.nextSibling):null;return!0}function pa(){aa=ra=null,ia=!1}function ha(){var e=oa;return null!==e&&(null===bu?bu=e:bu.push.apply(bu,e),oa=null),e}function ma(e){null===oa?oa=[e]:oa.push(e)}var ga=F(null),va=null,ya=null;function ba(e,n,t){H(ga,n._currentValue),n._currentValue=t}function wa(e){e._currentValue=ga.current,U(ga)}function xa(e,n,t){for(;null!==e;){var r=e.alternate;if((e.childLanes&n)!==n?(e.childLanes|=n,null!==r&&(r.childLanes|=n)):null!==r&&(r.childLanes&n)!==n&&(r.childLanes|=n),e===t)break;e=e.return}}function ka(e,n,t,r){var a=e.child;for(null!==a&&(a.return=e);null!==a;){var i=a.dependencies;if(null!==i){var l=a.child;i=i.firstContext;e:for(;null!==i;){var s=i;i=a;for(var u=0;u<n.length;u++)if(s.context===n[u]){i.lanes|=t,null!==(s=i.alternate)&&(s.lanes|=t),xa(i.return,t,e),r||(l=null);break e}i=s.next}}else if(18===a.tag){if(null===(l=a.return))throw Error(o(341));l.lanes|=t,null!==(i=l.alternate)&&(i.lanes|=t),xa(l,t,e),l=null}else l=a.child;if(null!==l)l.return=a;else for(l=a;null!==l;){if(l===e){l=null;break}if(null!==(a=l.sibling)){a.return=l.return,l=a;break}l=l.return}a=l}}function Sa(e,n,t,r){e=null;for(var a=n,i=!1;null!==a;){if(!i)if(0!==(524288&a.flags))i=!0;else if(0!==(262144&a.flags))break;if(10===a.tag){var l=a.alternate;if(null===l)throw Error(o(387));if(null!==(l=l.memoizedProps)){var s=a.type;Yt(a.pendingProps.value,l.value)||(null!==e?e.push(s):e=[s])}}else if(a===V.current){if(null===(l=a.alternate))throw Error(o(387));l.memoizedState.memoizedState!==a.memoizedState.memoizedState&&(null!==e?e.push(Qd):e=[Qd])}a=a.return}null!==e&&ka(n,e,t,r),n.flags|=262144}function Ea(e){for(e=e.firstContext;null!==e;){if(!Yt(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Ca(e){va=e,ya=null,null!==(e=e.dependencies)&&(e.firstContext=null)}function _a(e){return Pa(va,e)}function za(e,n){return null===va&&Ca(e),Pa(e,n)}function Pa(e,n){var t=n._currentValue;if(n={context:n,memoizedValue:t,next:null},null===ya){if(null===e)throw Error(o(308));ya=n,e.dependencies={lanes:0,firstContext:n},e.flags|=524288}else ya=ya.next=n;return t}var ja="undefined"!==typeof AbortController?AbortController:function(){var e=[],n=this.signal={aborted:!1,addEventListener:function(n,t){e.push(t)}};this.abort=function(){n.aborted=!0,e.forEach(function(e){return e()})}},Ta=r.unstable_scheduleCallback,Oa=r.unstable_NormalPriority,Na={$$typeof:x,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Aa(){return{controller:new ja,data:new Map,refCount:0}}function Ra(e){e.refCount--,0===e.refCount&&Ta(Oa,function(){e.controller.abort()})}var La=null,Ma=0,Da=0,Ia=null;function Fa(){if(0===--Ma&&null!==La){null!==Ia&&(Ia.status="fulfilled");var e=La;La=null,Da=0,Ia=null;for(var n=0;n<e.length;n++)(0,e[n])()}}var Ua=R.S;R.S=function(e,n){"object"===typeof n&&null!==n&&"function"===typeof n.then&&function(e,n){if(null===La){var t=La=[];Ma=0,Da=jc(),Ia={status:"pending",value:void 0,then:function(e){t.push(e)}}}Ma++,n.then(Fa,Fa)}(0,n),null!==Ua&&Ua(e,n)};var Ha=F(null);function Wa(){var e=Ha.current;return null!==e?e:ru.pooledCache}function $a(e,n){H(Ha,null===n?Ha.current:n.pool)}function Ba(){var e=Wa();return null===e?null:{parent:Na._currentValue,pool:e}}var Va=Error(o(460)),qa=Error(o(474)),Ka=Error(o(542)),Qa={then:function(){}};function Ya(e){return"fulfilled"===(e=e.status)||"rejected"===e}function Ga(){}function Xa(e,n,t){switch(void 0===(t=e[t])?e.push(n):t!==n&&(n.then(Ga,Ga),n=t),n.status){case"fulfilled":return n.value;case"rejected":throw ei(e=n.reason),e;default:if("string"===typeof n.status)n.then(Ga,Ga);else{if(null!==(e=ru)&&100<e.shellSuspendCounter)throw Error(o(482));(e=n).status="pending",e.then(function(e){if("pending"===n.status){var t=n;t.status="fulfilled",t.value=e}},function(e){if("pending"===n.status){var t=n;t.status="rejected",t.reason=e}})}switch(n.status){case"fulfilled":return n.value;case"rejected":throw ei(e=n.reason),e}throw Za=n,Va}}var Za=null;function Ja(){if(null===Za)throw Error(o(459));var e=Za;return Za=null,e}function ei(e){if(e===Va||e===Ka)throw Error(o(483))}var ni=!1;function ti(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function ri(e,n){e=e.updateQueue,n.updateQueue===e&&(n.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function ai(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function ii(e,n,t){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!==(2&tu)){var a=r.pending;return null===a?n.next=n:(n.next=a.next,a.next=n),r.pending=n,n=Ar(e),Nr(e,null,t),n}return jr(e,r,n,t),Ar(e)}function oi(e,n,t){if(null!==(n=n.updateQueue)&&(n=n.shared,0!==(4194048&t))){var r=n.lanes;t|=r&=e.pendingLanes,n.lanes=t,ze(e,t)}}function li(e,n){var t=e.updateQueue,r=e.alternate;if(null!==r&&t===(r=r.updateQueue)){var a=null,i=null;if(null!==(t=t.firstBaseUpdate)){do{var o={lane:t.lane,tag:t.tag,payload:t.payload,callback:null,next:null};null===i?a=i=o:i=i.next=o,t=t.next}while(null!==t);null===i?a=i=n:i=i.next=n}else a=i=n;return t={baseState:r.baseState,firstBaseUpdate:a,lastBaseUpdate:i,shared:r.shared,callbacks:r.callbacks},void(e.updateQueue=t)}null===(e=t.lastBaseUpdate)?t.firstBaseUpdate=n:e.next=n,t.lastBaseUpdate=n}var si=!1;function ui(){if(si){if(null!==Ia)throw Ia}}function ci(e,n,t,r){si=!1;var a=e.updateQueue;ni=!1;var i=a.firstBaseUpdate,o=a.lastBaseUpdate,l=a.shared.pending;if(null!==l){a.shared.pending=null;var s=l,u=s.next;s.next=null,null===o?i=u:o.next=u,o=s;var c=e.alternate;null!==c&&((l=(c=c.updateQueue).lastBaseUpdate)!==o&&(null===l?c.firstBaseUpdate=u:l.next=u,c.lastBaseUpdate=s))}if(null!==i){var d=a.baseState;for(o=0,c=u=s=null,l=i;;){var p=-536870913&l.lane,h=p!==l.lane;if(h?(iu&p)===p:(r&p)===p){0!==p&&p===Da&&(si=!0),null!==c&&(c=c.next={lane:0,tag:l.tag,payload:l.payload,callback:null,next:null});e:{var m=e,g=l;p=n;var v=t;switch(g.tag){case 1:if("function"===typeof(m=g.payload)){d=m.call(v,d,p);break e}d=m;break e;case 3:m.flags=-65537&m.flags|128;case 0:if(null===(p="function"===typeof(m=g.payload)?m.call(v,d,p):m)||void 0===p)break e;d=f({},d,p);break e;case 2:ni=!0}}null!==(p=l.callback)&&(e.flags|=64,h&&(e.flags|=8192),null===(h=a.callbacks)?a.callbacks=[p]:h.push(p))}else h={lane:p,tag:l.tag,payload:l.payload,callback:l.callback,next:null},null===c?(u=c=h,s=d):c=c.next=h,o|=p;if(null===(l=l.next)){if(null===(l=a.shared.pending))break;l=(h=l).next,h.next=null,a.lastBaseUpdate=h,a.shared.pending=null}}null===c&&(s=d),a.baseState=s,a.firstBaseUpdate=u,a.lastBaseUpdate=c,null===i&&(a.shared.lanes=0),pu|=o,e.lanes=o,e.memoizedState=d}}function di(e,n){if("function"!==typeof e)throw Error(o(191,e));e.call(n)}function fi(e,n){var t=e.callbacks;if(null!==t)for(e.callbacks=null,e=0;e<t.length;e++)di(t[e],n)}var pi=F(null),hi=F(0);function mi(e,n){H(hi,e=du),H(pi,n),du=e|n.baseLanes}function gi(){H(hi,du),H(pi,pi.current)}function vi(){du=hi.current,U(pi),U(hi)}var yi=0,bi=null,wi=null,xi=null,ki=!1,Si=!1,Ei=!1,Ci=0,_i=0,zi=null,Pi=0;function ji(){throw Error(o(321))}function Ti(e,n){if(null===n)return!1;for(var t=0;t<n.length&&t<e.length;t++)if(!Yt(e[t],n[t]))return!1;return!0}function Oi(e,n,t,r,a,i){return yi=i,bi=n,n.memoizedState=null,n.updateQueue=null,n.lanes=0,R.H=null===e||null===e.memoizedState?qo:Ko,Ei=!1,i=t(r,a),Ei=!1,Si&&(i=Ai(n,t,r,a)),Ni(e),i}function Ni(e){R.H=Vo;var n=null!==wi&&null!==wi.next;if(yi=0,xi=wi=bi=null,ki=!1,_i=0,zi=null,n)throw Error(o(300));null===e||zl||null!==(e=e.dependencies)&&Ea(e)&&(zl=!0)}function Ai(e,n,t,r){bi=e;var a=0;do{if(Si&&(zi=null),_i=0,Si=!1,25<=a)throw Error(o(301));if(a+=1,xi=wi=null,null!=e.updateQueue){var i=e.updateQueue;i.lastEffect=null,i.events=null,i.stores=null,null!=i.memoCache&&(i.memoCache.index=0)}R.H=Qo,i=n(t,r)}while(Si);return i}function Ri(){var e=R.H,n=e.useState()[0];return n="function"===typeof n.then?Ui(n):n,e=e.useState()[0],(null!==wi?wi.memoizedState:null)!==e&&(bi.flags|=1024),n}function Li(){var e=0!==Ci;return Ci=0,e}function Mi(e,n,t){n.updateQueue=e.updateQueue,n.flags&=-2053,e.lanes&=~t}function Di(e){if(ki){for(e=e.memoizedState;null!==e;){var n=e.queue;null!==n&&(n.pending=null),e=e.next}ki=!1}yi=0,xi=wi=bi=null,Si=!1,_i=Ci=0,zi=null}function Ii(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===xi?bi.memoizedState=xi=e:xi=xi.next=e,xi}function Fi(){if(null===wi){var e=bi.alternate;e=null!==e?e.memoizedState:null}else e=wi.next;var n=null===xi?bi.memoizedState:xi.next;if(null!==n)xi=n,wi=e;else{if(null===e){if(null===bi.alternate)throw Error(o(467));throw Error(o(310))}e={memoizedState:(wi=e).memoizedState,baseState:wi.baseState,baseQueue:wi.baseQueue,queue:wi.queue,next:null},null===xi?bi.memoizedState=xi=e:xi=xi.next=e}return xi}function Ui(e){var n=_i;return _i+=1,null===zi&&(zi=[]),e=Xa(zi,e,n),n=bi,null===(null===xi?n.memoizedState:xi.next)&&(n=n.alternate,R.H=null===n||null===n.memoizedState?qo:Ko),e}function Hi(e){if(null!==e&&"object"===typeof e){if("function"===typeof e.then)return Ui(e);if(e.$$typeof===x)return _a(e)}throw Error(o(438,String(e)))}function Wi(e){var n=null,t=bi.updateQueue;if(null!==t&&(n=t.memoCache),null==n){var r=bi.alternate;null!==r&&(null!==(r=r.updateQueue)&&(null!=(r=r.memoCache)&&(n={data:r.data.map(function(e){return e.slice()}),index:0})))}if(null==n&&(n={data:[],index:0}),null===t&&(t={lastEffect:null,events:null,stores:null,memoCache:null},bi.updateQueue=t),t.memoCache=n,void 0===(t=n.data[n.index]))for(t=n.data[n.index]=Array(e),r=0;r<e;r++)t[r]=P;return n.index++,t}function $i(e,n){return"function"===typeof n?n(e):n}function Bi(e){return Vi(Fi(),wi,e)}function Vi(e,n,t){var r=e.queue;if(null===r)throw Error(o(311));r.lastRenderedReducer=t;var a=e.baseQueue,i=r.pending;if(null!==i){if(null!==a){var l=a.next;a.next=i.next,i.next=l}n.baseQueue=a=i,r.pending=null}if(i=e.baseState,null===a)e.memoizedState=i;else{var s=l=null,u=null,c=n=a.next,d=!1;do{var f=-536870913&c.lane;if(f!==c.lane?(iu&f)===f:(yi&f)===f){var p=c.revertLane;if(0===p)null!==u&&(u=u.next={lane:0,revertLane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),f===Da&&(d=!0);else{if((yi&p)===p){c=c.next,p===Da&&(d=!0);continue}f={lane:0,revertLane:c.revertLane,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null},null===u?(s=u=f,l=i):u=u.next=f,bi.lanes|=p,pu|=p}f=c.action,Ei&&t(i,f),i=c.hasEagerState?c.eagerState:t(i,f)}else p={lane:f,revertLane:c.revertLane,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null},null===u?(s=u=p,l=i):u=u.next=p,bi.lanes|=f,pu|=f;c=c.next}while(null!==c&&c!==n);if(null===u?l=i:u.next=s,!Yt(i,e.memoizedState)&&(zl=!0,d&&null!==(t=Ia)))throw t;e.memoizedState=i,e.baseState=l,e.baseQueue=u,r.lastRenderedState=i}return null===a&&(r.lanes=0),[e.memoizedState,r.dispatch]}function qi(e){var n=Fi(),t=n.queue;if(null===t)throw Error(o(311));t.lastRenderedReducer=e;var r=t.dispatch,a=t.pending,i=n.memoizedState;if(null!==a){t.pending=null;var l=a=a.next;do{i=e(i,l.action),l=l.next}while(l!==a);Yt(i,n.memoizedState)||(zl=!0),n.memoizedState=i,null===n.baseQueue&&(n.baseState=i),t.lastRenderedState=i}return[i,r]}function Ki(e,n,t){var r=bi,a=Fi(),i=ia;if(i){if(void 0===t)throw Error(o(407));t=t()}else t=n();var l=!Yt((wi||a).memoizedState,t);if(l&&(a.memoizedState=t,zl=!0),a=a.queue,vo(2048,8,Gi.bind(null,r,a,e),[e]),a.getSnapshot!==n||l||null!==xi&&1&xi.memoizedState.tag){if(r.flags|=2048,ho(9,{destroy:void 0,resource:void 0},Yi.bind(null,r,a,t,n),null),null===ru)throw Error(o(349));i||0!==(124&yi)||Qi(r,n,t)}return t}function Qi(e,n,t){e.flags|=16384,e={getSnapshot:n,value:t},null===(n=bi.updateQueue)?(n={lastEffect:null,events:null,stores:null,memoCache:null},bi.updateQueue=n,n.stores=[e]):null===(t=n.stores)?n.stores=[e]:t.push(e)}function Yi(e,n,t,r){n.value=t,n.getSnapshot=r,Xi(n)&&Zi(e)}function Gi(e,n,t){return t(function(){Xi(n)&&Zi(e)})}function Xi(e){var n=e.getSnapshot;e=e.value;try{var t=n();return!Yt(e,t)}catch(r){return!0}}function Zi(e){var n=Or(e,2);null!==n&&Mu(n,e,2)}function Ji(e){var n=Ii();if("function"===typeof e){var t=e;if(e=t(),Ei){fe(!0);try{t()}finally{fe(!1)}}}return n.memoizedState=n.baseState=e,n.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:$i,lastRenderedState:e},n}function eo(e,n,t,r){return e.baseState=t,Vi(e,wi,"function"===typeof r?r:$i)}function no(e,n,t,r,a){if(Wo(e))throw Error(o(485));if(null!==(e=n.action)){var i={payload:a,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(e){i.listeners.push(e)}};null!==R.T?t(!0):i.isTransition=!1,r(i),null===(t=n.pending)?(i.next=n.pending=i,to(n,i)):(i.next=t.next,n.pending=t.next=i)}}function to(e,n){var t=n.action,r=n.payload,a=e.state;if(n.isTransition){var i=R.T,o={};R.T=o;try{var l=t(a,r),s=R.S;null!==s&&s(o,l),ro(e,n,l)}catch(u){io(e,n,u)}finally{R.T=i}}else try{ro(e,n,i=t(a,r))}catch(c){io(e,n,c)}}function ro(e,n,t){null!==t&&"object"===typeof t&&"function"===typeof t.then?t.then(function(t){ao(e,n,t)},function(t){return io(e,n,t)}):ao(e,n,t)}function ao(e,n,t){n.status="fulfilled",n.value=t,oo(n),e.state=t,null!==(n=e.pending)&&((t=n.next)===n?e.pending=null:(t=t.next,n.next=t,to(e,t)))}function io(e,n,t){var r=e.pending;if(e.pending=null,null!==r){r=r.next;do{n.status="rejected",n.reason=t,oo(n),n=n.next}while(n!==r)}e.action=null}function oo(e){e=e.listeners;for(var n=0;n<e.length;n++)(0,e[n])()}function lo(e,n){return n}function so(e,n){if(ia){var t=ru.formState;if(null!==t){e:{var r=bi;if(ia){if(aa){n:{for(var a=aa,i=la;8!==a.nodeType;){if(!i){a=null;break n}if(null===(a=vd(a.nextSibling))){a=null;break n}}a="F!"===(i=a.data)||"F"===i?a:null}if(a){aa=vd(a.nextSibling),r="F!"===a.data;break e}}ua(r)}r=!1}r&&(n=t[0])}}return(t=Ii()).memoizedState=t.baseState=n,r={pending:null,lanes:0,dispatch:null,lastRenderedReducer:lo,lastRenderedState:n},t.queue=r,t=Fo.bind(null,bi,r),r.dispatch=t,r=Ji(!1),i=Ho.bind(null,bi,!1,r.queue),a={state:n,dispatch:null,action:e,pending:null},(r=Ii()).queue=a,t=no.bind(null,bi,a,i,t),a.dispatch=t,r.memoizedState=e,[n,t,!1]}function uo(e){return co(Fi(),wi,e)}function co(e,n,t){if(n=Vi(e,n,lo)[0],e=Bi($i)[0],"object"===typeof n&&null!==n&&"function"===typeof n.then)try{var r=Ui(n)}catch(o){if(o===Va)throw Ka;throw o}else r=n;var a=(n=Fi()).queue,i=a.dispatch;return t!==n.memoizedState&&(bi.flags|=2048,ho(9,{destroy:void 0,resource:void 0},fo.bind(null,a,t),null)),[r,i,e]}function fo(e,n){e.action=n}function po(e){var n=Fi(),t=wi;if(null!==t)return co(n,t,e);Fi(),n=n.memoizedState;var r=(t=Fi()).queue.dispatch;return t.memoizedState=e,[n,r,!1]}function ho(e,n,t,r){return e={tag:e,create:t,deps:r,inst:n,next:null},null===(n=bi.updateQueue)&&(n={lastEffect:null,events:null,stores:null,memoCache:null},bi.updateQueue=n),null===(t=n.lastEffect)?n.lastEffect=e.next=e:(r=t.next,t.next=e,e.next=r,n.lastEffect=e),e}function mo(){return Fi().memoizedState}function go(e,n,t,r){var a=Ii();r=void 0===r?null:r,bi.flags|=e,a.memoizedState=ho(1|n,{destroy:void 0,resource:void 0},t,r)}function vo(e,n,t,r){var a=Fi();r=void 0===r?null:r;var i=a.memoizedState.inst;null!==wi&&null!==r&&Ti(r,wi.memoizedState.deps)?a.memoizedState=ho(n,i,t,r):(bi.flags|=e,a.memoizedState=ho(1|n,i,t,r))}function yo(e,n){go(8390656,8,e,n)}function bo(e,n){vo(2048,8,e,n)}function wo(e,n){return vo(4,2,e,n)}function xo(e,n){return vo(4,4,e,n)}function ko(e,n){if("function"===typeof n){e=e();var t=n(e);return function(){"function"===typeof t?t():n(null)}}if(null!==n&&void 0!==n)return e=e(),n.current=e,function(){n.current=null}}function So(e,n,t){t=null!==t&&void 0!==t?t.concat([e]):null,vo(4,4,ko.bind(null,n,e),t)}function Eo(){}function Co(e,n){var t=Fi();n=void 0===n?null:n;var r=t.memoizedState;return null!==n&&Ti(n,r[1])?r[0]:(t.memoizedState=[e,n],e)}function _o(e,n){var t=Fi();n=void 0===n?null:n;var r=t.memoizedState;if(null!==n&&Ti(n,r[1]))return r[0];if(r=e(),Ei){fe(!0);try{e()}finally{fe(!1)}}return t.memoizedState=[r,n],r}function zo(e,n,t){return void 0===t||0!==(1073741824&yi)?e.memoizedState=n:(e.memoizedState=t,e=Lu(),bi.lanes|=e,pu|=e,t)}function Po(e,n,t,r){return Yt(t,n)?t:null!==pi.current?(e=zo(e,t,r),Yt(e,n)||(zl=!0),e):0===(42&yi)?(zl=!0,e.memoizedState=t):(e=Lu(),bi.lanes|=e,pu|=e,n)}function jo(e,n,t,r,a){var i=L.p;L.p=0!==i&&8>i?i:8;var o=R.T,l={};R.T=l,Ho(e,!1,n,t);try{var s=a(),u=R.S;if(null!==u&&u(l,s),null!==s&&"object"===typeof s&&"function"===typeof s.then)Uo(e,n,function(e,n){var t=[],r={status:"pending",value:null,reason:null,then:function(e){t.push(e)}};return e.then(function(){r.status="fulfilled",r.value=n;for(var e=0;e<t.length;e++)(0,t[e])(n)},function(e){for(r.status="rejected",r.reason=e,e=0;e<t.length;e++)(0,t[e])(void 0)}),r}(s,r),Ru());else Uo(e,n,r,Ru())}catch(c){Uo(e,n,{then:function(){},status:"rejected",reason:c},Ru())}finally{L.p=i,R.T=o}}function To(){}function Oo(e,n,t,r){if(5!==e.tag)throw Error(o(476));var a=No(e).queue;jo(e,a,n,M,null===t?To:function(){return Ao(e),t(r)})}function No(e){var n=e.memoizedState;if(null!==n)return n;var t={};return(n={memoizedState:M,baseState:M,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:$i,lastRenderedState:M},next:null}).next={memoizedState:t,baseState:t,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:$i,lastRenderedState:t},next:null},e.memoizedState=n,null!==(e=e.alternate)&&(e.memoizedState=n),n}function Ao(e){Uo(e,No(e).next.queue,{},Ru())}function Ro(){return _a(Qd)}function Lo(){return Fi().memoizedState}function Mo(){return Fi().memoizedState}function Do(e){for(var n=e.return;null!==n;){switch(n.tag){case 24:case 3:var t=Ru(),r=ii(n,e=ai(t),t);return null!==r&&(Mu(r,n,t),oi(r,n,t)),n={cache:Aa()},void(e.payload=n)}n=n.return}}function Io(e,n,t){var r=Ru();t={lane:r,revertLane:0,action:t,hasEagerState:!1,eagerState:null,next:null},Wo(e)?$o(n,t):null!==(t=Tr(e,n,t,r))&&(Mu(t,e,r),Bo(t,n,r))}function Fo(e,n,t){Uo(e,n,t,Ru())}function Uo(e,n,t,r){var a={lane:r,revertLane:0,action:t,hasEagerState:!1,eagerState:null,next:null};if(Wo(e))$o(n,a);else{var i=e.alternate;if(0===e.lanes&&(null===i||0===i.lanes)&&null!==(i=n.lastRenderedReducer))try{var o=n.lastRenderedState,l=i(o,t);if(a.hasEagerState=!0,a.eagerState=l,Yt(l,o))return jr(e,n,a,0),null===ru&&Pr(),!1}catch(s){}if(null!==(t=Tr(e,n,a,r)))return Mu(t,e,r),Bo(t,n,r),!0}return!1}function Ho(e,n,t,r){if(r={lane:2,revertLane:jc(),action:r,hasEagerState:!1,eagerState:null,next:null},Wo(e)){if(n)throw Error(o(479))}else null!==(n=Tr(e,t,r,2))&&Mu(n,e,2)}function Wo(e){var n=e.alternate;return e===bi||null!==n&&n===bi}function $o(e,n){Si=ki=!0;var t=e.pending;null===t?n.next=n:(n.next=t.next,t.next=n),e.pending=n}function Bo(e,n,t){if(0!==(4194048&t)){var r=n.lanes;t|=r&=e.pendingLanes,n.lanes=t,ze(e,t)}}var Vo={readContext:_a,use:Hi,useCallback:ji,useContext:ji,useEffect:ji,useImperativeHandle:ji,useLayoutEffect:ji,useInsertionEffect:ji,useMemo:ji,useReducer:ji,useRef:ji,useState:ji,useDebugValue:ji,useDeferredValue:ji,useTransition:ji,useSyncExternalStore:ji,useId:ji,useHostTransitionStatus:ji,useFormState:ji,useActionState:ji,useOptimistic:ji,useMemoCache:ji,useCacheRefresh:ji},qo={readContext:_a,use:Hi,useCallback:function(e,n){return Ii().memoizedState=[e,void 0===n?null:n],e},useContext:_a,useEffect:yo,useImperativeHandle:function(e,n,t){t=null!==t&&void 0!==t?t.concat([e]):null,go(4194308,4,ko.bind(null,n,e),t)},useLayoutEffect:function(e,n){return go(4194308,4,e,n)},useInsertionEffect:function(e,n){go(4,2,e,n)},useMemo:function(e,n){var t=Ii();n=void 0===n?null:n;var r=e();if(Ei){fe(!0);try{e()}finally{fe(!1)}}return t.memoizedState=[r,n],r},useReducer:function(e,n,t){var r=Ii();if(void 0!==t){var a=t(n);if(Ei){fe(!0);try{t(n)}finally{fe(!1)}}}else a=n;return r.memoizedState=r.baseState=a,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:a},r.queue=e,e=e.dispatch=Io.bind(null,bi,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},Ii().memoizedState=e},useState:function(e){var n=(e=Ji(e)).queue,t=Fo.bind(null,bi,n);return n.dispatch=t,[e.memoizedState,t]},useDebugValue:Eo,useDeferredValue:function(e,n){return zo(Ii(),e,n)},useTransition:function(){var e=Ji(!1);return e=jo.bind(null,bi,e.queue,!0,!1),Ii().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,n,t){var r=bi,a=Ii();if(ia){if(void 0===t)throw Error(o(407));t=t()}else{if(t=n(),null===ru)throw Error(o(349));0!==(124&iu)||Qi(r,n,t)}a.memoizedState=t;var i={value:t,getSnapshot:n};return a.queue=i,yo(Gi.bind(null,r,i,e),[e]),r.flags|=2048,ho(9,{destroy:void 0,resource:void 0},Yi.bind(null,r,i,t,n),null),t},useId:function(){var e=Ii(),n=ru.identifierPrefix;if(ia){var t=Zr;n="\xab"+n+"R"+(t=(Xr&~(1<<32-pe(Xr)-1)).toString(32)+t),0<(t=Ci++)&&(n+="H"+t.toString(32)),n+="\xbb"}else n="\xab"+n+"r"+(t=Pi++).toString(32)+"\xbb";return e.memoizedState=n},useHostTransitionStatus:Ro,useFormState:so,useActionState:so,useOptimistic:function(e){var n=Ii();n.memoizedState=n.baseState=e;var t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return n.queue=t,n=Ho.bind(null,bi,!0,t),t.dispatch=n,[e,n]},useMemoCache:Wi,useCacheRefresh:function(){return Ii().memoizedState=Do.bind(null,bi)}},Ko={readContext:_a,use:Hi,useCallback:Co,useContext:_a,useEffect:bo,useImperativeHandle:So,useInsertionEffect:wo,useLayoutEffect:xo,useMemo:_o,useReducer:Bi,useRef:mo,useState:function(){return Bi($i)},useDebugValue:Eo,useDeferredValue:function(e,n){return Po(Fi(),wi.memoizedState,e,n)},useTransition:function(){var e=Bi($i)[0],n=Fi().memoizedState;return["boolean"===typeof e?e:Ui(e),n]},useSyncExternalStore:Ki,useId:Lo,useHostTransitionStatus:Ro,useFormState:uo,useActionState:uo,useOptimistic:function(e,n){return eo(Fi(),0,e,n)},useMemoCache:Wi,useCacheRefresh:Mo},Qo={readContext:_a,use:Hi,useCallback:Co,useContext:_a,useEffect:bo,useImperativeHandle:So,useInsertionEffect:wo,useLayoutEffect:xo,useMemo:_o,useReducer:qi,useRef:mo,useState:function(){return qi($i)},useDebugValue:Eo,useDeferredValue:function(e,n){var t=Fi();return null===wi?zo(t,e,n):Po(t,wi.memoizedState,e,n)},useTransition:function(){var e=qi($i)[0],n=Fi().memoizedState;return["boolean"===typeof e?e:Ui(e),n]},useSyncExternalStore:Ki,useId:Lo,useHostTransitionStatus:Ro,useFormState:po,useActionState:po,useOptimistic:function(e,n){var t=Fi();return null!==wi?eo(t,0,e,n):(t.baseState=e,[e,t.queue.dispatch])},useMemoCache:Wi,useCacheRefresh:Mo},Yo=null,Go=0;function Xo(e){var n=Go;return Go+=1,null===Yo&&(Yo=[]),Xa(Yo,e,n)}function Zo(e,n){n=n.props.ref,e.ref=void 0!==n?n:null}function Jo(e,n){if(n.$$typeof===p)throw Error(o(525));throw e=Object.prototype.toString.call(n),Error(o(31,"[object Object]"===e?"object with keys {"+Object.keys(n).join(", ")+"}":e))}function el(e){return(0,e._init)(e._payload)}function nl(e){function n(n,t){if(e){var r=n.deletions;null===r?(n.deletions=[t],n.flags|=16):r.push(t)}}function t(t,r){if(!e)return null;for(;null!==r;)n(t,r),r=r.sibling;return null}function r(e){for(var n=new Map;null!==e;)null!==e.key?n.set(e.key,e):n.set(e.index,e),e=e.sibling;return n}function a(e,n){return(e=Ir(e,n)).index=0,e.sibling=null,e}function i(n,t,r){return n.index=r,e?null!==(r=n.alternate)?(r=r.index)<t?(n.flags|=67108866,t):r:(n.flags|=67108866,t):(n.flags|=1048576,t)}function l(n){return e&&null===n.alternate&&(n.flags|=67108866),n}function s(e,n,t,r){return null===n||6!==n.tag?((n=Wr(t,e.mode,r)).return=e,n):((n=a(n,t)).return=e,n)}function u(e,n,t,r){var i=t.type;return i===g?d(e,n,t.props.children,r,t.key):null!==n&&(n.elementType===i||"object"===typeof i&&null!==i&&i.$$typeof===_&&el(i)===n.type)?(Zo(n=a(n,t.props),t),n.return=e,n):(Zo(n=Ur(t.type,t.key,t.props,null,e.mode,r),t),n.return=e,n)}function c(e,n,t,r){return null===n||4!==n.tag||n.stateNode.containerInfo!==t.containerInfo||n.stateNode.implementation!==t.implementation?((n=$r(t,e.mode,r)).return=e,n):((n=a(n,t.children||[])).return=e,n)}function d(e,n,t,r,i){return null===n||7!==n.tag?((n=Hr(t,e.mode,r,i)).return=e,n):((n=a(n,t)).return=e,n)}function f(e,n,t){if("string"===typeof n&&""!==n||"number"===typeof n||"bigint"===typeof n)return(n=Wr(""+n,e.mode,t)).return=e,n;if("object"===typeof n&&null!==n){switch(n.$$typeof){case h:return Zo(t=Ur(n.type,n.key,n.props,null,e.mode,t),n),t.return=e,t;case m:return(n=$r(n,e.mode,t)).return=e,n;case _:return f(e,n=(0,n._init)(n._payload),t)}if(A(n)||T(n))return(n=Hr(n,e.mode,t,null)).return=e,n;if("function"===typeof n.then)return f(e,Xo(n),t);if(n.$$typeof===x)return f(e,za(e,n),t);Jo(e,n)}return null}function p(e,n,t,r){var a=null!==n?n.key:null;if("string"===typeof t&&""!==t||"number"===typeof t||"bigint"===typeof t)return null!==a?null:s(e,n,""+t,r);if("object"===typeof t&&null!==t){switch(t.$$typeof){case h:return t.key===a?u(e,n,t,r):null;case m:return t.key===a?c(e,n,t,r):null;case _:return p(e,n,t=(a=t._init)(t._payload),r)}if(A(t)||T(t))return null!==a?null:d(e,n,t,r,null);if("function"===typeof t.then)return p(e,n,Xo(t),r);if(t.$$typeof===x)return p(e,n,za(e,t),r);Jo(e,t)}return null}function v(e,n,t,r,a){if("string"===typeof r&&""!==r||"number"===typeof r||"bigint"===typeof r)return s(n,e=e.get(t)||null,""+r,a);if("object"===typeof r&&null!==r){switch(r.$$typeof){case h:return u(n,e=e.get(null===r.key?t:r.key)||null,r,a);case m:return c(n,e=e.get(null===r.key?t:r.key)||null,r,a);case _:return v(e,n,t,r=(0,r._init)(r._payload),a)}if(A(r)||T(r))return d(n,e=e.get(t)||null,r,a,null);if("function"===typeof r.then)return v(e,n,t,Xo(r),a);if(r.$$typeof===x)return v(e,n,t,za(n,r),a);Jo(n,r)}return null}function y(s,u,c,d){if("object"===typeof c&&null!==c&&c.type===g&&null===c.key&&(c=c.props.children),"object"===typeof c&&null!==c){switch(c.$$typeof){case h:e:{for(var b=c.key;null!==u;){if(u.key===b){if((b=c.type)===g){if(7===u.tag){t(s,u.sibling),(d=a(u,c.props.children)).return=s,s=d;break e}}else if(u.elementType===b||"object"===typeof b&&null!==b&&b.$$typeof===_&&el(b)===u.type){t(s,u.sibling),Zo(d=a(u,c.props),c),d.return=s,s=d;break e}t(s,u);break}n(s,u),u=u.sibling}c.type===g?((d=Hr(c.props.children,s.mode,d,c.key)).return=s,s=d):(Zo(d=Ur(c.type,c.key,c.props,null,s.mode,d),c),d.return=s,s=d)}return l(s);case m:e:{for(b=c.key;null!==u;){if(u.key===b){if(4===u.tag&&u.stateNode.containerInfo===c.containerInfo&&u.stateNode.implementation===c.implementation){t(s,u.sibling),(d=a(u,c.children||[])).return=s,s=d;break e}t(s,u);break}n(s,u),u=u.sibling}(d=$r(c,s.mode,d)).return=s,s=d}return l(s);case _:return y(s,u,c=(b=c._init)(c._payload),d)}if(A(c))return function(a,o,l,s){for(var u=null,c=null,d=o,h=o=0,m=null;null!==d&&h<l.length;h++){d.index>h?(m=d,d=null):m=d.sibling;var g=p(a,d,l[h],s);if(null===g){null===d&&(d=m);break}e&&d&&null===g.alternate&&n(a,d),o=i(g,o,h),null===c?u=g:c.sibling=g,c=g,d=m}if(h===l.length)return t(a,d),ia&&Jr(a,h),u;if(null===d){for(;h<l.length;h++)null!==(d=f(a,l[h],s))&&(o=i(d,o,h),null===c?u=d:c.sibling=d,c=d);return ia&&Jr(a,h),u}for(d=r(d);h<l.length;h++)null!==(m=v(d,a,h,l[h],s))&&(e&&null!==m.alternate&&d.delete(null===m.key?h:m.key),o=i(m,o,h),null===c?u=m:c.sibling=m,c=m);return e&&d.forEach(function(e){return n(a,e)}),ia&&Jr(a,h),u}(s,u,c,d);if(T(c)){if("function"!==typeof(b=T(c)))throw Error(o(150));return function(a,l,s,u){if(null==s)throw Error(o(151));for(var c=null,d=null,h=l,m=l=0,g=null,y=s.next();null!==h&&!y.done;m++,y=s.next()){h.index>m?(g=h,h=null):g=h.sibling;var b=p(a,h,y.value,u);if(null===b){null===h&&(h=g);break}e&&h&&null===b.alternate&&n(a,h),l=i(b,l,m),null===d?c=b:d.sibling=b,d=b,h=g}if(y.done)return t(a,h),ia&&Jr(a,m),c;if(null===h){for(;!y.done;m++,y=s.next())null!==(y=f(a,y.value,u))&&(l=i(y,l,m),null===d?c=y:d.sibling=y,d=y);return ia&&Jr(a,m),c}for(h=r(h);!y.done;m++,y=s.next())null!==(y=v(h,a,m,y.value,u))&&(e&&null!==y.alternate&&h.delete(null===y.key?m:y.key),l=i(y,l,m),null===d?c=y:d.sibling=y,d=y);return e&&h.forEach(function(e){return n(a,e)}),ia&&Jr(a,m),c}(s,u,c=b.call(c),d)}if("function"===typeof c.then)return y(s,u,Xo(c),d);if(c.$$typeof===x)return y(s,u,za(s,c),d);Jo(s,c)}return"string"===typeof c&&""!==c||"number"===typeof c||"bigint"===typeof c?(c=""+c,null!==u&&6===u.tag?(t(s,u.sibling),(d=a(u,c)).return=s,s=d):(t(s,u),(d=Wr(c,s.mode,d)).return=s,s=d),l(s)):t(s,u)}return function(e,n,t,r){try{Go=0;var a=y(e,n,t,r);return Yo=null,a}catch(o){if(o===Va||o===Ka)throw o;var i=Mr(29,o,null,e.mode);return i.lanes=r,i.return=e,i}}}var tl=nl(!0),rl=nl(!1),al=F(null),il=null;function ol(e){var n=e.alternate;H(cl,1&cl.current),H(al,e),null===il&&(null===n||null!==pi.current||null!==n.memoizedState)&&(il=e)}function ll(e){if(22===e.tag){if(H(cl,cl.current),H(al,e),null===il){var n=e.alternate;null!==n&&null!==n.memoizedState&&(il=e)}}else sl()}function sl(){H(cl,cl.current),H(al,al.current)}function ul(e){U(al),il===e&&(il=null),U(cl)}var cl=F(0);function dl(e){for(var n=e;null!==n;){if(13===n.tag){var t=n.memoizedState;if(null!==t&&(null===(t=t.dehydrated)||"$?"===t.data||gd(t)))return n}else if(19===n.tag&&void 0!==n.memoizedProps.revealOrder){if(0!==(128&n.flags))return n}else if(null!==n.child){n.child.return=n,n=n.child;continue}if(n===e)break;for(;null===n.sibling;){if(null===n.return||n.return===e)return null;n=n.return}n.sibling.return=n.return,n=n.sibling}return null}function fl(e,n,t,r){t=null===(t=t(r,n=e.memoizedState))||void 0===t?n:f({},n,t),e.memoizedState=t,0===e.lanes&&(e.updateQueue.baseState=t)}var pl={enqueueSetState:function(e,n,t){e=e._reactInternals;var r=Ru(),a=ai(r);a.payload=n,void 0!==t&&null!==t&&(a.callback=t),null!==(n=ii(e,a,r))&&(Mu(n,e,r),oi(n,e,r))},enqueueReplaceState:function(e,n,t){e=e._reactInternals;var r=Ru(),a=ai(r);a.tag=1,a.payload=n,void 0!==t&&null!==t&&(a.callback=t),null!==(n=ii(e,a,r))&&(Mu(n,e,r),oi(n,e,r))},enqueueForceUpdate:function(e,n){e=e._reactInternals;var t=Ru(),r=ai(t);r.tag=2,void 0!==n&&null!==n&&(r.callback=n),null!==(n=ii(e,r,t))&&(Mu(n,e,t),oi(n,e,t))}};function hl(e,n,t,r,a,i,o){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,i,o):!n.prototype||!n.prototype.isPureReactComponent||(!Gt(t,r)||!Gt(a,i))}function ml(e,n,t,r){e=n.state,"function"===typeof n.componentWillReceiveProps&&n.componentWillReceiveProps(t,r),"function"===typeof n.UNSAFE_componentWillReceiveProps&&n.UNSAFE_componentWillReceiveProps(t,r),n.state!==e&&pl.enqueueReplaceState(n,n.state,null)}function gl(e,n){var t=n;if("ref"in n)for(var r in t={},n)"ref"!==r&&(t[r]=n[r]);if(e=e.defaultProps)for(var a in t===n&&(t=f({},t)),e)void 0===t[a]&&(t[a]=e[a]);return t}var vl="function"===typeof reportError?reportError:function(e){if("object"===typeof window&&"function"===typeof window.ErrorEvent){var n=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"===typeof e&&null!==e&&"string"===typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(n))return}else if("object"===typeof process&&"function"===typeof process.emit)return void process.emit("uncaughtException",e);console.error(e)};function yl(e){vl(e)}function bl(e){console.error(e)}function wl(e){vl(e)}function xl(e,n){try{(0,e.onUncaughtError)(n.value,{componentStack:n.stack})}catch(t){setTimeout(function(){throw t})}}function kl(e,n,t){try{(0,e.onCaughtError)(t.value,{componentStack:t.stack,errorBoundary:1===n.tag?n.stateNode:null})}catch(r){setTimeout(function(){throw r})}}function Sl(e,n,t){return(t=ai(t)).tag=3,t.payload={element:null},t.callback=function(){xl(e,n)},t}function El(e){return(e=ai(e)).tag=3,e}function Cl(e,n,t,r){var a=t.type.getDerivedStateFromError;if("function"===typeof a){var i=r.value;e.payload=function(){return a(i)},e.callback=function(){kl(n,t,r)}}var o=t.stateNode;null!==o&&"function"===typeof o.componentDidCatch&&(e.callback=function(){kl(n,t,r),"function"!==typeof a&&(null===Eu?Eu=new Set([this]):Eu.add(this));var e=r.stack;this.componentDidCatch(r.value,{componentStack:null!==e?e:""})})}var _l=Error(o(461)),zl=!1;function Pl(e,n,t,r){n.child=null===e?rl(n,null,t,r):tl(n,e.child,t,r)}function jl(e,n,t,r,a){t=t.render;var i=n.ref;if("ref"in r){var o={};for(var l in r)"ref"!==l&&(o[l]=r[l])}else o=r;return Ca(n),r=Oi(e,n,t,o,i,a),l=Li(),null===e||zl?(ia&&l&&na(n),n.flags|=1,Pl(e,n,r,a),n.child):(Mi(e,n,a),Yl(e,n,a))}function Tl(e,n,t,r,a){if(null===e){var i=t.type;return"function"!==typeof i||Dr(i)||void 0!==i.defaultProps||null!==t.compare?((e=Ur(t.type,null,r,n,n.mode,a)).ref=n.ref,e.return=n,n.child=e):(n.tag=15,n.type=i,Ol(e,n,i,r,a))}if(i=e.child,!Gl(e,a)){var o=i.memoizedProps;if((t=null!==(t=t.compare)?t:Gt)(o,r)&&e.ref===n.ref)return Yl(e,n,a)}return n.flags|=1,(e=Ir(i,r)).ref=n.ref,e.return=n,n.child=e}function Ol(e,n,t,r,a){if(null!==e){var i=e.memoizedProps;if(Gt(i,r)&&e.ref===n.ref){if(zl=!1,n.pendingProps=r=i,!Gl(e,a))return n.lanes=e.lanes,Yl(e,n,a);0!==(131072&e.flags)&&(zl=!0)}}return Ll(e,n,t,r,a)}function Nl(e,n,t){var r=n.pendingProps,a=r.children,i=null!==e?e.memoizedState:null;if("hidden"===r.mode){if(0!==(128&n.flags)){if(r=null!==i?i.baseLanes|t:t,null!==e){for(a=n.child=e.child,i=0;null!==a;)i=i|a.lanes|a.childLanes,a=a.sibling;n.childLanes=i&~r}else n.childLanes=0,n.child=null;return Al(e,n,r,t)}if(0===(536870912&t))return n.lanes=n.childLanes=536870912,Al(e,n,null!==i?i.baseLanes|t:t,t);n.memoizedState={baseLanes:0,cachePool:null},null!==e&&$a(0,null!==i?i.cachePool:null),null!==i?mi(n,i):gi(),ll(n)}else null!==i?($a(0,i.cachePool),mi(n,i),sl(),n.memoizedState=null):(null!==e&&$a(0,null),gi(),sl());return Pl(e,n,a,t),n.child}function Al(e,n,t,r){var a=Wa();return a=null===a?null:{parent:Na._currentValue,pool:a},n.memoizedState={baseLanes:t,cachePool:a},null!==e&&$a(0,null),gi(),ll(n),null!==e&&Sa(e,n,r,!0),null}function Rl(e,n){var t=n.ref;if(null===t)null!==e&&null!==e.ref&&(n.flags|=4194816);else{if("function"!==typeof t&&"object"!==typeof t)throw Error(o(284));null!==e&&e.ref===t||(n.flags|=4194816)}}function Ll(e,n,t,r,a){return Ca(n),t=Oi(e,n,t,r,void 0,a),r=Li(),null===e||zl?(ia&&r&&na(n),n.flags|=1,Pl(e,n,t,a),n.child):(Mi(e,n,a),Yl(e,n,a))}function Ml(e,n,t,r,a,i){return Ca(n),n.updateQueue=null,t=Ai(n,r,t,a),Ni(e),r=Li(),null===e||zl?(ia&&r&&na(n),n.flags|=1,Pl(e,n,t,i),n.child):(Mi(e,n,i),Yl(e,n,i))}function Dl(e,n,t,r,a){if(Ca(n),null===n.stateNode){var i=Rr,o=t.contextType;"object"===typeof o&&null!==o&&(i=_a(o)),i=new t(r,i),n.memoizedState=null!==i.state&&void 0!==i.state?i.state:null,i.updater=pl,n.stateNode=i,i._reactInternals=n,(i=n.stateNode).props=r,i.state=n.memoizedState,i.refs={},ti(n),o=t.contextType,i.context="object"===typeof o&&null!==o?_a(o):Rr,i.state=n.memoizedState,"function"===typeof(o=t.getDerivedStateFromProps)&&(fl(n,t,o,r),i.state=n.memoizedState),"function"===typeof t.getDerivedStateFromProps||"function"===typeof i.getSnapshotBeforeUpdate||"function"!==typeof i.UNSAFE_componentWillMount&&"function"!==typeof i.componentWillMount||(o=i.state,"function"===typeof i.componentWillMount&&i.componentWillMount(),"function"===typeof i.UNSAFE_componentWillMount&&i.UNSAFE_componentWillMount(),o!==i.state&&pl.enqueueReplaceState(i,i.state,null),ci(n,r,i,a),ui(),i.state=n.memoizedState),"function"===typeof i.componentDidMount&&(n.flags|=4194308),r=!0}else if(null===e){i=n.stateNode;var l=n.memoizedProps,s=gl(t,l);i.props=s;var u=i.context,c=t.contextType;o=Rr,"object"===typeof c&&null!==c&&(o=_a(c));var d=t.getDerivedStateFromProps;c="function"===typeof d||"function"===typeof i.getSnapshotBeforeUpdate,l=n.pendingProps!==l,c||"function"!==typeof i.UNSAFE_componentWillReceiveProps&&"function"!==typeof i.componentWillReceiveProps||(l||u!==o)&&ml(n,i,r,o),ni=!1;var f=n.memoizedState;i.state=f,ci(n,r,i,a),ui(),u=n.memoizedState,l||f!==u||ni?("function"===typeof d&&(fl(n,t,d,r),u=n.memoizedState),(s=ni||hl(n,t,s,r,f,u,o))?(c||"function"!==typeof i.UNSAFE_componentWillMount&&"function"!==typeof i.componentWillMount||("function"===typeof i.componentWillMount&&i.componentWillMount(),"function"===typeof i.UNSAFE_componentWillMount&&i.UNSAFE_componentWillMount()),"function"===typeof i.componentDidMount&&(n.flags|=4194308)):("function"===typeof i.componentDidMount&&(n.flags|=4194308),n.memoizedProps=r,n.memoizedState=u),i.props=r,i.state=u,i.context=o,r=s):("function"===typeof i.componentDidMount&&(n.flags|=4194308),r=!1)}else{i=n.stateNode,ri(e,n),c=gl(t,o=n.memoizedProps),i.props=c,d=n.pendingProps,f=i.context,u=t.contextType,s=Rr,"object"===typeof u&&null!==u&&(s=_a(u)),(u="function"===typeof(l=t.getDerivedStateFromProps)||"function"===typeof i.getSnapshotBeforeUpdate)||"function"!==typeof i.UNSAFE_componentWillReceiveProps&&"function"!==typeof i.componentWillReceiveProps||(o!==d||f!==s)&&ml(n,i,r,s),ni=!1,f=n.memoizedState,i.state=f,ci(n,r,i,a),ui();var p=n.memoizedState;o!==d||f!==p||ni||null!==e&&null!==e.dependencies&&Ea(e.dependencies)?("function"===typeof l&&(fl(n,t,l,r),p=n.memoizedState),(c=ni||hl(n,t,c,r,f,p,s)||null!==e&&null!==e.dependencies&&Ea(e.dependencies))?(u||"function"!==typeof i.UNSAFE_componentWillUpdate&&"function"!==typeof i.componentWillUpdate||("function"===typeof i.componentWillUpdate&&i.componentWillUpdate(r,p,s),"function"===typeof i.UNSAFE_componentWillUpdate&&i.UNSAFE_componentWillUpdate(r,p,s)),"function"===typeof i.componentDidUpdate&&(n.flags|=4),"function"===typeof i.getSnapshotBeforeUpdate&&(n.flags|=1024)):("function"!==typeof i.componentDidUpdate||o===e.memoizedProps&&f===e.memoizedState||(n.flags|=4),"function"!==typeof i.getSnapshotBeforeUpdate||o===e.memoizedProps&&f===e.memoizedState||(n.flags|=1024),n.memoizedProps=r,n.memoizedState=p),i.props=r,i.state=p,i.context=s,r=c):("function"!==typeof i.componentDidUpdate||o===e.memoizedProps&&f===e.memoizedState||(n.flags|=4),"function"!==typeof i.getSnapshotBeforeUpdate||o===e.memoizedProps&&f===e.memoizedState||(n.flags|=1024),r=!1)}return i=r,Rl(e,n),r=0!==(128&n.flags),i||r?(i=n.stateNode,t=r&&"function"!==typeof t.getDerivedStateFromError?null:i.render(),n.flags|=1,null!==e&&r?(n.child=tl(n,e.child,null,a),n.child=tl(n,null,t,a)):Pl(e,n,t,a),n.memoizedState=i.state,e=n.child):e=Yl(e,n,a),e}function Il(e,n,t,r){return pa(),n.flags|=256,Pl(e,n,t,r),n.child}var Fl={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Ul(e){return{baseLanes:e,cachePool:Ba()}}function Hl(e,n,t){return e=null!==e?e.childLanes&~t:0,n&&(e|=gu),e}function Wl(e,n,t){var r,a=n.pendingProps,i=!1,l=0!==(128&n.flags);if((r=l)||(r=(null===e||null!==e.memoizedState)&&0!==(2&cl.current)),r&&(i=!0,n.flags&=-129),r=0!==(32&n.flags),n.flags&=-33,null===e){if(ia){if(i?ol(n):sl(),ia){var s,u=aa;if(s=u){e:{for(s=u,u=la;8!==s.nodeType;){if(!u){u=null;break e}if(null===(s=vd(s.nextSibling))){u=null;break e}}u=s}null!==u?(n.memoizedState={dehydrated:u,treeContext:null!==Gr?{id:Xr,overflow:Zr}:null,retryLane:536870912,hydrationErrors:null},(s=Mr(18,null,null,0)).stateNode=u,s.return=n,n.child=s,ra=n,aa=null,s=!0):s=!1}s||ua(n)}if(null!==(u=n.memoizedState)&&null!==(u=u.dehydrated))return gd(u)?n.lanes=32:n.lanes=536870912,null;ul(n)}return u=a.children,a=a.fallback,i?(sl(),u=Bl({mode:"hidden",children:u},i=n.mode),a=Hr(a,i,t,null),u.return=n,a.return=n,u.sibling=a,n.child=u,(i=n.child).memoizedState=Ul(t),i.childLanes=Hl(e,r,t),n.memoizedState=Fl,a):(ol(n),$l(n,u))}if(null!==(s=e.memoizedState)&&null!==(u=s.dehydrated)){if(l)256&n.flags?(ol(n),n.flags&=-257,n=Vl(e,n,t)):null!==n.memoizedState?(sl(),n.child=e.child,n.flags|=128,n=null):(sl(),i=a.fallback,u=n.mode,a=Bl({mode:"visible",children:a.children},u),(i=Hr(i,u,t,null)).flags|=2,a.return=n,i.return=n,a.sibling=i,n.child=a,tl(n,e.child,null,t),(a=n.child).memoizedState=Ul(t),a.childLanes=Hl(e,r,t),n.memoizedState=Fl,n=i);else if(ol(n),gd(u)){if(r=u.nextSibling&&u.nextSibling.dataset)var c=r.dgst;r=c,(a=Error(o(419))).stack="",a.digest=r,ma({value:a,source:null,stack:null}),n=Vl(e,n,t)}else if(zl||Sa(e,n,t,!1),r=0!==(t&e.childLanes),zl||r){if(null!==(r=ru)&&(0!==(a=0!==((a=0!==(42&(a=t&-t))?1:Pe(a))&(r.suspendedLanes|t))?0:a)&&a!==s.retryLane))throw s.retryLane=a,Or(e,a),Mu(r,e,a),_l;"$?"===u.data||Ku(),n=Vl(e,n,t)}else"$?"===u.data?(n.flags|=192,n.child=e.child,n=null):(e=s.treeContext,aa=vd(u.nextSibling),ra=n,ia=!0,oa=null,la=!1,null!==e&&(Qr[Yr++]=Xr,Qr[Yr++]=Zr,Qr[Yr++]=Gr,Xr=e.id,Zr=e.overflow,Gr=n),(n=$l(n,a.children)).flags|=4096);return n}return i?(sl(),i=a.fallback,u=n.mode,c=(s=e.child).sibling,(a=Ir(s,{mode:"hidden",children:a.children})).subtreeFlags=65011712&s.subtreeFlags,null!==c?i=Ir(c,i):(i=Hr(i,u,t,null)).flags|=2,i.return=n,a.return=n,a.sibling=i,n.child=a,a=i,i=n.child,null===(u=e.child.memoizedState)?u=Ul(t):(null!==(s=u.cachePool)?(c=Na._currentValue,s=s.parent!==c?{parent:c,pool:c}:s):s=Ba(),u={baseLanes:u.baseLanes|t,cachePool:s}),i.memoizedState=u,i.childLanes=Hl(e,r,t),n.memoizedState=Fl,a):(ol(n),e=(t=e.child).sibling,(t=Ir(t,{mode:"visible",children:a.children})).return=n,t.sibling=null,null!==e&&(null===(r=n.deletions)?(n.deletions=[e],n.flags|=16):r.push(e)),n.child=t,n.memoizedState=null,t)}function $l(e,n){return(n=Bl({mode:"visible",children:n},e.mode)).return=e,e.child=n}function Bl(e,n){return(e=Mr(22,e,null,n)).lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function Vl(e,n,t){return tl(n,e.child,null,t),(e=$l(n,n.pendingProps.children)).flags|=2,n.memoizedState=null,e}function ql(e,n,t){e.lanes|=n;var r=e.alternate;null!==r&&(r.lanes|=n),xa(e.return,n,t)}function Kl(e,n,t,r,a){var i=e.memoizedState;null===i?e.memoizedState={isBackwards:n,rendering:null,renderingStartTime:0,last:r,tail:t,tailMode:a}:(i.isBackwards=n,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=t,i.tailMode=a)}function Ql(e,n,t){var r=n.pendingProps,a=r.revealOrder,i=r.tail;if(Pl(e,n,r.children,t),0!==(2&(r=cl.current)))r=1&r|2,n.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=n.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&ql(e,t,n);else if(19===e.tag)ql(e,t,n);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===n)break e;for(;null===e.sibling;){if(null===e.return||e.return===n)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}switch(H(cl,r),a){case"forwards":for(t=n.child,a=null;null!==t;)null!==(e=t.alternate)&&null===dl(e)&&(a=t),t=t.sibling;null===(t=a)?(a=n.child,n.child=null):(a=t.sibling,t.sibling=null),Kl(n,!1,a,t,i);break;case"backwards":for(t=null,a=n.child,n.child=null;null!==a;){if(null!==(e=a.alternate)&&null===dl(e)){n.child=a;break}e=a.sibling,a.sibling=t,t=a,a=e}Kl(n,!0,t,null,i);break;case"together":Kl(n,!1,null,null,void 0);break;default:n.memoizedState=null}return n.child}function Yl(e,n,t){if(null!==e&&(n.dependencies=e.dependencies),pu|=n.lanes,0===(t&n.childLanes)){if(null===e)return null;if(Sa(e,n,t,!1),0===(t&n.childLanes))return null}if(null!==e&&n.child!==e.child)throw Error(o(153));if(null!==n.child){for(t=Ir(e=n.child,e.pendingProps),n.child=t,t.return=n;null!==e.sibling;)e=e.sibling,(t=t.sibling=Ir(e,e.pendingProps)).return=n;t.sibling=null}return n.child}function Gl(e,n){return 0!==(e.lanes&n)||!(null===(e=e.dependencies)||!Ea(e))}function Xl(e,n,t){if(null!==e)if(e.memoizedProps!==n.pendingProps)zl=!0;else{if(!Gl(e,t)&&0===(128&n.flags))return zl=!1,function(e,n,t){switch(n.tag){case 3:q(n,n.stateNode.containerInfo),ba(0,Na,e.memoizedState.cache),pa();break;case 27:case 5:Q(n);break;case 4:q(n,n.stateNode.containerInfo);break;case 10:ba(0,n.type,n.memoizedProps.value);break;case 13:var r=n.memoizedState;if(null!==r)return null!==r.dehydrated?(ol(n),n.flags|=128,null):0!==(t&n.child.childLanes)?Wl(e,n,t):(ol(n),null!==(e=Yl(e,n,t))?e.sibling:null);ol(n);break;case 19:var a=0!==(128&e.flags);if((r=0!==(t&n.childLanes))||(Sa(e,n,t,!1),r=0!==(t&n.childLanes)),a){if(r)return Ql(e,n,t);n.flags|=128}if(null!==(a=n.memoizedState)&&(a.rendering=null,a.tail=null,a.lastEffect=null),H(cl,cl.current),r)break;return null;case 22:case 23:return n.lanes=0,Nl(e,n,t);case 24:ba(0,Na,e.memoizedState.cache)}return Yl(e,n,t)}(e,n,t);zl=0!==(131072&e.flags)}else zl=!1,ia&&0!==(1048576&n.flags)&&ea(n,Kr,n.index);switch(n.lanes=0,n.tag){case 16:e:{e=n.pendingProps;var r=n.elementType,a=r._init;if(r=a(r._payload),n.type=r,"function"!==typeof r){if(void 0!==r&&null!==r){if((a=r.$$typeof)===k){n.tag=11,n=jl(null,n,r,e,t);break e}if(a===C){n.tag=14,n=Tl(null,n,r,e,t);break e}}throw n=N(r)||r,Error(o(306,n,""))}Dr(r)?(e=gl(r,e),n.tag=1,n=Dl(null,n,r,e,t)):(n.tag=0,n=Ll(null,n,r,e,t))}return n;case 0:return Ll(e,n,n.type,n.pendingProps,t);case 1:return Dl(e,n,r=n.type,a=gl(r,n.pendingProps),t);case 3:e:{if(q(n,n.stateNode.containerInfo),null===e)throw Error(o(387));r=n.pendingProps;var i=n.memoizedState;a=i.element,ri(e,n),ci(n,r,null,t);var l=n.memoizedState;if(r=l.cache,ba(0,Na,r),r!==i.cache&&ka(n,[Na],t,!0),ui(),r=l.element,i.isDehydrated){if(i={element:r,isDehydrated:!1,cache:l.cache},n.updateQueue.baseState=i,n.memoizedState=i,256&n.flags){n=Il(e,n,r,t);break e}if(r!==a){ma(a=Er(Error(o(424)),n)),n=Il(e,n,r,t);break e}if(9===(e=n.stateNode.containerInfo).nodeType)e=e.body;else e="HTML"===e.nodeName?e.ownerDocument.body:e;for(aa=vd(e.firstChild),ra=n,ia=!0,oa=null,la=!0,t=rl(n,null,r,t),n.child=t;t;)t.flags=-3&t.flags|4096,t=t.sibling}else{if(pa(),r===a){n=Yl(e,n,t);break e}Pl(e,n,r,t)}n=n.child}return n;case 26:return Rl(e,n),null===e?(t=Pd(n.type,null,n.pendingProps,null))?n.memoizedState=t:ia||(t=n.type,e=n.pendingProps,(r=rd(B.current).createElement(t))[Ne]=n,r[Ae]=e,ed(r,t,e),Ve(r),n.stateNode=r):n.memoizedState=Pd(n.type,e.memoizedProps,n.pendingProps,e.memoizedState),null;case 27:return Q(n),null===e&&ia&&(r=n.stateNode=wd(n.type,n.pendingProps,B.current),ra=n,la=!0,a=aa,pd(n.type)?(yd=a,aa=vd(r.firstChild)):aa=a),Pl(e,n,n.pendingProps.children,t),Rl(e,n),null===e&&(n.flags|=4194304),n.child;case 5:return null===e&&ia&&((a=r=aa)&&(null!==(r=function(e,n,t,r){for(;1===e.nodeType;){var a=t;if(e.nodeName.toLowerCase()!==n.toLowerCase()){if(!r&&("INPUT"!==e.nodeName||"hidden"!==e.type))break}else if(r){if(!e[Fe])switch(n){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if("stylesheet"===(i=e.getAttribute("rel"))&&e.hasAttribute("data-precedence"))break;if(i!==a.rel||e.getAttribute("href")!==(null==a.href||""===a.href?null:a.href)||e.getAttribute("crossorigin")!==(null==a.crossOrigin?null:a.crossOrigin)||e.getAttribute("title")!==(null==a.title?null:a.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(((i=e.getAttribute("src"))!==(null==a.src?null:a.src)||e.getAttribute("type")!==(null==a.type?null:a.type)||e.getAttribute("crossorigin")!==(null==a.crossOrigin?null:a.crossOrigin))&&i&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else{if("input"!==n||"hidden"!==e.type)return e;var i=null==a.name?null:""+a.name;if("hidden"===a.type&&e.getAttribute("name")===i)return e}if(null===(e=vd(e.nextSibling)))break}return null}(r,n.type,n.pendingProps,la))?(n.stateNode=r,ra=n,aa=vd(r.firstChild),la=!1,a=!0):a=!1),a||ua(n)),Q(n),a=n.type,i=n.pendingProps,l=null!==e?e.memoizedProps:null,r=i.children,od(a,i)?r=null:null!==l&&od(a,l)&&(n.flags|=32),null!==n.memoizedState&&(a=Oi(e,n,Ri,null,null,t),Qd._currentValue=a),Rl(e,n),Pl(e,n,r,t),n.child;case 6:return null===e&&ia&&((e=t=aa)&&(null!==(t=function(e,n,t){if(""===n)return null;for(;3!==e.nodeType;){if((1!==e.nodeType||"INPUT"!==e.nodeName||"hidden"!==e.type)&&!t)return null;if(null===(e=vd(e.nextSibling)))return null}return e}(t,n.pendingProps,la))?(n.stateNode=t,ra=n,aa=null,e=!0):e=!1),e||ua(n)),null;case 13:return Wl(e,n,t);case 4:return q(n,n.stateNode.containerInfo),r=n.pendingProps,null===e?n.child=tl(n,null,r,t):Pl(e,n,r,t),n.child;case 11:return jl(e,n,n.type,n.pendingProps,t);case 7:return Pl(e,n,n.pendingProps,t),n.child;case 8:case 12:return Pl(e,n,n.pendingProps.children,t),n.child;case 10:return r=n.pendingProps,ba(0,n.type,r.value),Pl(e,n,r.children,t),n.child;case 9:return a=n.type._context,r=n.pendingProps.children,Ca(n),r=r(a=_a(a)),n.flags|=1,Pl(e,n,r,t),n.child;case 14:return Tl(e,n,n.type,n.pendingProps,t);case 15:return Ol(e,n,n.type,n.pendingProps,t);case 19:return Ql(e,n,t);case 31:return r=n.pendingProps,t=n.mode,r={mode:r.mode,children:r.children},null===e?((t=Bl(r,t)).ref=n.ref,n.child=t,t.return=n,n=t):((t=Ir(e.child,r)).ref=n.ref,n.child=t,t.return=n,n=t),n;case 22:return Nl(e,n,t);case 24:return Ca(n),r=_a(Na),null===e?(null===(a=Wa())&&(a=ru,i=Aa(),a.pooledCache=i,i.refCount++,null!==i&&(a.pooledCacheLanes|=t),a=i),n.memoizedState={parent:r,cache:a},ti(n),ba(0,Na,a)):(0!==(e.lanes&t)&&(ri(e,n),ci(n,null,null,t),ui()),a=e.memoizedState,i=n.memoizedState,a.parent!==r?(a={parent:r,cache:r},n.memoizedState=a,0===n.lanes&&(n.memoizedState=n.updateQueue.baseState=a),ba(0,Na,r)):(r=i.cache,ba(0,Na,r),r!==a.cache&&ka(n,[Na],t,!0))),Pl(e,n,n.pendingProps.children,t),n.child;case 29:throw n.pendingProps}throw Error(o(156,n.tag))}function Zl(e){e.flags|=4}function Jl(e,n){if("stylesheet"!==n.type||0!==(4&n.state.loading))e.flags&=-16777217;else if(e.flags|=16777216,!Hd(n)){if(null!==(n=al.current)&&((4194048&iu)===iu?null!==il:(62914560&iu)!==iu&&0===(536870912&iu)||n!==il))throw Za=Qa,qa;e.flags|=8192}}function es(e,n){null!==n&&(e.flags|=4),16384&e.flags&&(n=22!==e.tag?Se():536870912,e.lanes|=n,vu|=n)}function ns(e,n){if(!ia)switch(e.tailMode){case"hidden":n=e.tail;for(var t=null;null!==n;)null!==n.alternate&&(t=n),n=n.sibling;null===t?e.tail=null:t.sibling=null;break;case"collapsed":t=e.tail;for(var r=null;null!==t;)null!==t.alternate&&(r=t),t=t.sibling;null===r?n||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function ts(e){var n=null!==e.alternate&&e.alternate.child===e.child,t=0,r=0;if(n)for(var a=e.child;null!==a;)t|=a.lanes|a.childLanes,r|=65011712&a.subtreeFlags,r|=65011712&a.flags,a.return=e,a=a.sibling;else for(a=e.child;null!==a;)t|=a.lanes|a.childLanes,r|=a.subtreeFlags,r|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=r,e.childLanes=t,n}function rs(e,n,t){var r=n.pendingProps;switch(ta(n),n.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:case 1:return ts(n),null;case 3:return t=n.stateNode,r=null,null!==e&&(r=e.memoizedState.cache),n.memoizedState.cache!==r&&(n.flags|=2048),wa(Na),K(),t.pendingContext&&(t.context=t.pendingContext,t.pendingContext=null),null!==e&&null!==e.child||(fa(n)?Zl(n):null===e||e.memoizedState.isDehydrated&&0===(256&n.flags)||(n.flags|=1024,ha())),ts(n),null;case 26:return t=n.memoizedState,null===e?(Zl(n),null!==t?(ts(n),Jl(n,t)):(ts(n),n.flags&=-16777217)):t?t!==e.memoizedState?(Zl(n),ts(n),Jl(n,t)):(ts(n),n.flags&=-16777217):(e.memoizedProps!==r&&Zl(n),ts(n),n.flags&=-16777217),null;case 27:Y(n),t=B.current;var a=n.type;if(null!==e&&null!=n.stateNode)e.memoizedProps!==r&&Zl(n);else{if(!r){if(null===n.stateNode)throw Error(o(166));return ts(n),null}e=W.current,fa(n)?ca(n):(e=wd(a,r,t),n.stateNode=e,Zl(n))}return ts(n),null;case 5:if(Y(n),t=n.type,null!==e&&null!=n.stateNode)e.memoizedProps!==r&&Zl(n);else{if(!r){if(null===n.stateNode)throw Error(o(166));return ts(n),null}if(e=W.current,fa(n))ca(n);else{switch(a=rd(B.current),e){case 1:e=a.createElementNS("http://www.w3.org/2000/svg",t);break;case 2:e=a.createElementNS("http://www.w3.org/1998/Math/MathML",t);break;default:switch(t){case"svg":e=a.createElementNS("http://www.w3.org/2000/svg",t);break;case"math":e=a.createElementNS("http://www.w3.org/1998/Math/MathML",t);break;case"script":(e=a.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e="string"===typeof r.is?a.createElement("select",{is:r.is}):a.createElement("select"),r.multiple?e.multiple=!0:r.size&&(e.size=r.size);break;default:e="string"===typeof r.is?a.createElement(t,{is:r.is}):a.createElement(t)}}e[Ne]=n,e[Ae]=r;e:for(a=n.child;null!==a;){if(5===a.tag||6===a.tag)e.appendChild(a.stateNode);else if(4!==a.tag&&27!==a.tag&&null!==a.child){a.child.return=a,a=a.child;continue}if(a===n)break e;for(;null===a.sibling;){if(null===a.return||a.return===n)break e;a=a.return}a.sibling.return=a.return,a=a.sibling}n.stateNode=e;e:switch(ed(e,t,r),t){case"button":case"input":case"select":case"textarea":e=!!r.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&Zl(n)}}return ts(n),n.flags&=-16777217,null;case 6:if(e&&null!=n.stateNode)e.memoizedProps!==r&&Zl(n);else{if("string"!==typeof r&&null===n.stateNode)throw Error(o(166));if(e=B.current,fa(n)){if(e=n.stateNode,t=n.memoizedProps,r=null,null!==(a=ra))switch(a.tag){case 27:case 5:r=a.memoizedProps}e[Ne]=n,(e=!!(e.nodeValue===t||null!==r&&!0===r.suppressHydrationWarning||Gc(e.nodeValue,t)))||ua(n)}else(e=rd(e).createTextNode(r))[Ne]=n,n.stateNode=e}return ts(n),null;case 13:if(r=n.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(a=fa(n),null!==r&&null!==r.dehydrated){if(null===e){if(!a)throw Error(o(318));if(!(a=null!==(a=n.memoizedState)?a.dehydrated:null))throw Error(o(317));a[Ne]=n}else pa(),0===(128&n.flags)&&(n.memoizedState=null),n.flags|=4;ts(n),a=!1}else a=ha(),null!==e&&null!==e.memoizedState&&(e.memoizedState.hydrationErrors=a),a=!0;if(!a)return 256&n.flags?(ul(n),n):(ul(n),null)}if(ul(n),0!==(128&n.flags))return n.lanes=t,n;if(t=null!==r,e=null!==e&&null!==e.memoizedState,t){a=null,null!==(r=n.child).alternate&&null!==r.alternate.memoizedState&&null!==r.alternate.memoizedState.cachePool&&(a=r.alternate.memoizedState.cachePool.pool);var i=null;null!==r.memoizedState&&null!==r.memoizedState.cachePool&&(i=r.memoizedState.cachePool.pool),i!==a&&(r.flags|=2048)}return t!==e&&t&&(n.child.flags|=8192),es(n,n.updateQueue),ts(n),null;case 4:return K(),null===e&&Uc(n.stateNode.containerInfo),ts(n),null;case 10:return wa(n.type),ts(n),null;case 19:if(U(cl),null===(a=n.memoizedState))return ts(n),null;if(r=0!==(128&n.flags),null===(i=a.rendering))if(r)ns(a,!1);else{if(0!==fu||null!==e&&0!==(128&e.flags))for(e=n.child;null!==e;){if(null!==(i=dl(e))){for(n.flags|=128,ns(a,!1),e=i.updateQueue,n.updateQueue=e,es(n,e),n.subtreeFlags=0,e=t,t=n.child;null!==t;)Fr(t,e),t=t.sibling;return H(cl,1&cl.current|2),n.child}e=e.sibling}null!==a.tail&&ne()>ku&&(n.flags|=128,r=!0,ns(a,!1),n.lanes=4194304)}else{if(!r)if(null!==(e=dl(i))){if(n.flags|=128,r=!0,e=e.updateQueue,n.updateQueue=e,es(n,e),ns(a,!0),null===a.tail&&"hidden"===a.tailMode&&!i.alternate&&!ia)return ts(n),null}else 2*ne()-a.renderingStartTime>ku&&536870912!==t&&(n.flags|=128,r=!0,ns(a,!1),n.lanes=4194304);a.isBackwards?(i.sibling=n.child,n.child=i):(null!==(e=a.last)?e.sibling=i:n.child=i,a.last=i)}return null!==a.tail?(n=a.tail,a.rendering=n,a.tail=n.sibling,a.renderingStartTime=ne(),n.sibling=null,e=cl.current,H(cl,r?1&e|2:1&e),n):(ts(n),null);case 22:case 23:return ul(n),vi(),r=null!==n.memoizedState,null!==e?null!==e.memoizedState!==r&&(n.flags|=8192):r&&(n.flags|=8192),r?0!==(536870912&t)&&0===(128&n.flags)&&(ts(n),6&n.subtreeFlags&&(n.flags|=8192)):ts(n),null!==(t=n.updateQueue)&&es(n,t.retryQueue),t=null,null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(t=e.memoizedState.cachePool.pool),r=null,null!==n.memoizedState&&null!==n.memoizedState.cachePool&&(r=n.memoizedState.cachePool.pool),r!==t&&(n.flags|=2048),null!==e&&U(Ha),null;case 24:return t=null,null!==e&&(t=e.memoizedState.cache),n.memoizedState.cache!==t&&(n.flags|=2048),wa(Na),ts(n),null;case 25:case 30:return null}throw Error(o(156,n.tag))}function as(e,n){switch(ta(n),n.tag){case 1:return 65536&(e=n.flags)?(n.flags=-65537&e|128,n):null;case 3:return wa(Na),K(),0!==(65536&(e=n.flags))&&0===(128&e)?(n.flags=-65537&e|128,n):null;case 26:case 27:case 5:return Y(n),null;case 13:if(ul(n),null!==(e=n.memoizedState)&&null!==e.dehydrated){if(null===n.alternate)throw Error(o(340));pa()}return 65536&(e=n.flags)?(n.flags=-65537&e|128,n):null;case 19:return U(cl),null;case 4:return K(),null;case 10:return wa(n.type),null;case 22:case 23:return ul(n),vi(),null!==e&&U(Ha),65536&(e=n.flags)?(n.flags=-65537&e|128,n):null;case 24:return wa(Na),null;default:return null}}function is(e,n){switch(ta(n),n.tag){case 3:wa(Na),K();break;case 26:case 27:case 5:Y(n);break;case 4:K();break;case 13:ul(n);break;case 19:U(cl);break;case 10:wa(n.type);break;case 22:case 23:ul(n),vi(),null!==e&&U(Ha);break;case 24:wa(Na)}}function os(e,n){try{var t=n.updateQueue,r=null!==t?t.lastEffect:null;if(null!==r){var a=r.next;t=a;do{if((t.tag&e)===e){r=void 0;var i=t.create,o=t.inst;r=i(),o.destroy=r}t=t.next}while(t!==a)}}catch(l){cc(n,n.return,l)}}function ls(e,n,t){try{var r=n.updateQueue,a=null!==r?r.lastEffect:null;if(null!==a){var i=a.next;r=i;do{if((r.tag&e)===e){var o=r.inst,l=o.destroy;if(void 0!==l){o.destroy=void 0,a=n;var s=t,u=l;try{u()}catch(c){cc(a,s,c)}}}r=r.next}while(r!==i)}}catch(c){cc(n,n.return,c)}}function ss(e){var n=e.updateQueue;if(null!==n){var t=e.stateNode;try{fi(n,t)}catch(r){cc(e,e.return,r)}}}function us(e,n,t){t.props=gl(e.type,e.memoizedProps),t.state=e.memoizedState;try{t.componentWillUnmount()}catch(r){cc(e,n,r)}}function cs(e,n){try{var t=e.ref;if(null!==t){switch(e.tag){case 26:case 27:case 5:var r=e.stateNode;break;default:r=e.stateNode}"function"===typeof t?e.refCleanup=t(r):t.current=r}}catch(a){cc(e,n,a)}}function ds(e,n){var t=e.ref,r=e.refCleanup;if(null!==t)if("function"===typeof r)try{r()}catch(a){cc(e,n,a)}finally{e.refCleanup=null,null!=(e=e.alternate)&&(e.refCleanup=null)}else if("function"===typeof t)try{t(null)}catch(i){cc(e,n,i)}else t.current=null}function fs(e){var n=e.type,t=e.memoizedProps,r=e.stateNode;try{e:switch(n){case"button":case"input":case"select":case"textarea":t.autoFocus&&r.focus();break e;case"img":t.src?r.src=t.src:t.srcSet&&(r.srcset=t.srcSet)}}catch(a){cc(e,e.return,a)}}function ps(e,n,t){try{var r=e.stateNode;!function(e,n,t,r){switch(n){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var a=null,i=null,l=null,s=null,u=null,c=null,d=null;for(h in t){var f=t[h];if(t.hasOwnProperty(h)&&null!=f)switch(h){case"checked":case"value":break;case"defaultValue":u=f;default:r.hasOwnProperty(h)||Zc(e,n,h,null,r,f)}}for(var p in r){var h=r[p];if(f=t[p],r.hasOwnProperty(p)&&(null!=h||null!=f))switch(p){case"type":i=h;break;case"name":a=h;break;case"checked":c=h;break;case"defaultChecked":d=h;break;case"value":l=h;break;case"defaultValue":s=h;break;case"children":case"dangerouslySetInnerHTML":if(null!=h)throw Error(o(137,n));break;default:h!==f&&Zc(e,n,p,h,r,f)}}return void vn(e,l,s,u,c,d,i,a);case"select":for(i in h=l=s=p=null,t)if(u=t[i],t.hasOwnProperty(i)&&null!=u)switch(i){case"value":break;case"multiple":h=u;default:r.hasOwnProperty(i)||Zc(e,n,i,null,r,u)}for(a in r)if(i=r[a],u=t[a],r.hasOwnProperty(a)&&(null!=i||null!=u))switch(a){case"value":p=i;break;case"defaultValue":s=i;break;case"multiple":l=i;default:i!==u&&Zc(e,n,a,i,r,u)}return n=s,t=l,r=h,void(null!=p?wn(e,!!t,p,!1):!!r!==!!t&&(null!=n?wn(e,!!t,n,!0):wn(e,!!t,t?[]:"",!1)));case"textarea":for(s in h=p=null,t)if(a=t[s],t.hasOwnProperty(s)&&null!=a&&!r.hasOwnProperty(s))switch(s){case"value":case"children":break;default:Zc(e,n,s,null,r,a)}for(l in r)if(a=r[l],i=t[l],r.hasOwnProperty(l)&&(null!=a||null!=i))switch(l){case"value":p=a;break;case"defaultValue":h=a;break;case"children":break;case"dangerouslySetInnerHTML":if(null!=a)throw Error(o(91));break;default:a!==i&&Zc(e,n,l,a,r,i)}return void xn(e,p,h);case"option":for(var m in t)if(p=t[m],t.hasOwnProperty(m)&&null!=p&&!r.hasOwnProperty(m))if("selected"===m)e.selected=!1;else Zc(e,n,m,null,r,p);for(u in r)if(p=r[u],h=t[u],r.hasOwnProperty(u)&&p!==h&&(null!=p||null!=h))if("selected"===u)e.selected=p&&"function"!==typeof p&&"symbol"!==typeof p;else Zc(e,n,u,p,r,h);return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var g in t)p=t[g],t.hasOwnProperty(g)&&null!=p&&!r.hasOwnProperty(g)&&Zc(e,n,g,null,r,p);for(c in r)if(p=r[c],h=t[c],r.hasOwnProperty(c)&&p!==h&&(null!=p||null!=h))switch(c){case"children":case"dangerouslySetInnerHTML":if(null!=p)throw Error(o(137,n));break;default:Zc(e,n,c,p,r,h)}return;default:if(zn(n)){for(var v in t)p=t[v],t.hasOwnProperty(v)&&void 0!==p&&!r.hasOwnProperty(v)&&Jc(e,n,v,void 0,r,p);for(d in r)p=r[d],h=t[d],!r.hasOwnProperty(d)||p===h||void 0===p&&void 0===h||Jc(e,n,d,p,r,h);return}}for(var y in t)p=t[y],t.hasOwnProperty(y)&&null!=p&&!r.hasOwnProperty(y)&&Zc(e,n,y,null,r,p);for(f in r)p=r[f],h=t[f],!r.hasOwnProperty(f)||p===h||null==p&&null==h||Zc(e,n,f,p,r,h)}(r,e.type,t,n),r[Ae]=n}catch(a){cc(e,e.return,a)}}function hs(e){return 5===e.tag||3===e.tag||26===e.tag||27===e.tag&&pd(e.type)||4===e.tag}function ms(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||hs(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(27===e.tag&&pd(e.type))continue e;if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function gs(e,n,t){var r=e.tag;if(5===r||6===r)e=e.stateNode,n?(9===t.nodeType?t.body:"HTML"===t.nodeName?t.ownerDocument.body:t).insertBefore(e,n):((n=9===t.nodeType?t.body:"HTML"===t.nodeName?t.ownerDocument.body:t).appendChild(e),null!==(t=t._reactRootContainer)&&void 0!==t||null!==n.onclick||(n.onclick=Xc));else if(4!==r&&(27===r&&pd(e.type)&&(t=e.stateNode,n=null),null!==(e=e.child)))for(gs(e,n,t),e=e.sibling;null!==e;)gs(e,n,t),e=e.sibling}function vs(e,n,t){var r=e.tag;if(5===r||6===r)e=e.stateNode,n?t.insertBefore(e,n):t.appendChild(e);else if(4!==r&&(27===r&&pd(e.type)&&(t=e.stateNode),null!==(e=e.child)))for(vs(e,n,t),e=e.sibling;null!==e;)vs(e,n,t),e=e.sibling}function ys(e){var n=e.stateNode,t=e.memoizedProps;try{for(var r=e.type,a=n.attributes;a.length;)n.removeAttributeNode(a[0]);ed(n,r,t),n[Ne]=e,n[Ae]=t}catch(i){cc(e,e.return,i)}}var bs=!1,ws=!1,xs=!1,ks="function"===typeof WeakSet?WeakSet:Set,Ss=null;function Es(e,n,t){var r=t.flags;switch(t.tag){case 0:case 11:case 15:Ds(e,t),4&r&&os(5,t);break;case 1:if(Ds(e,t),4&r)if(e=t.stateNode,null===n)try{e.componentDidMount()}catch(o){cc(t,t.return,o)}else{var a=gl(t.type,n.memoizedProps);n=n.memoizedState;try{e.componentDidUpdate(a,n,e.__reactInternalSnapshotBeforeUpdate)}catch(l){cc(t,t.return,l)}}64&r&&ss(t),512&r&&cs(t,t.return);break;case 3:if(Ds(e,t),64&r&&null!==(e=t.updateQueue)){if(n=null,null!==t.child)switch(t.child.tag){case 27:case 5:case 1:n=t.child.stateNode}try{fi(e,n)}catch(o){cc(t,t.return,o)}}break;case 27:null===n&&4&r&&ys(t);case 26:case 5:Ds(e,t),null===n&&4&r&&fs(t),512&r&&cs(t,t.return);break;case 12:Ds(e,t);break;case 13:Ds(e,t),4&r&&Ts(e,t),64&r&&(null!==(e=t.memoizedState)&&(null!==(e=e.dehydrated)&&function(e,n){var t=e.ownerDocument;if("$?"!==e.data||"complete"===t.readyState)n();else{var r=function(){n(),t.removeEventListener("DOMContentLoaded",r)};t.addEventListener("DOMContentLoaded",r),e._reactRetry=r}}(e,t=hc.bind(null,t))));break;case 22:if(!(r=null!==t.memoizedState||bs)){n=null!==n&&null!==n.memoizedState||ws,a=bs;var i=ws;bs=r,(ws=n)&&!i?Fs(e,t,0!==(8772&t.subtreeFlags)):Ds(e,t),bs=a,ws=i}break;case 30:break;default:Ds(e,t)}}function Cs(e){var n=e.alternate;null!==n&&(e.alternate=null,Cs(n)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(n=e.stateNode)&&Ue(n)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var _s=null,zs=!1;function Ps(e,n,t){for(t=t.child;null!==t;)js(e,n,t),t=t.sibling}function js(e,n,t){if(de&&"function"===typeof de.onCommitFiberUnmount)try{de.onCommitFiberUnmount(ce,t)}catch(i){}switch(t.tag){case 26:ws||ds(t,n),Ps(e,n,t),t.memoizedState?t.memoizedState.count--:t.stateNode&&(t=t.stateNode).parentNode.removeChild(t);break;case 27:ws||ds(t,n);var r=_s,a=zs;pd(t.type)&&(_s=t.stateNode,zs=!1),Ps(e,n,t),xd(t.stateNode),_s=r,zs=a;break;case 5:ws||ds(t,n);case 6:if(r=_s,a=zs,_s=null,Ps(e,n,t),zs=a,null!==(_s=r))if(zs)try{(9===_s.nodeType?_s.body:"HTML"===_s.nodeName?_s.ownerDocument.body:_s).removeChild(t.stateNode)}catch(o){cc(t,n,o)}else try{_s.removeChild(t.stateNode)}catch(o){cc(t,n,o)}break;case 18:null!==_s&&(zs?(hd(9===(e=_s).nodeType?e.body:"HTML"===e.nodeName?e.ownerDocument.body:e,t.stateNode),Pf(e)):hd(_s,t.stateNode));break;case 4:r=_s,a=zs,_s=t.stateNode.containerInfo,zs=!0,Ps(e,n,t),_s=r,zs=a;break;case 0:case 11:case 14:case 15:ws||ls(2,t,n),ws||ls(4,t,n),Ps(e,n,t);break;case 1:ws||(ds(t,n),"function"===typeof(r=t.stateNode).componentWillUnmount&&us(t,n,r)),Ps(e,n,t);break;case 21:Ps(e,n,t);break;case 22:ws=(r=ws)||null!==t.memoizedState,Ps(e,n,t),ws=r;break;default:Ps(e,n,t)}}function Ts(e,n){if(null===n.memoizedState&&(null!==(e=n.alternate)&&(null!==(e=e.memoizedState)&&null!==(e=e.dehydrated))))try{Pf(e)}catch(t){cc(n,n.return,t)}}function Os(e,n){var t=function(e){switch(e.tag){case 13:case 19:var n=e.stateNode;return null===n&&(n=e.stateNode=new ks),n;case 22:return null===(n=(e=e.stateNode)._retryCache)&&(n=e._retryCache=new ks),n;default:throw Error(o(435,e.tag))}}(e);n.forEach(function(n){var r=mc.bind(null,e,n);t.has(n)||(t.add(n),n.then(r,r))})}function Ns(e,n){var t=n.deletions;if(null!==t)for(var r=0;r<t.length;r++){var a=t[r],i=e,l=n,s=l;e:for(;null!==s;){switch(s.tag){case 27:if(pd(s.type)){_s=s.stateNode,zs=!1;break e}break;case 5:_s=s.stateNode,zs=!1;break e;case 3:case 4:_s=s.stateNode.containerInfo,zs=!0;break e}s=s.return}if(null===_s)throw Error(o(160));js(i,l,a),_s=null,zs=!1,null!==(i=a.alternate)&&(i.return=null),a.return=null}if(13878&n.subtreeFlags)for(n=n.child;null!==n;)Rs(n,e),n=n.sibling}var As=null;function Rs(e,n){var t=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:Ns(n,e),Ls(e),4&r&&(ls(3,e,e.return),os(3,e),ls(5,e,e.return));break;case 1:Ns(n,e),Ls(e),512&r&&(ws||null===t||ds(t,t.return)),64&r&&bs&&(null!==(e=e.updateQueue)&&(null!==(r=e.callbacks)&&(t=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=null===t?r:t.concat(r))));break;case 26:var a=As;if(Ns(n,e),Ls(e),512&r&&(ws||null===t||ds(t,t.return)),4&r){var i=null!==t?t.memoizedState:null;if(r=e.memoizedState,null===t)if(null===r)if(null===e.stateNode){e:{r=e.type,t=e.memoizedProps,a=a.ownerDocument||a;n:switch(r){case"title":(!(i=a.getElementsByTagName("title")[0])||i[Fe]||i[Ne]||"http://www.w3.org/2000/svg"===i.namespaceURI||i.hasAttribute("itemprop"))&&(i=a.createElement(r),a.head.insertBefore(i,a.querySelector("head > title"))),ed(i,r,t),i[Ne]=e,Ve(i),r=i;break e;case"link":var l=Fd("link","href",a).get(r+(t.href||""));if(l)for(var s=0;s<l.length;s++)if((i=l[s]).getAttribute("href")===(null==t.href||""===t.href?null:t.href)&&i.getAttribute("rel")===(null==t.rel?null:t.rel)&&i.getAttribute("title")===(null==t.title?null:t.title)&&i.getAttribute("crossorigin")===(null==t.crossOrigin?null:t.crossOrigin)){l.splice(s,1);break n}ed(i=a.createElement(r),r,t),a.head.appendChild(i);break;case"meta":if(l=Fd("meta","content",a).get(r+(t.content||"")))for(s=0;s<l.length;s++)if((i=l[s]).getAttribute("content")===(null==t.content?null:""+t.content)&&i.getAttribute("name")===(null==t.name?null:t.name)&&i.getAttribute("property")===(null==t.property?null:t.property)&&i.getAttribute("http-equiv")===(null==t.httpEquiv?null:t.httpEquiv)&&i.getAttribute("charset")===(null==t.charSet?null:t.charSet)){l.splice(s,1);break n}ed(i=a.createElement(r),r,t),a.head.appendChild(i);break;default:throw Error(o(468,r))}i[Ne]=e,Ve(i),r=i}e.stateNode=r}else Ud(a,e.type,e.stateNode);else e.stateNode=Rd(a,r,e.memoizedProps);else i!==r?(null===i?null!==t.stateNode&&(t=t.stateNode).parentNode.removeChild(t):i.count--,null===r?Ud(a,e.type,e.stateNode):Rd(a,r,e.memoizedProps)):null===r&&null!==e.stateNode&&ps(e,e.memoizedProps,t.memoizedProps)}break;case 27:Ns(n,e),Ls(e),512&r&&(ws||null===t||ds(t,t.return)),null!==t&&4&r&&ps(e,e.memoizedProps,t.memoizedProps);break;case 5:if(Ns(n,e),Ls(e),512&r&&(ws||null===t||ds(t,t.return)),32&e.flags){a=e.stateNode;try{Sn(a,"")}catch(h){cc(e,e.return,h)}}4&r&&null!=e.stateNode&&ps(e,a=e.memoizedProps,null!==t?t.memoizedProps:a),1024&r&&(xs=!0);break;case 6:if(Ns(n,e),Ls(e),4&r){if(null===e.stateNode)throw Error(o(162));r=e.memoizedProps,t=e.stateNode;try{t.nodeValue=r}catch(h){cc(e,e.return,h)}}break;case 3:if(Id=null,a=As,As=Ed(n.containerInfo),Ns(n,e),As=a,Ls(e),4&r&&null!==t&&t.memoizedState.isDehydrated)try{Pf(n.containerInfo)}catch(h){cc(e,e.return,h)}xs&&(xs=!1,Ms(e));break;case 4:r=As,As=Ed(e.stateNode.containerInfo),Ns(n,e),Ls(e),As=r;break;case 12:default:Ns(n,e),Ls(e);break;case 13:Ns(n,e),Ls(e),8192&e.child.flags&&null!==e.memoizedState!==(null!==t&&null!==t.memoizedState)&&(xu=ne()),4&r&&(null!==(r=e.updateQueue)&&(e.updateQueue=null,Os(e,r)));break;case 22:a=null!==e.memoizedState;var u=null!==t&&null!==t.memoizedState,c=bs,d=ws;if(bs=c||a,ws=d||u,Ns(n,e),ws=d,bs=c,Ls(e),8192&r)e:for(n=e.stateNode,n._visibility=a?-2&n._visibility:1|n._visibility,a&&(null===t||u||bs||ws||Is(e)),t=null,n=e;;){if(5===n.tag||26===n.tag){if(null===t){u=t=n;try{if(i=u.stateNode,a)"function"===typeof(l=i.style).setProperty?l.setProperty("display","none","important"):l.display="none";else{s=u.stateNode;var f=u.memoizedProps.style,p=void 0!==f&&null!==f&&f.hasOwnProperty("display")?f.display:null;s.style.display=null==p||"boolean"===typeof p?"":(""+p).trim()}}catch(h){cc(u,u.return,h)}}}else if(6===n.tag){if(null===t){u=n;try{u.stateNode.nodeValue=a?"":u.memoizedProps}catch(h){cc(u,u.return,h)}}}else if((22!==n.tag&&23!==n.tag||null===n.memoizedState||n===e)&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===e)break e;for(;null===n.sibling;){if(null===n.return||n.return===e)break e;t===n&&(t=null),n=n.return}t===n&&(t=null),n.sibling.return=n.return,n=n.sibling}4&r&&(null!==(r=e.updateQueue)&&(null!==(t=r.retryQueue)&&(r.retryQueue=null,Os(e,t))));break;case 19:Ns(n,e),Ls(e),4&r&&(null!==(r=e.updateQueue)&&(e.updateQueue=null,Os(e,r)));case 30:case 21:}}function Ls(e){var n=e.flags;if(2&n){try{for(var t,r=e.return;null!==r;){if(hs(r)){t=r;break}r=r.return}if(null==t)throw Error(o(160));switch(t.tag){case 27:var a=t.stateNode;vs(e,ms(e),a);break;case 5:var i=t.stateNode;32&t.flags&&(Sn(i,""),t.flags&=-33),vs(e,ms(e),i);break;case 3:case 4:var l=t.stateNode.containerInfo;gs(e,ms(e),l);break;default:throw Error(o(161))}}catch(s){cc(e,e.return,s)}e.flags&=-3}4096&n&&(e.flags&=-4097)}function Ms(e){if(1024&e.subtreeFlags)for(e=e.child;null!==e;){var n=e;Ms(n),5===n.tag&&1024&n.flags&&n.stateNode.reset(),e=e.sibling}}function Ds(e,n){if(8772&n.subtreeFlags)for(n=n.child;null!==n;)Es(e,n.alternate,n),n=n.sibling}function Is(e){for(e=e.child;null!==e;){var n=e;switch(n.tag){case 0:case 11:case 14:case 15:ls(4,n,n.return),Is(n);break;case 1:ds(n,n.return);var t=n.stateNode;"function"===typeof t.componentWillUnmount&&us(n,n.return,t),Is(n);break;case 27:xd(n.stateNode);case 26:case 5:ds(n,n.return),Is(n);break;case 22:null===n.memoizedState&&Is(n);break;default:Is(n)}e=e.sibling}}function Fs(e,n,t){for(t=t&&0!==(8772&n.subtreeFlags),n=n.child;null!==n;){var r=n.alternate,a=e,i=n,o=i.flags;switch(i.tag){case 0:case 11:case 15:Fs(a,i,t),os(4,i);break;case 1:if(Fs(a,i,t),"function"===typeof(a=(r=i).stateNode).componentDidMount)try{a.componentDidMount()}catch(u){cc(r,r.return,u)}if(null!==(a=(r=i).updateQueue)){var l=r.stateNode;try{var s=a.shared.hiddenCallbacks;if(null!==s)for(a.shared.hiddenCallbacks=null,a=0;a<s.length;a++)di(s[a],l)}catch(u){cc(r,r.return,u)}}t&&64&o&&ss(i),cs(i,i.return);break;case 27:ys(i);case 26:case 5:Fs(a,i,t),t&&null===r&&4&o&&fs(i),cs(i,i.return);break;case 12:Fs(a,i,t);break;case 13:Fs(a,i,t),t&&4&o&&Ts(a,i);break;case 22:null===i.memoizedState&&Fs(a,i,t),cs(i,i.return);break;case 30:break;default:Fs(a,i,t)}n=n.sibling}}function Us(e,n){var t=null;null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(t=e.memoizedState.cachePool.pool),e=null,null!==n.memoizedState&&null!==n.memoizedState.cachePool&&(e=n.memoizedState.cachePool.pool),e!==t&&(null!=e&&e.refCount++,null!=t&&Ra(t))}function Hs(e,n){e=null,null!==n.alternate&&(e=n.alternate.memoizedState.cache),(n=n.memoizedState.cache)!==e&&(n.refCount++,null!=e&&Ra(e))}function Ws(e,n,t,r){if(10256&n.subtreeFlags)for(n=n.child;null!==n;)$s(e,n,t,r),n=n.sibling}function $s(e,n,t,r){var a=n.flags;switch(n.tag){case 0:case 11:case 15:Ws(e,n,t,r),2048&a&&os(9,n);break;case 1:case 13:default:Ws(e,n,t,r);break;case 3:Ws(e,n,t,r),2048&a&&(e=null,null!==n.alternate&&(e=n.alternate.memoizedState.cache),(n=n.memoizedState.cache)!==e&&(n.refCount++,null!=e&&Ra(e)));break;case 12:if(2048&a){Ws(e,n,t,r),e=n.stateNode;try{var i=n.memoizedProps,o=i.id,l=i.onPostCommit;"function"===typeof l&&l(o,null===n.alternate?"mount":"update",e.passiveEffectDuration,-0)}catch(s){cc(n,n.return,s)}}else Ws(e,n,t,r);break;case 23:break;case 22:i=n.stateNode,o=n.alternate,null!==n.memoizedState?2&i._visibility?Ws(e,n,t,r):Vs(e,n):2&i._visibility?Ws(e,n,t,r):(i._visibility|=2,Bs(e,n,t,r,0!==(10256&n.subtreeFlags))),2048&a&&Us(o,n);break;case 24:Ws(e,n,t,r),2048&a&&Hs(n.alternate,n)}}function Bs(e,n,t,r,a){for(a=a&&0!==(10256&n.subtreeFlags),n=n.child;null!==n;){var i=e,o=n,l=t,s=r,u=o.flags;switch(o.tag){case 0:case 11:case 15:Bs(i,o,l,s,a),os(8,o);break;case 23:break;case 22:var c=o.stateNode;null!==o.memoizedState?2&c._visibility?Bs(i,o,l,s,a):Vs(i,o):(c._visibility|=2,Bs(i,o,l,s,a)),a&&2048&u&&Us(o.alternate,o);break;case 24:Bs(i,o,l,s,a),a&&2048&u&&Hs(o.alternate,o);break;default:Bs(i,o,l,s,a)}n=n.sibling}}function Vs(e,n){if(10256&n.subtreeFlags)for(n=n.child;null!==n;){var t=e,r=n,a=r.flags;switch(r.tag){case 22:Vs(t,r),2048&a&&Us(r.alternate,r);break;case 24:Vs(t,r),2048&a&&Hs(r.alternate,r);break;default:Vs(t,r)}n=n.sibling}}var qs=8192;function Ks(e){if(e.subtreeFlags&qs)for(e=e.child;null!==e;)Qs(e),e=e.sibling}function Qs(e){switch(e.tag){case 26:Ks(e),e.flags&qs&&null!==e.memoizedState&&function(e,n,t){if(null===Wd)throw Error(o(475));var r=Wd;if("stylesheet"===n.type&&("string"!==typeof t.media||!1!==matchMedia(t.media).matches)&&0===(4&n.state.loading)){if(null===n.instance){var a=jd(t.href),i=e.querySelector(Td(a));if(i)return null!==(e=i._p)&&"object"===typeof e&&"function"===typeof e.then&&(r.count++,r=Bd.bind(r),e.then(r,r)),n.state.loading|=4,n.instance=i,void Ve(i);i=e.ownerDocument||e,t=Od(t),(a=kd.get(a))&&Md(t,a),Ve(i=i.createElement("link"));var l=i;l._p=new Promise(function(e,n){l.onload=e,l.onerror=n}),ed(i,"link",t),n.instance=i}null===r.stylesheets&&(r.stylesheets=new Map),r.stylesheets.set(n,e),(e=n.state.preload)&&0===(3&n.state.loading)&&(r.count++,n=Bd.bind(r),e.addEventListener("load",n),e.addEventListener("error",n))}}(As,e.memoizedState,e.memoizedProps);break;case 5:default:Ks(e);break;case 3:case 4:var n=As;As=Ed(e.stateNode.containerInfo),Ks(e),As=n;break;case 22:null===e.memoizedState&&(null!==(n=e.alternate)&&null!==n.memoizedState?(n=qs,qs=16777216,Ks(e),qs=n):Ks(e))}}function Ys(e){var n=e.alternate;if(null!==n&&null!==(e=n.child)){n.child=null;do{n=e.sibling,e.sibling=null,e=n}while(null!==e)}}function Gs(e){var n=e.deletions;if(0!==(16&e.flags)){if(null!==n)for(var t=0;t<n.length;t++){var r=n[t];Ss=r,Js(r,e)}Ys(e)}if(10256&e.subtreeFlags)for(e=e.child;null!==e;)Xs(e),e=e.sibling}function Xs(e){switch(e.tag){case 0:case 11:case 15:Gs(e),2048&e.flags&&ls(9,e,e.return);break;case 3:case 12:default:Gs(e);break;case 22:var n=e.stateNode;null!==e.memoizedState&&2&n._visibility&&(null===e.return||13!==e.return.tag)?(n._visibility&=-3,Zs(e)):Gs(e)}}function Zs(e){var n=e.deletions;if(0!==(16&e.flags)){if(null!==n)for(var t=0;t<n.length;t++){var r=n[t];Ss=r,Js(r,e)}Ys(e)}for(e=e.child;null!==e;){switch((n=e).tag){case 0:case 11:case 15:ls(8,n,n.return),Zs(n);break;case 22:2&(t=n.stateNode)._visibility&&(t._visibility&=-3,Zs(n));break;default:Zs(n)}e=e.sibling}}function Js(e,n){for(;null!==Ss;){var t=Ss;switch(t.tag){case 0:case 11:case 15:ls(8,t,n);break;case 23:case 22:if(null!==t.memoizedState&&null!==t.memoizedState.cachePool){var r=t.memoizedState.cachePool.pool;null!=r&&r.refCount++}break;case 24:Ra(t.memoizedState.cache)}if(null!==(r=t.child))r.return=t,Ss=r;else e:for(t=e;null!==Ss;){var a=(r=Ss).sibling,i=r.return;if(Cs(r),r===t){Ss=null;break e}if(null!==a){a.return=i,Ss=a;break e}Ss=i}}}var eu={getCacheForType:function(e){var n=_a(Na),t=n.data.get(e);return void 0===t&&(t=e(),n.data.set(e,t)),t}},nu="function"===typeof WeakMap?WeakMap:Map,tu=0,ru=null,au=null,iu=0,ou=0,lu=null,su=!1,uu=!1,cu=!1,du=0,fu=0,pu=0,hu=0,mu=0,gu=0,vu=0,yu=null,bu=null,wu=!1,xu=0,ku=1/0,Su=null,Eu=null,Cu=0,_u=null,zu=null,Pu=0,ju=0,Tu=null,Ou=null,Nu=0,Au=null;function Ru(){if(0!==(2&tu)&&0!==iu)return iu&-iu;if(null!==R.T){return 0!==Da?Da:jc()}return Te()}function Lu(){0===gu&&(gu=0===(536870912&iu)||ia?ke():536870912);var e=al.current;return null!==e&&(e.flags|=32),gu}function Mu(e,n,t){(e!==ru||2!==ou&&9!==ou)&&null===e.cancelPendingCommit||($u(e,0),Uu(e,iu,gu,!1)),Ce(e,t),0!==(2&tu)&&e===ru||(e===ru&&(0===(2&tu)&&(hu|=t),4===fu&&Uu(e,iu,gu,!1)),kc(e))}function Du(e,n,t){if(0!==(6&tu))throw Error(o(327));for(var r=!t&&0===(124&n)&&0===(n&e.expiredLanes)||we(e,n),a=r?function(e,n){var t=tu;tu|=2;var r=Vu(),a=qu();ru!==e||iu!==n?(Su=null,ku=ne()+500,$u(e,n)):uu=we(e,n);e:for(;;)try{if(0!==ou&&null!==au){n=au;var i=lu;n:switch(ou){case 1:ou=0,lu=null,Ju(e,n,i,1);break;case 2:case 9:if(Ya(i)){ou=0,lu=null,Zu(n);break}n=function(){2!==ou&&9!==ou||ru!==e||(ou=7),kc(e)},i.then(n,n);break e;case 3:ou=7;break e;case 4:ou=5;break e;case 7:Ya(i)?(ou=0,lu=null,Zu(n)):(ou=0,lu=null,Ju(e,n,i,7));break;case 5:var l=null;switch(au.tag){case 26:l=au.memoizedState;case 5:case 27:var s=au;if(!l||Hd(l)){ou=0,lu=null;var u=s.sibling;if(null!==u)au=u;else{var c=s.return;null!==c?(au=c,ec(c)):au=null}break n}}ou=0,lu=null,Ju(e,n,i,5);break;case 6:ou=0,lu=null,Ju(e,n,i,6);break;case 8:Wu(),fu=6;break e;default:throw Error(o(462))}}Gu();break}catch(d){Bu(e,d)}return ya=va=null,R.H=r,R.A=a,tu=t,null!==au?0:(ru=null,iu=0,Pr(),fu)}(e,n):Qu(e,n,!0),i=r;;){if(0===a){uu&&!r&&Uu(e,n,0,!1);break}if(t=e.current.alternate,!i||Fu(t)){if(2===a){if(i=n,e.errorRecoveryDisabledLanes&i)var l=0;else l=0!==(l=-536870913&e.pendingLanes)?l:536870912&l?536870912:0;if(0!==l){n=l;e:{var s=e;a=yu;var u=s.current.memoizedState.isDehydrated;if(u&&($u(s,l).flags|=256),2!==(l=Qu(s,l,!1))){if(cu&&!u){s.errorRecoveryDisabledLanes|=i,hu|=i,a=4;break e}i=bu,bu=a,null!==i&&(null===bu?bu=i:bu.push.apply(bu,i))}a=l}if(i=!1,2!==a)continue}}if(1===a){$u(e,0),Uu(e,n,0,!0);break}e:{switch(r=e,i=a){case 0:case 1:throw Error(o(345));case 4:if((4194048&n)!==n)break;case 6:Uu(r,n,gu,!su);break e;case 2:bu=null;break;case 3:case 5:break;default:throw Error(o(329))}if((62914560&n)===n&&10<(a=xu+300-ne())){if(Uu(r,n,gu,!su),0!==be(r,0,!0))break e;r.timeoutHandle=sd(Iu.bind(null,r,t,bu,Su,wu,n,gu,hu,vu,su,i,2,-0,0),a)}else Iu(r,t,bu,Su,wu,n,gu,hu,vu,su,i,0,-0,0)}break}a=Qu(e,n,!1),i=!1}kc(e)}function Iu(e,n,t,r,a,i,l,s,u,c,d,f,p,h){if(e.timeoutHandle=-1,(8192&(f=n.subtreeFlags)||16785408===(16785408&f))&&(Wd={stylesheets:null,count:0,unsuspend:$d},Qs(n),null!==(f=function(){if(null===Wd)throw Error(o(475));var e=Wd;return e.stylesheets&&0===e.count&&qd(e,e.stylesheets),0<e.count?function(n){var t=setTimeout(function(){if(e.stylesheets&&qd(e,e.stylesheets),e.unsuspend){var n=e.unsuspend;e.unsuspend=null,n()}},6e4);return e.unsuspend=n,function(){e.unsuspend=null,clearTimeout(t)}}:null}())))return e.cancelPendingCommit=f(tc.bind(null,e,n,i,t,r,a,l,s,u,d,1,p,h)),void Uu(e,i,l,!c);tc(e,n,i,t,r,a,l,s,u)}function Fu(e){for(var n=e;;){var t=n.tag;if((0===t||11===t||15===t)&&16384&n.flags&&(null!==(t=n.updateQueue)&&null!==(t=t.stores)))for(var r=0;r<t.length;r++){var a=t[r],i=a.getSnapshot;a=a.value;try{if(!Yt(i(),a))return!1}catch(o){return!1}}if(t=n.child,16384&n.subtreeFlags&&null!==t)t.return=n,n=t;else{if(n===e)break;for(;null===n.sibling;){if(null===n.return||n.return===e)return!0;n=n.return}n.sibling.return=n.return,n=n.sibling}}return!0}function Uu(e,n,t,r){n&=~mu,n&=~hu,e.suspendedLanes|=n,e.pingedLanes&=~n,r&&(e.warmLanes|=n),r=e.expirationTimes;for(var a=n;0<a;){var i=31-pe(a),o=1<<i;r[i]=-1,a&=~o}0!==t&&_e(e,t,n)}function Hu(){return 0!==(6&tu)||(Sc(0,!1),!1)}function Wu(){if(null!==au){if(0===ou)var e=au.return;else ya=va=null,Di(e=au),Yo=null,Go=0,e=au;for(;null!==e;)is(e.alternate,e),e=e.return;au=null}}function $u(e,n){var t=e.timeoutHandle;-1!==t&&(e.timeoutHandle=-1,ud(t)),null!==(t=e.cancelPendingCommit)&&(e.cancelPendingCommit=null,t()),Wu(),ru=e,au=t=Ir(e.current,null),iu=n,ou=0,lu=null,su=!1,uu=we(e,n),cu=!1,vu=gu=mu=hu=pu=fu=0,bu=yu=null,wu=!1,0!==(8&n)&&(n|=32&n);var r=e.entangledLanes;if(0!==r)for(e=e.entanglements,r&=n;0<r;){var a=31-pe(r),i=1<<a;n|=e[a],r&=~i}return du=n,Pr(),t}function Bu(e,n){bi=null,R.H=Vo,n===Va||n===Ka?(n=Ja(),ou=3):n===qa?(n=Ja(),ou=4):ou=n===_l?8:null!==n&&"object"===typeof n&&"function"===typeof n.then?6:1,lu=n,null===au&&(fu=1,xl(e,Er(n,e.current)))}function Vu(){var e=R.H;return R.H=Vo,null===e?Vo:e}function qu(){var e=R.A;return R.A=eu,e}function Ku(){fu=4,su||(4194048&iu)!==iu&&null!==al.current||(uu=!0),0===(134217727&pu)&&0===(134217727&hu)||null===ru||Uu(ru,iu,gu,!1)}function Qu(e,n,t){var r=tu;tu|=2;var a=Vu(),i=qu();ru===e&&iu===n||(Su=null,$u(e,n)),n=!1;var o=fu;e:for(;;)try{if(0!==ou&&null!==au){var l=au,s=lu;switch(ou){case 8:Wu(),o=6;break e;case 3:case 2:case 9:case 6:null===al.current&&(n=!0);var u=ou;if(ou=0,lu=null,Ju(e,l,s,u),t&&uu){o=0;break e}break;default:u=ou,ou=0,lu=null,Ju(e,l,s,u)}}Yu(),o=fu;break}catch(c){Bu(e,c)}return n&&e.shellSuspendCounter++,ya=va=null,tu=r,R.H=a,R.A=i,null===au&&(ru=null,iu=0,Pr()),o}function Yu(){for(;null!==au;)Xu(au)}function Gu(){for(;null!==au&&!J();)Xu(au)}function Xu(e){var n=Xl(e.alternate,e,du);e.memoizedProps=e.pendingProps,null===n?ec(e):au=n}function Zu(e){var n=e,t=n.alternate;switch(n.tag){case 15:case 0:n=Ml(t,n,n.pendingProps,n.type,void 0,iu);break;case 11:n=Ml(t,n,n.pendingProps,n.type.render,n.ref,iu);break;case 5:Di(n);default:is(t,n),n=Xl(t,n=au=Fr(n,du),du)}e.memoizedProps=e.pendingProps,null===n?ec(e):au=n}function Ju(e,n,t,r){ya=va=null,Di(n),Yo=null,Go=0;var a=n.return;try{if(function(e,n,t,r,a){if(t.flags|=32768,null!==r&&"object"===typeof r&&"function"===typeof r.then){if(null!==(n=t.alternate)&&Sa(n,t,a,!0),null!==(t=al.current)){switch(t.tag){case 13:return null===il?Ku():null===t.alternate&&0===fu&&(fu=3),t.flags&=-257,t.flags|=65536,t.lanes=a,r===Qa?t.flags|=16384:(null===(n=t.updateQueue)?t.updateQueue=new Set([r]):n.add(r),dc(e,r,a)),!1;case 22:return t.flags|=65536,r===Qa?t.flags|=16384:(null===(n=t.updateQueue)?(n={transitions:null,markerInstances:null,retryQueue:new Set([r])},t.updateQueue=n):null===(t=n.retryQueue)?n.retryQueue=new Set([r]):t.add(r),dc(e,r,a)),!1}throw Error(o(435,t.tag))}return dc(e,r,a),Ku(),!1}if(ia)return null!==(n=al.current)?(0===(65536&n.flags)&&(n.flags|=256),n.flags|=65536,n.lanes=a,r!==sa&&ma(Er(e=Error(o(422),{cause:r}),t))):(r!==sa&&ma(Er(n=Error(o(423),{cause:r}),t)),(e=e.current.alternate).flags|=65536,a&=-a,e.lanes|=a,r=Er(r,t),li(e,a=Sl(e.stateNode,r,a)),4!==fu&&(fu=2)),!1;var i=Error(o(520),{cause:r});if(i=Er(i,t),null===yu?yu=[i]:yu.push(i),4!==fu&&(fu=2),null===n)return!0;r=Er(r,t),t=n;do{switch(t.tag){case 3:return t.flags|=65536,e=a&-a,t.lanes|=e,li(t,e=Sl(t.stateNode,r,e)),!1;case 1:if(n=t.type,i=t.stateNode,0===(128&t.flags)&&("function"===typeof n.getDerivedStateFromError||null!==i&&"function"===typeof i.componentDidCatch&&(null===Eu||!Eu.has(i))))return t.flags|=65536,a&=-a,t.lanes|=a,Cl(a=El(a),e,t,r),li(t,a),!1}t=t.return}while(null!==t);return!1}(e,a,n,t,iu))return fu=1,xl(e,Er(t,e.current)),void(au=null)}catch(i){if(null!==a)throw au=a,i;return fu=1,xl(e,Er(t,e.current)),void(au=null)}32768&n.flags?(ia||1===r?e=!0:uu||0!==(536870912&iu)?e=!1:(su=e=!0,(2===r||9===r||3===r||6===r)&&(null!==(r=al.current)&&13===r.tag&&(r.flags|=16384))),nc(n,e)):ec(n)}function ec(e){var n=e;do{if(0!==(32768&n.flags))return void nc(n,su);e=n.return;var t=rs(n.alternate,n,du);if(null!==t)return void(au=t);if(null!==(n=n.sibling))return void(au=n);au=n=e}while(null!==n);0===fu&&(fu=5)}function nc(e,n){do{var t=as(e.alternate,e);if(null!==t)return t.flags&=32767,void(au=t);if(null!==(t=e.return)&&(t.flags|=32768,t.subtreeFlags=0,t.deletions=null),!n&&null!==(e=e.sibling))return void(au=e);au=e=t}while(null!==e);fu=6,au=null}function tc(e,n,t,r,a,i,l,s,u){e.cancelPendingCommit=null;do{lc()}while(0!==Cu);if(0!==(6&tu))throw Error(o(327));if(null!==n){if(n===e.current)throw Error(o(177));if(i=n.lanes|n.childLanes,function(e,n,t,r,a,i){var o=e.pendingLanes;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=t,e.entangledLanes&=t,e.errorRecoveryDisabledLanes&=t,e.shellSuspendCounter=0;var l=e.entanglements,s=e.expirationTimes,u=e.hiddenUpdates;for(t=o&~t;0<t;){var c=31-pe(t),d=1<<c;l[c]=0,s[c]=-1;var f=u[c];if(null!==f)for(u[c]=null,c=0;c<f.length;c++){var p=f[c];null!==p&&(p.lane&=-536870913)}t&=~d}0!==r&&_e(e,r,0),0!==i&&0===a&&0!==e.tag&&(e.suspendedLanes|=i&~(o&~n))}(e,t,i|=zr,l,s,u),e===ru&&(au=ru=null,iu=0),zu=n,_u=e,Pu=t,ju=i,Tu=a,Ou=r,0!==(10256&n.subtreeFlags)||0!==(10256&n.flags)?(e.callbackNode=null,e.callbackPriority=0,X(ie,function(){return sc(),null})):(e.callbackNode=null,e.callbackPriority=0),r=0!==(13878&n.flags),0!==(13878&n.subtreeFlags)||r){r=R.T,R.T=null,a=L.p,L.p=2,l=tu,tu|=4;try{!function(e,n){if(e=e.containerInfo,nd=tf,nr(e=er(e))){if("selectionStart"in e)var t={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(t=(t=e.ownerDocument)&&t.defaultView||window).getSelection&&t.getSelection();if(r&&0!==r.rangeCount){t=r.anchorNode;var a=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{t.nodeType,i.nodeType}catch(g){t=null;break e}var l=0,s=-1,u=-1,c=0,d=0,f=e,p=null;n:for(;;){for(var h;f!==t||0!==a&&3!==f.nodeType||(s=l+a),f!==i||0!==r&&3!==f.nodeType||(u=l+r),3===f.nodeType&&(l+=f.nodeValue.length),null!==(h=f.firstChild);)p=f,f=h;for(;;){if(f===e)break n;if(p===t&&++c===a&&(s=l),p===i&&++d===r&&(u=l),null!==(h=f.nextSibling))break;p=(f=p).parentNode}f=h}t=-1===s||-1===u?null:{start:s,end:u}}else t=null}t=t||{start:0,end:0}}else t=null;for(td={focusedElem:e,selectionRange:t},tf=!1,Ss=n;null!==Ss;)if(e=(n=Ss).child,0!==(1024&n.subtreeFlags)&&null!==e)e.return=n,Ss=e;else for(;null!==Ss;){switch(i=(n=Ss).alternate,e=n.flags,n.tag){case 0:case 11:case 15:case 5:case 26:case 27:case 6:case 4:case 17:break;case 1:if(0!==(1024&e)&&null!==i){e=void 0,t=n,a=i.memoizedProps,i=i.memoizedState,r=t.stateNode;try{var m=gl(t.type,a,(t.elementType,t.type));e=r.getSnapshotBeforeUpdate(m,i),r.__reactInternalSnapshotBeforeUpdate=e}catch(v){cc(t,t.return,v)}}break;case 3:if(0!==(1024&e))if(9===(t=(e=n.stateNode.containerInfo).nodeType))md(e);else if(1===t)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":md(e);break;default:e.textContent=""}break;default:if(0!==(1024&e))throw Error(o(163))}if(null!==(e=n.sibling)){e.return=n.return,Ss=e;break}Ss=n.return}}(e,n)}finally{tu=l,L.p=a,R.T=r}}Cu=1,rc(),ac(),ic()}}function rc(){if(1===Cu){Cu=0;var e=_u,n=zu,t=0!==(13878&n.flags);if(0!==(13878&n.subtreeFlags)||t){t=R.T,R.T=null;var r=L.p;L.p=2;var a=tu;tu|=4;try{Rs(n,e);var i=td,o=er(e.containerInfo),l=i.focusedElem,s=i.selectionRange;if(o!==l&&l&&l.ownerDocument&&Jt(l.ownerDocument.documentElement,l)){if(null!==s&&nr(l)){var u=s.start,c=s.end;if(void 0===c&&(c=u),"selectionStart"in l)l.selectionStart=u,l.selectionEnd=Math.min(c,l.value.length);else{var d=l.ownerDocument||document,f=d&&d.defaultView||window;if(f.getSelection){var p=f.getSelection(),h=l.textContent.length,m=Math.min(s.start,h),g=void 0===s.end?m:Math.min(s.end,h);!p.extend&&m>g&&(o=g,g=m,m=o);var v=Zt(l,m),y=Zt(l,g);if(v&&y&&(1!==p.rangeCount||p.anchorNode!==v.node||p.anchorOffset!==v.offset||p.focusNode!==y.node||p.focusOffset!==y.offset)){var b=d.createRange();b.setStart(v.node,v.offset),p.removeAllRanges(),m>g?(p.addRange(b),p.extend(y.node,y.offset)):(b.setEnd(y.node,y.offset),p.addRange(b))}}}}for(d=[],p=l;p=p.parentNode;)1===p.nodeType&&d.push({element:p,left:p.scrollLeft,top:p.scrollTop});for("function"===typeof l.focus&&l.focus(),l=0;l<d.length;l++){var w=d[l];w.element.scrollLeft=w.left,w.element.scrollTop=w.top}}tf=!!nd,td=nd=null}finally{tu=a,L.p=r,R.T=t}}e.current=n,Cu=2}}function ac(){if(2===Cu){Cu=0;var e=_u,n=zu,t=0!==(8772&n.flags);if(0!==(8772&n.subtreeFlags)||t){t=R.T,R.T=null;var r=L.p;L.p=2;var a=tu;tu|=4;try{Es(e,n.alternate,n)}finally{tu=a,L.p=r,R.T=t}}Cu=3}}function ic(){if(4===Cu||3===Cu){Cu=0,ee();var e=_u,n=zu,t=Pu,r=Ou;0!==(10256&n.subtreeFlags)||0!==(10256&n.flags)?Cu=5:(Cu=0,zu=_u=null,oc(e,e.pendingLanes));var a=e.pendingLanes;if(0===a&&(Eu=null),je(t),n=n.stateNode,de&&"function"===typeof de.onCommitFiberRoot)try{de.onCommitFiberRoot(ce,n,void 0,128===(128&n.current.flags))}catch(s){}if(null!==r){n=R.T,a=L.p,L.p=2,R.T=null;try{for(var i=e.onRecoverableError,o=0;o<r.length;o++){var l=r[o];i(l.value,{componentStack:l.stack})}}finally{R.T=n,L.p=a}}0!==(3&Pu)&&lc(),kc(e),a=e.pendingLanes,0!==(4194090&t)&&0!==(42&a)?e===Au?Nu++:(Nu=0,Au=e):Nu=0,Sc(0,!1)}}function oc(e,n){0===(e.pooledCacheLanes&=n)&&(null!=(n=e.pooledCache)&&(e.pooledCache=null,Ra(n)))}function lc(e){return rc(),ac(),ic(),sc()}function sc(){if(5!==Cu)return!1;var e=_u,n=ju;ju=0;var t=je(Pu),r=R.T,a=L.p;try{L.p=32>t?32:t,R.T=null,t=Tu,Tu=null;var i=_u,l=Pu;if(Cu=0,zu=_u=null,Pu=0,0!==(6&tu))throw Error(o(331));var s=tu;if(tu|=4,Xs(i.current),$s(i,i.current,l,t),tu=s,Sc(0,!1),de&&"function"===typeof de.onPostCommitFiberRoot)try{de.onPostCommitFiberRoot(ce,i)}catch(u){}return!0}finally{L.p=a,R.T=r,oc(e,n)}}function uc(e,n,t){n=Er(t,n),null!==(e=ii(e,n=Sl(e.stateNode,n,2),2))&&(Ce(e,2),kc(e))}function cc(e,n,t){if(3===e.tag)uc(e,e,t);else for(;null!==n;){if(3===n.tag){uc(n,e,t);break}if(1===n.tag){var r=n.stateNode;if("function"===typeof n.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===Eu||!Eu.has(r))){e=Er(t,e),null!==(r=ii(n,t=El(2),2))&&(Cl(t,r,n,e),Ce(r,2),kc(r));break}}n=n.return}}function dc(e,n,t){var r=e.pingCache;if(null===r){r=e.pingCache=new nu;var a=new Set;r.set(n,a)}else void 0===(a=r.get(n))&&(a=new Set,r.set(n,a));a.has(t)||(cu=!0,a.add(t),e=fc.bind(null,e,n,t),n.then(e,e))}function fc(e,n,t){var r=e.pingCache;null!==r&&r.delete(n),e.pingedLanes|=e.suspendedLanes&t,e.warmLanes&=~t,ru===e&&(iu&t)===t&&(4===fu||3===fu&&(62914560&iu)===iu&&300>ne()-xu?0===(2&tu)&&$u(e,0):mu|=t,vu===iu&&(vu=0)),kc(e)}function pc(e,n){0===n&&(n=Se()),null!==(e=Or(e,n))&&(Ce(e,n),kc(e))}function hc(e){var n=e.memoizedState,t=0;null!==n&&(t=n.retryLane),pc(e,t)}function mc(e,n){var t=0;switch(e.tag){case 13:var r=e.stateNode,a=e.memoizedState;null!==a&&(t=a.retryLane);break;case 19:r=e.stateNode;break;case 22:r=e.stateNode._retryCache;break;default:throw Error(o(314))}null!==r&&r.delete(n),pc(e,t)}var gc=null,vc=null,yc=!1,bc=!1,wc=!1,xc=0;function kc(e){e!==vc&&null===e.next&&(null===vc?gc=vc=e:vc=vc.next=e),bc=!0,yc||(yc=!0,dd(function(){0!==(6&tu)?X(re,Ec):Cc()}))}function Sc(e,n){if(!wc&&bc){wc=!0;do{for(var t=!1,r=gc;null!==r;){if(!n)if(0!==e){var a=r.pendingLanes;if(0===a)var i=0;else{var o=r.suspendedLanes,l=r.pingedLanes;i=(1<<31-pe(42|e)+1)-1,i=201326741&(i&=a&~(o&~l))?201326741&i|1:i?2|i:0}0!==i&&(t=!0,Pc(r,i))}else i=iu,0===(3&(i=be(r,r===ru?i:0,null!==r.cancelPendingCommit||-1!==r.timeoutHandle)))||we(r,i)||(t=!0,Pc(r,i));r=r.next}}while(t);wc=!1}}function Ec(){Cc()}function Cc(){bc=yc=!1;var e=0;0!==xc&&(function(){var e=window.event;if(e&&"popstate"===e.type)return e!==ld&&(ld=e,!0);return ld=null,!1}()&&(e=xc),xc=0);for(var n=ne(),t=null,r=gc;null!==r;){var a=r.next,i=_c(r,n);0===i?(r.next=null,null===t?gc=a:t.next=a,null===a&&(vc=t)):(t=r,(0!==e||0!==(3&i))&&(bc=!0)),r=a}Sc(e,!1)}function _c(e,n){for(var t=e.suspendedLanes,r=e.pingedLanes,a=e.expirationTimes,i=-62914561&e.pendingLanes;0<i;){var o=31-pe(i),l=1<<o,s=a[o];-1===s?0!==(l&t)&&0===(l&r)||(a[o]=xe(l,n)):s<=n&&(e.expiredLanes|=l),i&=~l}if(t=iu,t=be(e,e===(n=ru)?t:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle),r=e.callbackNode,0===t||e===n&&(2===ou||9===ou)||null!==e.cancelPendingCommit)return null!==r&&null!==r&&Z(r),e.callbackNode=null,e.callbackPriority=0;if(0===(3&t)||we(e,t)){if((n=t&-t)===e.callbackPriority)return n;switch(null!==r&&Z(r),je(t)){case 2:case 8:t=ae;break;case 32:default:t=ie;break;case 268435456:t=le}return r=zc.bind(null,e),t=X(t,r),e.callbackPriority=n,e.callbackNode=t,n}return null!==r&&null!==r&&Z(r),e.callbackPriority=2,e.callbackNode=null,2}function zc(e,n){if(0!==Cu&&5!==Cu)return e.callbackNode=null,e.callbackPriority=0,null;var t=e.callbackNode;if(lc()&&e.callbackNode!==t)return null;var r=iu;return 0===(r=be(e,e===ru?r:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle))?null:(Du(e,r,n),_c(e,ne()),null!=e.callbackNode&&e.callbackNode===t?zc.bind(null,e):null)}function Pc(e,n){if(lc())return null;Du(e,n,!0)}function jc(){return 0===xc&&(xc=ke()),xc}function Tc(e){return null==e||"symbol"===typeof e||"boolean"===typeof e?null:"function"===typeof e?e:Tn(""+e)}function Oc(e,n){var t=n.ownerDocument.createElement("input");return t.name=n.name,t.value=n.value,e.id&&t.setAttribute("form",e.id),n.parentNode.insertBefore(t,n),e=new FormData(e),t.parentNode.removeChild(t),e}for(var Nc=0;Nc<xr.length;Nc++){var Ac=xr[Nc];kr(Ac.toLowerCase(),"on"+(Ac[0].toUpperCase()+Ac.slice(1)))}kr(pr,"onAnimationEnd"),kr(hr,"onAnimationIteration"),kr(mr,"onAnimationStart"),kr("dblclick","onDoubleClick"),kr("focusin","onFocus"),kr("focusout","onBlur"),kr(gr,"onTransitionRun"),kr(vr,"onTransitionStart"),kr(yr,"onTransitionCancel"),kr(br,"onTransitionEnd"),Ye("onMouseEnter",["mouseout","mouseover"]),Ye("onMouseLeave",["mouseout","mouseover"]),Ye("onPointerEnter",["pointerout","pointerover"]),Ye("onPointerLeave",["pointerout","pointerover"]),Qe("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Qe("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Qe("onBeforeInput",["compositionend","keypress","textInput","paste"]),Qe("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Qe("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Qe("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Rc="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Lc=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Rc));function Mc(e,n){n=0!==(4&n);for(var t=0;t<e.length;t++){var r=e[t],a=r.event;r=r.listeners;e:{var i=void 0;if(n)for(var o=r.length-1;0<=o;o--){var l=r[o],s=l.instance,u=l.currentTarget;if(l=l.listener,s!==i&&a.isPropagationStopped())break e;i=l,a.currentTarget=u;try{i(a)}catch(c){vl(c)}a.currentTarget=null,i=s}else for(o=0;o<r.length;o++){if(s=(l=r[o]).instance,u=l.currentTarget,l=l.listener,s!==i&&a.isPropagationStopped())break e;i=l,a.currentTarget=u;try{i(a)}catch(c){vl(c)}a.currentTarget=null,i=s}}}}function Dc(e,n){var t=n[Le];void 0===t&&(t=n[Le]=new Set);var r=e+"__bubble";t.has(r)||(Hc(n,e,2,!1),t.add(r))}function Ic(e,n,t){var r=0;n&&(r|=4),Hc(t,e,r,n)}var Fc="_reactListening"+Math.random().toString(36).slice(2);function Uc(e){if(!e[Fc]){e[Fc]=!0,qe.forEach(function(n){"selectionchange"!==n&&(Lc.has(n)||Ic(n,!1,e),Ic(n,!0,e))});var n=9===e.nodeType?e:e.ownerDocument;null===n||n[Fc]||(n[Fc]=!0,Ic("selectionchange",!1,n))}}function Hc(e,n,t,r){switch(cf(n)){case 2:var a=rf;break;case 8:a=af;break;default:a=of}t=a.bind(null,n,t,e),a=void 0,!Un||"touchstart"!==n&&"touchmove"!==n&&"wheel"!==n||(a=!0),r?void 0!==a?e.addEventListener(n,t,{capture:!0,passive:a}):e.addEventListener(n,t,!0):void 0!==a?e.addEventListener(n,t,{passive:a}):e.addEventListener(n,t,!1)}function Wc(e,n,t,r,a){var i=r;if(0===(1&n)&&0===(2&n)&&null!==r)e:for(;;){if(null===r)return;var o=r.tag;if(3===o||4===o){var l=r.stateNode.containerInfo;if(l===a)break;if(4===o)for(o=r.return;null!==o;){var u=o.tag;if((3===u||4===u)&&o.stateNode.containerInfo===a)return;o=o.return}for(;null!==l;){if(null===(o=He(l)))return;if(5===(u=o.tag)||6===u||26===u||27===u){r=i=o;continue e}l=l.parentNode}}r=r.return}Dn(function(){var r=i,a=Nn(t),o=[];e:{var l=wr.get(e);if(void 0!==l){var u=et,c=e;switch(e){case"keypress":if(0===qn(t))break e;case"keydown":case"keyup":u=mt;break;case"focusin":c="focus",u=ot;break;case"focusout":c="blur",u=ot;break;case"beforeblur":case"afterblur":u=ot;break;case"click":if(2===t.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":u=at;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":u=it;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":u=vt;break;case pr:case hr:case mr:u=lt;break;case br:u=yt;break;case"scroll":case"scrollend":u=tt;break;case"wheel":u=bt;break;case"copy":case"cut":case"paste":u=st;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":u=gt;break;case"toggle":case"beforetoggle":u=wt}var d=0!==(4&n),f=!d&&("scroll"===e||"scrollend"===e),p=d?null!==l?l+"Capture":null:l;d=[];for(var h,m=r;null!==m;){var g=m;if(h=g.stateNode,5!==(g=g.tag)&&26!==g&&27!==g||null===h||null===p||null!=(g=In(m,p))&&d.push($c(m,g,h)),f)break;m=m.return}0<d.length&&(l=new u(l,c,null,t,a),o.push({event:l,listeners:d}))}}if(0===(7&n)){if(u="mouseout"===e||"pointerout"===e,(!(l="mouseover"===e||"pointerover"===e)||t===On||!(c=t.relatedTarget||t.fromElement)||!He(c)&&!c[Re])&&(u||l)&&(l=a.window===a?a:(l=a.ownerDocument)?l.defaultView||l.parentWindow:window,u?(u=r,null!==(c=(c=t.relatedTarget||t.toElement)?He(c):null)&&(f=s(c),d=c.tag,c!==f||5!==d&&27!==d&&6!==d)&&(c=null)):(u=null,c=r),u!==c)){if(d=at,g="onMouseLeave",p="onMouseEnter",m="mouse","pointerout"!==e&&"pointerover"!==e||(d=gt,g="onPointerLeave",p="onPointerEnter",m="pointer"),f=null==u?l:$e(u),h=null==c?l:$e(c),(l=new d(g,m+"leave",u,t,a)).target=f,l.relatedTarget=h,g=null,He(a)===r&&((d=new d(p,m+"enter",c,t,a)).target=h,d.relatedTarget=f,g=d),f=g,u&&c)e:{for(p=c,m=0,h=d=u;h;h=Vc(h))m++;for(h=0,g=p;g;g=Vc(g))h++;for(;0<m-h;)d=Vc(d),m--;for(;0<h-m;)p=Vc(p),h--;for(;m--;){if(d===p||null!==p&&d===p.alternate)break e;d=Vc(d),p=Vc(p)}d=null}else d=null;null!==u&&qc(o,l,u,d,!1),null!==c&&null!==f&&qc(o,f,c,d,!0)}if("select"===(u=(l=r?$e(r):window).nodeName&&l.nodeName.toLowerCase())||"input"===u&&"file"===l.type)var v=It;else if(Nt(l))if(Ft)v=Qt;else{v=qt;var y=Vt}else!(u=l.nodeName)||"input"!==u.toLowerCase()||"checkbox"!==l.type&&"radio"!==l.type?r&&zn(r.elementType)&&(v=It):v=Kt;switch(v&&(v=v(e,r))?At(o,v,t,a):(y&&y(e,l,r),"focusout"===e&&r&&"number"===l.type&&null!=r.memoizedProps.value&&bn(l,"number",l.value)),y=r?$e(r):window,e){case"focusin":(Nt(y)||"true"===y.contentEditable)&&(rr=y,ar=r,ir=null);break;case"focusout":ir=ar=rr=null;break;case"mousedown":or=!0;break;case"contextmenu":case"mouseup":case"dragend":or=!1,lr(o,t,a);break;case"selectionchange":if(tr)break;case"keydown":case"keyup":lr(o,t,a)}var b;if(kt)e:{switch(e){case"compositionstart":var w="onCompositionStart";break e;case"compositionend":w="onCompositionEnd";break e;case"compositionupdate":w="onCompositionUpdate";break e}w=void 0}else Tt?Pt(e,t)&&(w="onCompositionEnd"):"keydown"===e&&229===t.keyCode&&(w="onCompositionStart");w&&(Ct&&"ko"!==t.locale&&(Tt||"onCompositionStart"!==w?"onCompositionEnd"===w&&Tt&&(b=Vn()):($n="value"in(Wn=a)?Wn.value:Wn.textContent,Tt=!0)),0<(y=Bc(r,w)).length&&(w=new ut(w,e,null,t,a),o.push({event:w,listeners:y}),b?w.data=b:null!==(b=jt(t))&&(w.data=b))),(b=Et?function(e,n){switch(e){case"compositionend":return jt(n);case"keypress":return 32!==n.which?null:(zt=!0,_t);case"textInput":return(e=n.data)===_t&&zt?null:e;default:return null}}(e,t):function(e,n){if(Tt)return"compositionend"===e||!kt&&Pt(e,n)?(e=Vn(),Bn=$n=Wn=null,Tt=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(n.ctrlKey||n.altKey||n.metaKey)||n.ctrlKey&&n.altKey){if(n.char&&1<n.char.length)return n.char;if(n.which)return String.fromCharCode(n.which)}return null;case"compositionend":return Ct&&"ko"!==n.locale?null:n.data}}(e,t))&&(0<(w=Bc(r,"onBeforeInput")).length&&(y=new ut("onBeforeInput","beforeinput",null,t,a),o.push({event:y,listeners:w}),y.data=b)),function(e,n,t,r,a){if("submit"===n&&t&&t.stateNode===a){var i=Tc((a[Ae]||null).action),o=r.submitter;o&&null!==(n=(n=o[Ae]||null)?Tc(n.formAction):o.getAttribute("formAction"))&&(i=n,o=null);var l=new et("action","action",null,r,a);e.push({event:l,listeners:[{instance:null,listener:function(){if(r.defaultPrevented){if(0!==xc){var e=o?Oc(a,o):new FormData(a);Oo(t,{pending:!0,data:e,method:a.method,action:i},null,e)}}else"function"===typeof i&&(l.preventDefault(),e=o?Oc(a,o):new FormData(a),Oo(t,{pending:!0,data:e,method:a.method,action:i},i,e))},currentTarget:a}]})}}(o,e,r,t,a)}Mc(o,n)})}function $c(e,n,t){return{instance:e,listener:n,currentTarget:t}}function Bc(e,n){for(var t=n+"Capture",r=[];null!==e;){var a=e,i=a.stateNode;if(5!==(a=a.tag)&&26!==a&&27!==a||null===i||(null!=(a=In(e,t))&&r.unshift($c(e,a,i)),null!=(a=In(e,n))&&r.push($c(e,a,i))),3===e.tag)return r;e=e.return}return[]}function Vc(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag&&27!==e.tag);return e||null}function qc(e,n,t,r,a){for(var i=n._reactName,o=[];null!==t&&t!==r;){var l=t,s=l.alternate,u=l.stateNode;if(l=l.tag,null!==s&&s===r)break;5!==l&&26!==l&&27!==l||null===u||(s=u,a?null!=(u=In(t,i))&&o.unshift($c(t,u,s)):a||null!=(u=In(t,i))&&o.push($c(t,u,s))),t=t.return}0!==o.length&&e.push({event:n,listeners:o})}var Kc=/\r\n?/g,Qc=/\u0000|\uFFFD/g;function Yc(e){return("string"===typeof e?e:""+e).replace(Kc,"\n").replace(Qc,"")}function Gc(e,n){return n=Yc(n),Yc(e)===n}function Xc(){}function Zc(e,n,t,r,a,i){switch(t){case"children":"string"===typeof r?"body"===n||"textarea"===n&&""===r||Sn(e,r):("number"===typeof r||"bigint"===typeof r)&&"body"!==n&&Sn(e,""+r);break;case"className":tn(e,"class",r);break;case"tabIndex":tn(e,"tabindex",r);break;case"dir":case"role":case"viewBox":case"width":case"height":tn(e,t,r);break;case"style":_n(e,r,i);break;case"data":if("object"!==n){tn(e,"data",r);break}case"src":case"href":if(""===r&&("a"!==n||"href"!==t)){e.removeAttribute(t);break}if(null==r||"function"===typeof r||"symbol"===typeof r||"boolean"===typeof r){e.removeAttribute(t);break}r=Tn(""+r),e.setAttribute(t,r);break;case"action":case"formAction":if("function"===typeof r){e.setAttribute(t,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}if("function"===typeof i&&("formAction"===t?("input"!==n&&Zc(e,n,"name",a.name,a,null),Zc(e,n,"formEncType",a.formEncType,a,null),Zc(e,n,"formMethod",a.formMethod,a,null),Zc(e,n,"formTarget",a.formTarget,a,null)):(Zc(e,n,"encType",a.encType,a,null),Zc(e,n,"method",a.method,a,null),Zc(e,n,"target",a.target,a,null))),null==r||"symbol"===typeof r||"boolean"===typeof r){e.removeAttribute(t);break}r=Tn(""+r),e.setAttribute(t,r);break;case"onClick":null!=r&&(e.onclick=Xc);break;case"onScroll":null!=r&&Dc("scroll",e);break;case"onScrollEnd":null!=r&&Dc("scrollend",e);break;case"dangerouslySetInnerHTML":if(null!=r){if("object"!==typeof r||!("__html"in r))throw Error(o(61));if(null!=(t=r.__html)){if(null!=a.children)throw Error(o(60));e.innerHTML=t}}break;case"multiple":e.multiple=r&&"function"!==typeof r&&"symbol"!==typeof r;break;case"muted":e.muted=r&&"function"!==typeof r&&"symbol"!==typeof r;break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":case"autoFocus":break;case"xlinkHref":if(null==r||"function"===typeof r||"boolean"===typeof r||"symbol"===typeof r){e.removeAttribute("xlink:href");break}t=Tn(""+r),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",t);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":null!=r&&"function"!==typeof r&&"symbol"!==typeof r?e.setAttribute(t,""+r):e.removeAttribute(t);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":r&&"function"!==typeof r&&"symbol"!==typeof r?e.setAttribute(t,""):e.removeAttribute(t);break;case"capture":case"download":!0===r?e.setAttribute(t,""):!1!==r&&null!=r&&"function"!==typeof r&&"symbol"!==typeof r?e.setAttribute(t,r):e.removeAttribute(t);break;case"cols":case"rows":case"size":case"span":null!=r&&"function"!==typeof r&&"symbol"!==typeof r&&!isNaN(r)&&1<=r?e.setAttribute(t,r):e.removeAttribute(t);break;case"rowSpan":case"start":null==r||"function"===typeof r||"symbol"===typeof r||isNaN(r)?e.removeAttribute(t):e.setAttribute(t,r);break;case"popover":Dc("beforetoggle",e),Dc("toggle",e),nn(e,"popover",r);break;case"xlinkActuate":rn(e,"http://www.w3.org/1999/xlink","xlink:actuate",r);break;case"xlinkArcrole":rn(e,"http://www.w3.org/1999/xlink","xlink:arcrole",r);break;case"xlinkRole":rn(e,"http://www.w3.org/1999/xlink","xlink:role",r);break;case"xlinkShow":rn(e,"http://www.w3.org/1999/xlink","xlink:show",r);break;case"xlinkTitle":rn(e,"http://www.w3.org/1999/xlink","xlink:title",r);break;case"xlinkType":rn(e,"http://www.w3.org/1999/xlink","xlink:type",r);break;case"xmlBase":rn(e,"http://www.w3.org/XML/1998/namespace","xml:base",r);break;case"xmlLang":rn(e,"http://www.w3.org/XML/1998/namespace","xml:lang",r);break;case"xmlSpace":rn(e,"http://www.w3.org/XML/1998/namespace","xml:space",r);break;case"is":nn(e,"is",r);break;case"innerText":case"textContent":break;default:(!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&nn(e,t=Pn.get(t)||t,r)}}function Jc(e,n,t,r,a,i){switch(t){case"style":_n(e,r,i);break;case"dangerouslySetInnerHTML":if(null!=r){if("object"!==typeof r||!("__html"in r))throw Error(o(61));if(null!=(t=r.__html)){if(null!=a.children)throw Error(o(60));e.innerHTML=t}}break;case"children":"string"===typeof r?Sn(e,r):("number"===typeof r||"bigint"===typeof r)&&Sn(e,""+r);break;case"onScroll":null!=r&&Dc("scroll",e);break;case"onScrollEnd":null!=r&&Dc("scrollend",e);break;case"onClick":null!=r&&(e.onclick=Xc);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":case"innerText":case"textContent":break;default:Ke.hasOwnProperty(t)||("o"!==t[0]||"n"!==t[1]||(a=t.endsWith("Capture"),n=t.slice(2,a?t.length-7:void 0),"function"===typeof(i=null!=(i=e[Ae]||null)?i[t]:null)&&e.removeEventListener(n,i,a),"function"!==typeof r)?t in e?e[t]=r:!0===r?e.setAttribute(t,""):nn(e,t,r):("function"!==typeof i&&null!==i&&(t in e?e[t]=null:e.hasAttribute(t)&&e.removeAttribute(t)),e.addEventListener(n,r,a)))}}function ed(e,n,t){switch(n){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Dc("error",e),Dc("load",e);var r,a=!1,i=!1;for(r in t)if(t.hasOwnProperty(r)){var l=t[r];if(null!=l)switch(r){case"src":a=!0;break;case"srcSet":i=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(o(137,n));default:Zc(e,n,r,l,t,null)}}return i&&Zc(e,n,"srcSet",t.srcSet,t,null),void(a&&Zc(e,n,"src",t.src,t,null));case"input":Dc("invalid",e);var s=r=l=i=null,u=null,c=null;for(a in t)if(t.hasOwnProperty(a)){var d=t[a];if(null!=d)switch(a){case"name":i=d;break;case"type":l=d;break;case"checked":u=d;break;case"defaultChecked":c=d;break;case"value":r=d;break;case"defaultValue":s=d;break;case"children":case"dangerouslySetInnerHTML":if(null!=d)throw Error(o(137,n));break;default:Zc(e,n,a,d,t,null)}}return yn(e,r,s,u,c,l,i,!1),void fn(e);case"select":for(i in Dc("invalid",e),a=l=r=null,t)if(t.hasOwnProperty(i)&&null!=(s=t[i]))switch(i){case"value":r=s;break;case"defaultValue":l=s;break;case"multiple":a=s;default:Zc(e,n,i,s,t,null)}return n=r,t=l,e.multiple=!!a,void(null!=n?wn(e,!!a,n,!1):null!=t&&wn(e,!!a,t,!0));case"textarea":for(l in Dc("invalid",e),r=i=a=null,t)if(t.hasOwnProperty(l)&&null!=(s=t[l]))switch(l){case"value":a=s;break;case"defaultValue":i=s;break;case"children":r=s;break;case"dangerouslySetInnerHTML":if(null!=s)throw Error(o(91));break;default:Zc(e,n,l,s,t,null)}return kn(e,a,i,r),void fn(e);case"option":for(u in t)if(t.hasOwnProperty(u)&&null!=(a=t[u]))if("selected"===u)e.selected=a&&"function"!==typeof a&&"symbol"!==typeof a;else Zc(e,n,u,a,t,null);return;case"dialog":Dc("beforetoggle",e),Dc("toggle",e),Dc("cancel",e),Dc("close",e);break;case"iframe":case"object":Dc("load",e);break;case"video":case"audio":for(a=0;a<Rc.length;a++)Dc(Rc[a],e);break;case"image":Dc("error",e),Dc("load",e);break;case"details":Dc("toggle",e);break;case"embed":case"source":case"link":Dc("error",e),Dc("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(c in t)if(t.hasOwnProperty(c)&&null!=(a=t[c]))switch(c){case"children":case"dangerouslySetInnerHTML":throw Error(o(137,n));default:Zc(e,n,c,a,t,null)}return;default:if(zn(n)){for(d in t)t.hasOwnProperty(d)&&(void 0!==(a=t[d])&&Jc(e,n,d,a,t,void 0));return}}for(s in t)t.hasOwnProperty(s)&&(null!=(a=t[s])&&Zc(e,n,s,a,t,null))}var nd=null,td=null;function rd(e){return 9===e.nodeType?e:e.ownerDocument}function ad(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function id(e,n){if(0===e)switch(n){case"svg":return 1;case"math":return 2;default:return 0}return 1===e&&"foreignObject"===n?0:e}function od(e,n){return"textarea"===e||"noscript"===e||"string"===typeof n.children||"number"===typeof n.children||"bigint"===typeof n.children||"object"===typeof n.dangerouslySetInnerHTML&&null!==n.dangerouslySetInnerHTML&&null!=n.dangerouslySetInnerHTML.__html}var ld=null;var sd="function"===typeof setTimeout?setTimeout:void 0,ud="function"===typeof clearTimeout?clearTimeout:void 0,cd="function"===typeof Promise?Promise:void 0,dd="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof cd?function(e){return cd.resolve(null).then(e).catch(fd)}:sd;function fd(e){setTimeout(function(){throw e})}function pd(e){return"head"===e}function hd(e,n){var t=n,r=0,a=0;do{var i=t.nextSibling;if(e.removeChild(t),i&&8===i.nodeType)if("/$"===(t=i.data)){if(0<r&&8>r){t=r;var o=e.ownerDocument;if(1&t&&xd(o.documentElement),2&t&&xd(o.body),4&t)for(xd(t=o.head),o=t.firstChild;o;){var l=o.nextSibling,s=o.nodeName;o[Fe]||"SCRIPT"===s||"STYLE"===s||"LINK"===s&&"stylesheet"===o.rel.toLowerCase()||t.removeChild(o),o=l}}if(0===a)return e.removeChild(i),void Pf(n);a--}else"$"===t||"$?"===t||"$!"===t?a++:r=t.charCodeAt(0)-48;else r=0;t=i}while(t);Pf(n)}function md(e){var n=e.firstChild;for(n&&10===n.nodeType&&(n=n.nextSibling);n;){var t=n;switch(n=n.nextSibling,t.nodeName){case"HTML":case"HEAD":case"BODY":md(t),Ue(t);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if("stylesheet"===t.rel.toLowerCase())continue}e.removeChild(t)}}function gd(e){return"$!"===e.data||"$?"===e.data&&"complete"===e.ownerDocument.readyState}function vd(e){for(;null!=e;e=e.nextSibling){var n=e.nodeType;if(1===n||3===n)break;if(8===n){if("$"===(n=e.data)||"$!"===n||"$?"===n||"F!"===n||"F"===n)break;if("/$"===n)return null}}return e}var yd=null;function bd(e){e=e.previousSibling;for(var n=0;e;){if(8===e.nodeType){var t=e.data;if("$"===t||"$!"===t||"$?"===t){if(0===n)return e;n--}else"/$"===t&&n++}e=e.previousSibling}return null}function wd(e,n,t){switch(n=rd(t),e){case"html":if(!(e=n.documentElement))throw Error(o(452));return e;case"head":if(!(e=n.head))throw Error(o(453));return e;case"body":if(!(e=n.body))throw Error(o(454));return e;default:throw Error(o(451))}}function xd(e){for(var n=e.attributes;n.length;)e.removeAttributeNode(n[0]);Ue(e)}var kd=new Map,Sd=new Set;function Ed(e){return"function"===typeof e.getRootNode?e.getRootNode():9===e.nodeType?e:e.ownerDocument}var Cd=L.d;L.d={f:function(){var e=Cd.f(),n=Hu();return e||n},r:function(e){var n=We(e);null!==n&&5===n.tag&&"form"===n.type?Ao(n):Cd.r(e)},D:function(e){Cd.D(e),zd("dns-prefetch",e,null)},C:function(e,n){Cd.C(e,n),zd("preconnect",e,n)},L:function(e,n,t){Cd.L(e,n,t);var r=_d;if(r&&e&&n){var a='link[rel="preload"][as="'+gn(n)+'"]';"image"===n&&t&&t.imageSrcSet?(a+='[imagesrcset="'+gn(t.imageSrcSet)+'"]',"string"===typeof t.imageSizes&&(a+='[imagesizes="'+gn(t.imageSizes)+'"]')):a+='[href="'+gn(e)+'"]';var i=a;switch(n){case"style":i=jd(e);break;case"script":i=Nd(e)}kd.has(i)||(e=f({rel:"preload",href:"image"===n&&t&&t.imageSrcSet?void 0:e,as:n},t),kd.set(i,e),null!==r.querySelector(a)||"style"===n&&r.querySelector(Td(i))||"script"===n&&r.querySelector(Ad(i))||(ed(n=r.createElement("link"),"link",e),Ve(n),r.head.appendChild(n)))}},m:function(e,n){Cd.m(e,n);var t=_d;if(t&&e){var r=n&&"string"===typeof n.as?n.as:"script",a='link[rel="modulepreload"][as="'+gn(r)+'"][href="'+gn(e)+'"]',i=a;switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":i=Nd(e)}if(!kd.has(i)&&(e=f({rel:"modulepreload",href:e},n),kd.set(i,e),null===t.querySelector(a))){switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(t.querySelector(Ad(i)))return}ed(r=t.createElement("link"),"link",e),Ve(r),t.head.appendChild(r)}}},X:function(e,n){Cd.X(e,n);var t=_d;if(t&&e){var r=Be(t).hoistableScripts,a=Nd(e),i=r.get(a);i||((i=t.querySelector(Ad(a)))||(e=f({src:e,async:!0},n),(n=kd.get(a))&&Dd(e,n),Ve(i=t.createElement("script")),ed(i,"link",e),t.head.appendChild(i)),i={type:"script",instance:i,count:1,state:null},r.set(a,i))}},S:function(e,n,t){Cd.S(e,n,t);var r=_d;if(r&&e){var a=Be(r).hoistableStyles,i=jd(e);n=n||"default";var o=a.get(i);if(!o){var l={loading:0,preload:null};if(o=r.querySelector(Td(i)))l.loading=5;else{e=f({rel:"stylesheet",href:e,"data-precedence":n},t),(t=kd.get(i))&&Md(e,t);var s=o=r.createElement("link");Ve(s),ed(s,"link",e),s._p=new Promise(function(e,n){s.onload=e,s.onerror=n}),s.addEventListener("load",function(){l.loading|=1}),s.addEventListener("error",function(){l.loading|=2}),l.loading|=4,Ld(o,n,r)}o={type:"stylesheet",instance:o,count:1,state:l},a.set(i,o)}}},M:function(e,n){Cd.M(e,n);var t=_d;if(t&&e){var r=Be(t).hoistableScripts,a=Nd(e),i=r.get(a);i||((i=t.querySelector(Ad(a)))||(e=f({src:e,async:!0,type:"module"},n),(n=kd.get(a))&&Dd(e,n),Ve(i=t.createElement("script")),ed(i,"link",e),t.head.appendChild(i)),i={type:"script",instance:i,count:1,state:null},r.set(a,i))}}};var _d="undefined"===typeof document?null:document;function zd(e,n,t){var r=_d;if(r&&"string"===typeof n&&n){var a=gn(n);a='link[rel="'+e+'"][href="'+a+'"]',"string"===typeof t&&(a+='[crossorigin="'+t+'"]'),Sd.has(a)||(Sd.add(a),e={rel:e,crossOrigin:t,href:n},null===r.querySelector(a)&&(ed(n=r.createElement("link"),"link",e),Ve(n),r.head.appendChild(n)))}}function Pd(e,n,t,r){var a,i,l,s,u=(u=B.current)?Ed(u):null;if(!u)throw Error(o(446));switch(e){case"meta":case"title":return null;case"style":return"string"===typeof t.precedence&&"string"===typeof t.href?(n=jd(t.href),(r=(t=Be(u).hoistableStyles).get(n))||(r={type:"style",instance:null,count:0,state:null},t.set(n,r)),r):{type:"void",instance:null,count:0,state:null};case"link":if("stylesheet"===t.rel&&"string"===typeof t.href&&"string"===typeof t.precedence){e=jd(t.href);var c=Be(u).hoistableStyles,d=c.get(e);if(d||(u=u.ownerDocument||u,d={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},c.set(e,d),(c=u.querySelector(Td(e)))&&!c._p&&(d.instance=c,d.state.loading=5),kd.has(e)||(t={rel:"preload",as:"style",href:t.href,crossOrigin:t.crossOrigin,integrity:t.integrity,media:t.media,hrefLang:t.hrefLang,referrerPolicy:t.referrerPolicy},kd.set(e,t),c||(a=u,i=e,l=t,s=d.state,a.querySelector('link[rel="preload"][as="style"]['+i+"]")?s.loading=1:(i=a.createElement("link"),s.preload=i,i.addEventListener("load",function(){return s.loading|=1}),i.addEventListener("error",function(){return s.loading|=2}),ed(i,"link",l),Ve(i),a.head.appendChild(i))))),n&&null===r)throw Error(o(528,""));return d}if(n&&null!==r)throw Error(o(529,""));return null;case"script":return n=t.async,"string"===typeof(t=t.src)&&n&&"function"!==typeof n&&"symbol"!==typeof n?(n=Nd(t),(r=(t=Be(u).hoistableScripts).get(n))||(r={type:"script",instance:null,count:0,state:null},t.set(n,r)),r):{type:"void",instance:null,count:0,state:null};default:throw Error(o(444,e))}}function jd(e){return'href="'+gn(e)+'"'}function Td(e){return'link[rel="stylesheet"]['+e+"]"}function Od(e){return f({},e,{"data-precedence":e.precedence,precedence:null})}function Nd(e){return'[src="'+gn(e)+'"]'}function Ad(e){return"script[async]"+e}function Rd(e,n,t){if(n.count++,null===n.instance)switch(n.type){case"style":var r=e.querySelector('style[data-href~="'+gn(t.href)+'"]');if(r)return n.instance=r,Ve(r),r;var a=f({},t,{"data-href":t.href,"data-precedence":t.precedence,href:null,precedence:null});return Ve(r=(e.ownerDocument||e).createElement("style")),ed(r,"style",a),Ld(r,t.precedence,e),n.instance=r;case"stylesheet":a=jd(t.href);var i=e.querySelector(Td(a));if(i)return n.state.loading|=4,n.instance=i,Ve(i),i;r=Od(t),(a=kd.get(a))&&Md(r,a),Ve(i=(e.ownerDocument||e).createElement("link"));var l=i;return l._p=new Promise(function(e,n){l.onload=e,l.onerror=n}),ed(i,"link",r),n.state.loading|=4,Ld(i,t.precedence,e),n.instance=i;case"script":return i=Nd(t.src),(a=e.querySelector(Ad(i)))?(n.instance=a,Ve(a),a):(r=t,(a=kd.get(i))&&Dd(r=f({},t),a),Ve(a=(e=e.ownerDocument||e).createElement("script")),ed(a,"link",r),e.head.appendChild(a),n.instance=a);case"void":return null;default:throw Error(o(443,n.type))}else"stylesheet"===n.type&&0===(4&n.state.loading)&&(r=n.instance,n.state.loading|=4,Ld(r,t.precedence,e));return n.instance}function Ld(e,n,t){for(var r=t.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),a=r.length?r[r.length-1]:null,i=a,o=0;o<r.length;o++){var l=r[o];if(l.dataset.precedence===n)i=l;else if(i!==a)break}i?i.parentNode.insertBefore(e,i.nextSibling):(n=9===t.nodeType?t.head:t).insertBefore(e,n.firstChild)}function Md(e,n){null==e.crossOrigin&&(e.crossOrigin=n.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=n.referrerPolicy),null==e.title&&(e.title=n.title)}function Dd(e,n){null==e.crossOrigin&&(e.crossOrigin=n.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=n.referrerPolicy),null==e.integrity&&(e.integrity=n.integrity)}var Id=null;function Fd(e,n,t){if(null===Id){var r=new Map,a=Id=new Map;a.set(t,r)}else(r=(a=Id).get(t))||(r=new Map,a.set(t,r));if(r.has(e))return r;for(r.set(e,null),t=t.getElementsByTagName(e),a=0;a<t.length;a++){var i=t[a];if(!(i[Fe]||i[Ne]||"link"===e&&"stylesheet"===i.getAttribute("rel"))&&"http://www.w3.org/2000/svg"!==i.namespaceURI){var o=i.getAttribute(n)||"";o=e+o;var l=r.get(o);l?l.push(i):r.set(o,[i])}}return r}function Ud(e,n,t){(e=e.ownerDocument||e).head.insertBefore(t,"title"===n?e.querySelector("head > title"):null)}function Hd(e){return"stylesheet"!==e.type||0!==(3&e.state.loading)}var Wd=null;function $d(){}function Bd(){if(this.count--,0===this.count)if(this.stylesheets)qd(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}var Vd=null;function qd(e,n){e.stylesheets=null,null!==e.unsuspend&&(e.count++,Vd=new Map,n.forEach(Kd,e),Vd=null,Bd.call(e))}function Kd(e,n){if(!(4&n.state.loading)){var t=Vd.get(e);if(t)var r=t.get(null);else{t=new Map,Vd.set(e,t);for(var a=e.querySelectorAll("link[data-precedence],style[data-precedence]"),i=0;i<a.length;i++){var o=a[i];"LINK"!==o.nodeName&&"not all"===o.getAttribute("media")||(t.set(o.dataset.precedence,o),r=o)}r&&t.set(null,r)}o=(a=n.instance).getAttribute("data-precedence"),(i=t.get(o)||r)===r&&t.set(null,a),t.set(o,a),this.count++,r=Bd.bind(this),a.addEventListener("load",r),a.addEventListener("error",r),i?i.parentNode.insertBefore(a,i.nextSibling):(e=9===e.nodeType?e.head:e).insertBefore(a,e.firstChild),n.state.loading|=4}}var Qd={$$typeof:x,Provider:null,Consumer:null,_currentValue:M,_currentValue2:M,_threadCount:0};function Yd(e,n,t,r,a,i,o,l){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Ee(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ee(0),this.hiddenUpdates=Ee(null),this.identifierPrefix=r,this.onUncaughtError=a,this.onCaughtError=i,this.onRecoverableError=o,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=l,this.incompleteTransitions=new Map}function Gd(e,n,t,r,a,i,o,l,s,u,c,d){return e=new Yd(e,n,t,o,l,s,u,d),n=1,!0===i&&(n|=24),i=Mr(3,null,null,n),e.current=i,i.stateNode=e,(n=Aa()).refCount++,e.pooledCache=n,n.refCount++,i.memoizedState={element:r,isDehydrated:t,cache:n},ti(i),e}function Xd(e){return e?e=Rr:Rr}function Zd(e,n,t,r,a,i){a=Xd(a),null===r.context?r.context=a:r.pendingContext=a,(r=ai(n)).payload={element:t},null!==(i=void 0===i?null:i)&&(r.callback=i),null!==(t=ii(e,r,n))&&(Mu(t,0,n),oi(t,e,n))}function Jd(e,n){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var t=e.retryLane;e.retryLane=0!==t&&t<n?t:n}}function ef(e,n){Jd(e,n),(e=e.alternate)&&Jd(e,n)}function nf(e){if(13===e.tag){var n=Or(e,67108864);null!==n&&Mu(n,0,67108864),ef(e,67108864)}}var tf=!0;function rf(e,n,t,r){var a=R.T;R.T=null;var i=L.p;try{L.p=2,of(e,n,t,r)}finally{L.p=i,R.T=a}}function af(e,n,t,r){var a=R.T;R.T=null;var i=L.p;try{L.p=8,of(e,n,t,r)}finally{L.p=i,R.T=a}}function of(e,n,t,r){if(tf){var a=lf(r);if(null===a)Wc(e,n,r,sf,t),bf(e,r);else if(function(e,n,t,r,a){switch(n){case"focusin":return ff=wf(ff,e,n,t,r,a),!0;case"dragenter":return pf=wf(pf,e,n,t,r,a),!0;case"mouseover":return hf=wf(hf,e,n,t,r,a),!0;case"pointerover":var i=a.pointerId;return mf.set(i,wf(mf.get(i)||null,e,n,t,r,a)),!0;case"gotpointercapture":return i=a.pointerId,gf.set(i,wf(gf.get(i)||null,e,n,t,r,a)),!0}return!1}(a,e,n,t,r))r.stopPropagation();else if(bf(e,r),4&n&&-1<yf.indexOf(e)){for(;null!==a;){var i=We(a);if(null!==i)switch(i.tag){case 3:if((i=i.stateNode).current.memoizedState.isDehydrated){var o=ye(i.pendingLanes);if(0!==o){var l=i;for(l.pendingLanes|=2,l.entangledLanes|=2;o;){var s=1<<31-pe(o);l.entanglements[1]|=s,o&=~s}kc(i),0===(6&tu)&&(ku=ne()+500,Sc(0,!1))}}break;case 13:null!==(l=Or(i,2))&&Mu(l,0,2),Hu(),ef(i,2)}if(null===(i=lf(r))&&Wc(e,n,r,sf,t),i===a)break;a=i}null!==a&&r.stopPropagation()}else Wc(e,n,r,null,t)}}function lf(e){return uf(e=Nn(e))}var sf=null;function uf(e){if(sf=null,null!==(e=He(e))){var n=s(e);if(null===n)e=null;else{var t=n.tag;if(13===t){if(null!==(e=u(n)))return e;e=null}else if(3===t){if(n.stateNode.current.memoizedState.isDehydrated)return 3===n.tag?n.stateNode.containerInfo:null;e=null}else n!==e&&(e=null)}}return sf=e,null}function cf(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(te()){case re:return 2;case ae:return 8;case ie:case oe:return 32;case le:return 268435456;default:return 32}default:return 32}}var df=!1,ff=null,pf=null,hf=null,mf=new Map,gf=new Map,vf=[],yf="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function bf(e,n){switch(e){case"focusin":case"focusout":ff=null;break;case"dragenter":case"dragleave":pf=null;break;case"mouseover":case"mouseout":hf=null;break;case"pointerover":case"pointerout":mf.delete(n.pointerId);break;case"gotpointercapture":case"lostpointercapture":gf.delete(n.pointerId)}}function wf(e,n,t,r,a,i){return null===e||e.nativeEvent!==i?(e={blockedOn:n,domEventName:t,eventSystemFlags:r,nativeEvent:i,targetContainers:[a]},null!==n&&(null!==(n=We(n))&&nf(n)),e):(e.eventSystemFlags|=r,n=e.targetContainers,null!==a&&-1===n.indexOf(a)&&n.push(a),e)}function xf(e){var n=He(e.target);if(null!==n){var t=s(n);if(null!==t)if(13===(n=t.tag)){if(null!==(n=u(t)))return e.blockedOn=n,void function(e,n){var t=L.p;try{return L.p=e,n()}finally{L.p=t}}(e.priority,function(){if(13===t.tag){var e=Ru();e=Pe(e);var n=Or(t,e);null!==n&&Mu(n,0,e),ef(t,e)}})}else if(3===n&&t.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===t.tag?t.stateNode.containerInfo:null)}e.blockedOn=null}function kf(e){if(null!==e.blockedOn)return!1;for(var n=e.targetContainers;0<n.length;){var t=lf(e.nativeEvent);if(null!==t)return null!==(n=We(t))&&nf(n),e.blockedOn=t,!1;var r=new(t=e.nativeEvent).constructor(t.type,t);On=r,t.target.dispatchEvent(r),On=null,n.shift()}return!0}function Sf(e,n,t){kf(e)&&t.delete(n)}function Ef(){df=!1,null!==ff&&kf(ff)&&(ff=null),null!==pf&&kf(pf)&&(pf=null),null!==hf&&kf(hf)&&(hf=null),mf.forEach(Sf),gf.forEach(Sf)}function Cf(e,n){e.blockedOn===n&&(e.blockedOn=null,df||(df=!0,r.unstable_scheduleCallback(r.unstable_NormalPriority,Ef)))}var _f=null;function zf(e){_f!==e&&(_f=e,r.unstable_scheduleCallback(r.unstable_NormalPriority,function(){_f===e&&(_f=null);for(var n=0;n<e.length;n+=3){var t=e[n],r=e[n+1],a=e[n+2];if("function"!==typeof r){if(null===uf(r||t))continue;break}var i=We(t);null!==i&&(e.splice(n,3),n-=3,Oo(i,{pending:!0,data:a,method:t.method,action:r},r,a))}}))}function Pf(e){function n(n){return Cf(n,e)}null!==ff&&Cf(ff,e),null!==pf&&Cf(pf,e),null!==hf&&Cf(hf,e),mf.forEach(n),gf.forEach(n);for(var t=0;t<vf.length;t++){var r=vf[t];r.blockedOn===e&&(r.blockedOn=null)}for(;0<vf.length&&null===(t=vf[0]).blockedOn;)xf(t),null===t.blockedOn&&vf.shift();if(null!=(t=(e.ownerDocument||e).$$reactFormReplay))for(r=0;r<t.length;r+=3){var a=t[r],i=t[r+1],o=a[Ae]||null;if("function"===typeof i)o||zf(t);else if(o){var l=null;if(i&&i.hasAttribute("formAction")){if(a=i,o=i[Ae]||null)l=o.formAction;else if(null!==uf(a))continue}else l=o.action;"function"===typeof l?t[r+1]=l:(t.splice(r,3),r-=3),zf(t)}}}function jf(e){this._internalRoot=e}function Tf(e){this._internalRoot=e}Tf.prototype.render=jf.prototype.render=function(e){var n=this._internalRoot;if(null===n)throw Error(o(409));Zd(n.current,Ru(),e,n,null,null)},Tf.prototype.unmount=jf.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var n=e.containerInfo;Zd(e.current,2,null,e,null,null),Hu(),n[Re]=null}},Tf.prototype.unstable_scheduleHydration=function(e){if(e){var n=Te();e={blockedOn:null,target:e,priority:n};for(var t=0;t<vf.length&&0!==n&&n<vf[t].priority;t++);vf.splice(t,0,e),0===t&&xf(e)}};var Of=a.version;if("19.1.0"!==Of)throw Error(o(527,Of,"19.1.0"));L.findDOMNode=function(e){var n=e._reactInternals;if(void 0===n){if("function"===typeof e.render)throw Error(o(188));throw e=Object.keys(e).join(","),Error(o(268,e))}return e=function(e){var n=e.alternate;if(!n){if(null===(n=s(e)))throw Error(o(188));return n!==e?null:e}for(var t=e,r=n;;){var a=t.return;if(null===a)break;var i=a.alternate;if(null===i){if(null!==(r=a.return)){t=r;continue}break}if(a.child===i.child){for(i=a.child;i;){if(i===t)return c(a),e;if(i===r)return c(a),n;i=i.sibling}throw Error(o(188))}if(t.return!==r.return)t=a,r=i;else{for(var l=!1,u=a.child;u;){if(u===t){l=!0,t=a,r=i;break}if(u===r){l=!0,r=a,t=i;break}u=u.sibling}if(!l){for(u=i.child;u;){if(u===t){l=!0,t=i,r=a;break}if(u===r){l=!0,r=i,t=a;break}u=u.sibling}if(!l)throw Error(o(189))}}if(t.alternate!==r)throw Error(o(190))}if(3!==t.tag)throw Error(o(188));return t.stateNode.current===t?e:n}(n),e=null===(e=null!==e?d(e):null)?null:e.stateNode};var Nf={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:R,reconcilerVersion:"19.1.0"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var Af=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Af.isDisabled&&Af.supportsFiber)try{ce=Af.inject(Nf),de=Af}catch(Lf){}}n.createRoot=function(e,n){if(!l(e))throw Error(o(299));var t=!1,r="",a=yl,i=bl,s=wl;return null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(t=!0),void 0!==n.identifierPrefix&&(r=n.identifierPrefix),void 0!==n.onUncaughtError&&(a=n.onUncaughtError),void 0!==n.onCaughtError&&(i=n.onCaughtError),void 0!==n.onRecoverableError&&(s=n.onRecoverableError),void 0!==n.unstable_transitionCallbacks&&n.unstable_transitionCallbacks),n=Gd(e,1,!1,null,0,t,r,a,i,s,0,null),e[Re]=n.current,Uc(e),new jf(n)},n.hydrateRoot=function(e,n,t){if(!l(e))throw Error(o(299));var r=!1,a="",i=yl,s=bl,u=wl,c=null;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(r=!0),void 0!==t.identifierPrefix&&(a=t.identifierPrefix),void 0!==t.onUncaughtError&&(i=t.onUncaughtError),void 0!==t.onCaughtError&&(s=t.onCaughtError),void 0!==t.onRecoverableError&&(u=t.onRecoverableError),void 0!==t.unstable_transitionCallbacks&&t.unstable_transitionCallbacks,void 0!==t.formState&&(c=t.formState)),(n=Gd(e,1,!0,n,0,r,a,i,s,u,0,c)).context=Xd(null),t=n.current,(a=ai(r=Pe(r=Ru()))).callback=null,ii(t,a,r),t=r,n.current.lanes=t,Ce(n,t),kc(n),e[Re]=n.current,Uc(e),new Tf(n)},n.version="19.1.0"},29:function(e,n,t){var r;r=function(e){return function(e){var n={};function t(r){if(n[r])return n[r].exports;var a=n[r]={i:r,l:!1,exports:{}};return e[r].call(a.exports,a,a.exports,t),a.l=!0,a.exports}return t.m=e,t.c=n,t.d=function(e,n,r){t.o(e,n)||Object.defineProperty(e,n,{enumerable:!0,get:r})},t.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},t.t=function(e,n){if(1&n&&(e=t(e)),8&n)return e;if(4&n&&"object"===typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(t.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&n&&"string"!=typeof e)for(var a in e)t.d(r,a,function(n){return e[n]}.bind(null,a));return r},t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,"a",n),n},t.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},t.p="",t(t.s="./src/react-webcam.tsx")}({"./src/react-webcam.tsx":function(e,n,t){"use strict";t.r(n);var r=t("react"),a=function(){var e=function(n,t){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,n){e.__proto__=n}||function(e,n){for(var t in n)n.hasOwnProperty(t)&&(e[t]=n[t])},e(n,t)};return function(n,t){function r(){this.constructor=n}e(n,t),n.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}}(),i=function(){return i=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++)for(var a in n=arguments[t])Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a]);return e},i.apply(this,arguments)},o=function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var a=0;for(r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]])}return t};function l(){return!(!navigator.mediaDevices||!navigator.mediaDevices.getUserMedia)}"undefined"!==typeof window&&(void 0===navigator.mediaDevices&&(navigator.mediaDevices={}),void 0===navigator.mediaDevices.getUserMedia&&(navigator.mediaDevices.getUserMedia=function(e){var n=navigator.getUserMedia||navigator.webkitGetUserMedia||navigator.mozGetUserMedia||navigator.msGetUserMedia;return n?new Promise(function(t,r){n.call(navigator,e,t,r)}):Promise.reject(new Error("getUserMedia is not implemented in this browser"))}));var s=function(e){function n(n){var t=e.call(this,n)||this;return t.canvas=null,t.ctx=null,t.requestUserMediaId=0,t.unmounted=!1,t.state={hasUserMedia:!1},t}return a(n,e),n.prototype.componentDidMount=function(){var e=this.state,n=this.props;this.unmounted=!1,l()?(e.hasUserMedia||this.requestUserMedia(),n.children&&"function"!=typeof n.children&&console.warn("children must be a function")):n.onUserMediaError("getUserMedia not supported")},n.prototype.componentDidUpdate=function(e){var n=this.props;if(l()){var t=JSON.stringify(e.audioConstraints)!==JSON.stringify(n.audioConstraints),r=JSON.stringify(e.videoConstraints)!==JSON.stringify(n.videoConstraints),a=e.minScreenshotWidth!==n.minScreenshotWidth,i=e.minScreenshotHeight!==n.minScreenshotHeight;(r||a||i)&&(this.canvas=null,this.ctx=null),(t||r)&&(this.stopAndCleanup(),this.requestUserMedia())}else n.onUserMediaError("getUserMedia not supported")},n.prototype.componentWillUnmount=function(){this.unmounted=!0,this.stopAndCleanup()},n.stopMediaStream=function(e){e&&(e.getVideoTracks&&e.getAudioTracks?(e.getVideoTracks().map(function(n){e.removeTrack(n),n.stop()}),e.getAudioTracks().map(function(n){e.removeTrack(n),n.stop()})):e.stop())},n.prototype.stopAndCleanup=function(){var e=this.state;e.hasUserMedia&&(n.stopMediaStream(this.stream),e.src&&window.URL.revokeObjectURL(e.src))},n.prototype.getScreenshot=function(e){var n=this.state,t=this.props;if(!n.hasUserMedia)return null;var r=this.getCanvas(e);return r&&r.toDataURL(t.screenshotFormat,t.screenshotQuality)},n.prototype.getCanvas=function(e){var n=this.state,t=this.props;if(!this.video)return null;if(!n.hasUserMedia||!this.video.videoHeight)return null;if(!this.ctx){var r=this.video.videoWidth,a=this.video.videoHeight;if(!this.props.forceScreenshotSourceSize){var i=r/a;a=(r=t.minScreenshotWidth||this.video.clientWidth)/i,t.minScreenshotHeight&&a<t.minScreenshotHeight&&(r=(a=t.minScreenshotHeight)*i)}this.canvas=document.createElement("canvas"),this.canvas.width=(null===e||void 0===e?void 0:e.width)||r,this.canvas.height=(null===e||void 0===e?void 0:e.height)||a,this.ctx=this.canvas.getContext("2d")}var o=this.ctx,l=this.canvas;return o&&l&&(l.width=(null===e||void 0===e?void 0:e.width)||l.width,l.height=(null===e||void 0===e?void 0:e.height)||l.height,t.mirrored&&(o.translate(l.width,0),o.scale(-1,1)),o.imageSmoothingEnabled=t.imageSmoothing,o.drawImage(this.video,0,0,(null===e||void 0===e?void 0:e.width)||l.width,(null===e||void 0===e?void 0:e.height)||l.height),t.mirrored&&(o.scale(-1,1),o.translate(-l.width,0))),l},n.prototype.requestUserMedia=function(){var e=this,t=this.props,r=function(r,a){var i={video:"undefined"===typeof a||a};t.audio&&(i.audio="undefined"===typeof r||r),e.requestUserMediaId++;var o=e.requestUserMediaId;navigator.mediaDevices.getUserMedia(i).then(function(t){e.unmounted||o!==e.requestUserMediaId?n.stopMediaStream(t):e.handleUserMedia(null,t)}).catch(function(n){e.handleUserMedia(n)})};if("mediaDevices"in navigator)r(t.audioConstraints,t.videoConstraints);else{var a=function(e){return{optional:[{sourceId:e}]}},i=function(e){var n=e.deviceId;return"string"===typeof n?n:Array.isArray(n)&&n.length>0?n[0]:"object"===typeof n&&n.ideal?n.ideal:null};MediaStreamTrack.getSources(function(e){var n=null,o=null;e.forEach(function(e){"audio"===e.kind?n=e.id:"video"===e.kind&&(o=e.id)});var l=i(t.audioConstraints);l&&(n=l);var s=i(t.videoConstraints);s&&(o=s),r(a(n),a(o))})}},n.prototype.handleUserMedia=function(e,n){var t=this.props;if(e||!n)return this.setState({hasUserMedia:!1}),void t.onUserMediaError(e);this.stream=n;try{this.video&&(this.video.srcObject=n),this.setState({hasUserMedia:!0})}catch(r){this.setState({hasUserMedia:!0,src:window.URL.createObjectURL(n)})}t.onUserMedia(n)},n.prototype.render=function(){var e=this,n=this.state,t=this.props,a=t.audio,l=(t.forceScreenshotSourceSize,t.disablePictureInPicture),s=(t.onUserMedia,t.onUserMediaError,t.screenshotFormat,t.screenshotQuality,t.minScreenshotWidth,t.minScreenshotHeight,t.audioConstraints,t.videoConstraints,t.imageSmoothing,t.mirrored),u=t.style,c=void 0===u?{}:u,d=t.children,f=o(t,["audio","forceScreenshotSourceSize","disablePictureInPicture","onUserMedia","onUserMediaError","screenshotFormat","screenshotQuality","minScreenshotWidth","minScreenshotHeight","audioConstraints","videoConstraints","imageSmoothing","mirrored","style","children"]),p=s?i(i({},c),{transform:(c.transform||"")+" scaleX(-1)"}):c,h={getScreenshot:this.getScreenshot.bind(this)};return r.createElement(r.Fragment,null,r.createElement("video",i({autoPlay:!0,disablePictureInPicture:l,src:n.src,muted:!a,playsInline:!0,ref:function(n){e.video=n},style:p},f)),d&&d(h))},n.defaultProps={audio:!1,disablePictureInPicture:!1,forceScreenshotSourceSize:!1,imageSmoothing:!0,mirrored:!1,onUserMedia:function(){},onUserMediaError:function(){},screenshotFormat:"image/webp",screenshotQuality:.92},n}(r.Component);n.default=s},react:function(n,t){n.exports=e}}).default},e.exports=r(t(43))},43:(e,n,t)=>{"use strict";e.exports=t(288)},288:(e,n)=>{"use strict";var t=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),l=Symbol.for("react.consumer"),s=Symbol.for("react.context"),u=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),p=Symbol.iterator;var h={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,g={};function v(e,n,t){this.props=e,this.context=n,this.refs=g,this.updater=t||h}function y(){}function b(e,n,t){this.props=e,this.context=n,this.refs=g,this.updater=t||h}v.prototype.isReactComponent={},v.prototype.setState=function(e,n){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,n,"setState")},v.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},y.prototype=v.prototype;var w=b.prototype=new y;w.constructor=b,m(w,v.prototype),w.isPureReactComponent=!0;var x=Array.isArray,k={H:null,A:null,T:null,S:null,V:null},S=Object.prototype.hasOwnProperty;function E(e,n,r,a,i,o){return r=o.ref,{$$typeof:t,type:e,key:n,ref:void 0!==r?r:null,props:o}}function C(e){return"object"===typeof e&&null!==e&&e.$$typeof===t}var _=/\/+/g;function z(e,n){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var n={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(e){return n[e]})}(""+e.key):n.toString(36)}function P(){}function j(e,n,a,i,o){var l=typeof e;"undefined"!==l&&"boolean"!==l||(e=null);var s,u,c=!1;if(null===e)c=!0;else switch(l){case"bigint":case"string":case"number":c=!0;break;case"object":switch(e.$$typeof){case t:case r:c=!0;break;case f:return j((c=e._init)(e._payload),n,a,i,o)}}if(c)return o=o(e),c=""===i?"."+z(e,0):i,x(o)?(a="",null!=c&&(a=c.replace(_,"$&/")+"/"),j(o,n,a,"",function(e){return e})):null!=o&&(C(o)&&(s=o,u=a+(null==o.key||e&&e.key===o.key?"":(""+o.key).replace(_,"$&/")+"/")+c,o=E(s.type,u,void 0,0,0,s.props)),n.push(o)),1;c=0;var d,h=""===i?".":i+":";if(x(e))for(var m=0;m<e.length;m++)c+=j(i=e[m],n,a,l=h+z(i,m),o);else if("function"===typeof(m=null===(d=e)||"object"!==typeof d?null:"function"===typeof(d=p&&d[p]||d["@@iterator"])?d:null))for(e=m.call(e),m=0;!(i=e.next()).done;)c+=j(i=i.value,n,a,l=h+z(i,m++),o);else if("object"===l){if("function"===typeof e.then)return j(function(e){switch(e.status){case"fulfilled":return e.value;case"rejected":throw e.reason;default:switch("string"===typeof e.status?e.then(P,P):(e.status="pending",e.then(function(n){"pending"===e.status&&(e.status="fulfilled",e.value=n)},function(n){"pending"===e.status&&(e.status="rejected",e.reason=n)})),e.status){case"fulfilled":return e.value;case"rejected":throw e.reason}}throw e}(e),n,a,i,o);throw n=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===n?"object with keys {"+Object.keys(e).join(", ")+"}":n)+"). If you meant to render a collection of children, use an array instead.")}return c}function T(e,n,t){if(null==e)return e;var r=[],a=0;return j(e,r,"","",function(e){return n.call(t,e,a++)}),r}function O(e){if(-1===e._status){var n=e._result;(n=n()).then(function(n){0!==e._status&&-1!==e._status||(e._status=1,e._result=n)},function(n){0!==e._status&&-1!==e._status||(e._status=2,e._result=n)}),-1===e._status&&(e._status=0,e._result=n)}if(1===e._status)return e._result.default;throw e._result}var N="function"===typeof reportError?reportError:function(e){if("object"===typeof window&&"function"===typeof window.ErrorEvent){var n=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"===typeof e&&null!==e&&"string"===typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(n))return}else if("object"===typeof process&&"function"===typeof process.emit)return void process.emit("uncaughtException",e);console.error(e)};function A(){}n.Children={map:T,forEach:function(e,n,t){T(e,function(){n.apply(this,arguments)},t)},count:function(e){var n=0;return T(e,function(){n++}),n},toArray:function(e){return T(e,function(e){return e})||[]},only:function(e){if(!C(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},n.Component=v,n.Fragment=a,n.Profiler=o,n.PureComponent=b,n.StrictMode=i,n.Suspense=c,n.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=k,n.__COMPILER_RUNTIME={__proto__:null,c:function(e){return k.H.useMemoCache(e)}},n.cache=function(e){return function(){return e.apply(null,arguments)}},n.cloneElement=function(e,n,t){if(null===e||void 0===e)throw Error("The argument must be a React element, but you passed "+e+".");var r=m({},e.props),a=e.key;if(null!=n)for(i in void 0!==n.ref&&void 0,void 0!==n.key&&(a=""+n.key),n)!S.call(n,i)||"key"===i||"__self"===i||"__source"===i||"ref"===i&&void 0===n.ref||(r[i]=n[i]);var i=arguments.length-2;if(1===i)r.children=t;else if(1<i){for(var o=Array(i),l=0;l<i;l++)o[l]=arguments[l+2];r.children=o}return E(e.type,a,void 0,0,0,r)},n.createContext=function(e){return(e={$$typeof:s,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null}).Provider=e,e.Consumer={$$typeof:l,_context:e},e},n.createElement=function(e,n,t){var r,a={},i=null;if(null!=n)for(r in void 0!==n.key&&(i=""+n.key),n)S.call(n,r)&&"key"!==r&&"__self"!==r&&"__source"!==r&&(a[r]=n[r]);var o=arguments.length-2;if(1===o)a.children=t;else if(1<o){for(var l=Array(o),s=0;s<o;s++)l[s]=arguments[s+2];a.children=l}if(e&&e.defaultProps)for(r in o=e.defaultProps)void 0===a[r]&&(a[r]=o[r]);return E(e,i,void 0,0,0,a)},n.createRef=function(){return{current:null}},n.forwardRef=function(e){return{$$typeof:u,render:e}},n.isValidElement=C,n.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:O}},n.memo=function(e,n){return{$$typeof:d,type:e,compare:void 0===n?null:n}},n.startTransition=function(e){var n=k.T,t={};k.T=t;try{var r=e(),a=k.S;null!==a&&a(t,r),"object"===typeof r&&null!==r&&"function"===typeof r.then&&r.then(A,N)}catch(i){N(i)}finally{k.T=n}},n.unstable_useCacheRefresh=function(){return k.H.useCacheRefresh()},n.use=function(e){return k.H.use(e)},n.useActionState=function(e,n,t){return k.H.useActionState(e,n,t)},n.useCallback=function(e,n){return k.H.useCallback(e,n)},n.useContext=function(e){return k.H.useContext(e)},n.useDebugValue=function(){},n.useDeferredValue=function(e,n){return k.H.useDeferredValue(e,n)},n.useEffect=function(e,n,t){var r=k.H;if("function"===typeof t)throw Error("useEffect CRUD overload is not enabled in this build of React.");return r.useEffect(e,n)},n.useId=function(){return k.H.useId()},n.useImperativeHandle=function(e,n,t){return k.H.useImperativeHandle(e,n,t)},n.useInsertionEffect=function(e,n){return k.H.useInsertionEffect(e,n)},n.useLayoutEffect=function(e,n){return k.H.useLayoutEffect(e,n)},n.useMemo=function(e,n){return k.H.useMemo(e,n)},n.useOptimistic=function(e,n){return k.H.useOptimistic(e,n)},n.useReducer=function(e,n,t){return k.H.useReducer(e,n,t)},n.useRef=function(e){return k.H.useRef(e)},n.useState=function(e){return k.H.useState(e)},n.useSyncExternalStore=function(e,n,t){return k.H.useSyncExternalStore(e,n,t)},n.useTransition=function(){return k.H.useTransition()},n.version="19.1.0"},324:e=>{e.exports=function(e,n,t,r){var a=t?t.call(r,e,n):void 0;if(void 0!==a)return!!a;if(e===n)return!0;if("object"!==typeof e||!e||"object"!==typeof n||!n)return!1;var i=Object.keys(e),o=Object.keys(n);if(i.length!==o.length)return!1;for(var l=Object.prototype.hasOwnProperty.bind(n),s=0;s<i.length;s++){var u=i[s];if(!l(u))return!1;var c=e[u],d=n[u];if(!1===(a=t?t.call(r,c,d,u):void 0)||void 0===a&&c!==d)return!1}return!0}},391:(e,n,t)=>{"use strict";!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(n){console.error(n)}}(),e.exports=t(4)},579:(e,n,t)=>{"use strict";e.exports=t(799)},672:(e,n,t)=>{"use strict";var r=t(43);function a(e){var n="https://react.dev/errors/"+e;if(1<arguments.length){n+="?args[]="+encodeURIComponent(arguments[1]);for(var t=2;t<arguments.length;t++)n+="&args[]="+encodeURIComponent(arguments[t])}return"Minified React error #"+e+"; visit "+n+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function i(){}var o={d:{f:i,r:function(){throw Error(a(522))},D:i,C:i,L:i,m:i,X:i,S:i,M:i},p:0,findDOMNode:null},l=Symbol.for("react.portal");var s=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function u(e,n){return"font"===e?"":"string"===typeof n?"use-credentials"===n?n:"":void 0}n.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=o,n.createPortal=function(e,n){var t=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!n||1!==n.nodeType&&9!==n.nodeType&&11!==n.nodeType)throw Error(a(299));return function(e,n,t){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:l,key:null==r?null:""+r,children:e,containerInfo:n,implementation:t}}(e,n,null,t)},n.flushSync=function(e){var n=s.T,t=o.p;try{if(s.T=null,o.p=2,e)return e()}finally{s.T=n,o.p=t,o.d.f()}},n.preconnect=function(e,n){"string"===typeof e&&(n?n="string"===typeof(n=n.crossOrigin)?"use-credentials"===n?n:"":void 0:n=null,o.d.C(e,n))},n.prefetchDNS=function(e){"string"===typeof e&&o.d.D(e)},n.preinit=function(e,n){if("string"===typeof e&&n&&"string"===typeof n.as){var t=n.as,r=u(t,n.crossOrigin),a="string"===typeof n.integrity?n.integrity:void 0,i="string"===typeof n.fetchPriority?n.fetchPriority:void 0;"style"===t?o.d.S(e,"string"===typeof n.precedence?n.precedence:void 0,{crossOrigin:r,integrity:a,fetchPriority:i}):"script"===t&&o.d.X(e,{crossOrigin:r,integrity:a,fetchPriority:i,nonce:"string"===typeof n.nonce?n.nonce:void 0})}},n.preinitModule=function(e,n){if("string"===typeof e)if("object"===typeof n&&null!==n){if(null==n.as||"script"===n.as){var t=u(n.as,n.crossOrigin);o.d.M(e,{crossOrigin:t,integrity:"string"===typeof n.integrity?n.integrity:void 0,nonce:"string"===typeof n.nonce?n.nonce:void 0})}}else null==n&&o.d.M(e)},n.preload=function(e,n){if("string"===typeof e&&"object"===typeof n&&null!==n&&"string"===typeof n.as){var t=n.as,r=u(t,n.crossOrigin);o.d.L(e,t,{crossOrigin:r,integrity:"string"===typeof n.integrity?n.integrity:void 0,nonce:"string"===typeof n.nonce?n.nonce:void 0,type:"string"===typeof n.type?n.type:void 0,fetchPriority:"string"===typeof n.fetchPriority?n.fetchPriority:void 0,referrerPolicy:"string"===typeof n.referrerPolicy?n.referrerPolicy:void 0,imageSrcSet:"string"===typeof n.imageSrcSet?n.imageSrcSet:void 0,imageSizes:"string"===typeof n.imageSizes?n.imageSizes:void 0,media:"string"===typeof n.media?n.media:void 0})}},n.preloadModule=function(e,n){if("string"===typeof e)if(n){var t=u(n.as,n.crossOrigin);o.d.m(e,{as:"string"===typeof n.as&&"script"!==n.as?n.as:void 0,crossOrigin:t,integrity:"string"===typeof n.integrity?n.integrity:void 0})}else o.d.m(e)},n.requestFormReset=function(e){o.d.r(e)},n.unstable_batchedUpdates=function(e,n){return e(n)},n.useFormState=function(e,n,t){return s.H.useFormState(e,n,t)},n.useFormStatus=function(){return s.H.useHostTransitionStatus()},n.version="19.1.0"},799:(e,n)=>{"use strict";var t=Symbol.for("react.transitional.element"),r=Symbol.for("react.fragment");function a(e,n,r){var a=null;if(void 0!==r&&(a=""+r),void 0!==n.key&&(a=""+n.key),"key"in n)for(var i in r={},n)"key"!==i&&(r[i]=n[i]);else r=n;return n=r.ref,{$$typeof:t,type:e,key:a,ref:void 0!==n?n:null,props:r}}n.Fragment=r,n.jsx=a,n.jsxs=a},853:(e,n,t)=>{"use strict";e.exports=t(896)},896:(e,n)=>{"use strict";function t(e,n){var t=e.length;e.push(n);e:for(;0<t;){var r=t-1>>>1,a=e[r];if(!(0<i(a,n)))break e;e[r]=n,e[t]=a,t=r}}function r(e){return 0===e.length?null:e[0]}function a(e){if(0===e.length)return null;var n=e[0],t=e.pop();if(t!==n){e[0]=t;e:for(var r=0,a=e.length,o=a>>>1;r<o;){var l=2*(r+1)-1,s=e[l],u=l+1,c=e[u];if(0>i(s,t))u<a&&0>i(c,s)?(e[r]=c,e[u]=t,r=u):(e[r]=s,e[l]=t,r=l);else{if(!(u<a&&0>i(c,t)))break e;e[r]=c,e[u]=t,r=u}}}return n}function i(e,n){var t=e.sortIndex-n.sortIndex;return 0!==t?t:e.id-n.id}if(n.unstable_now=void 0,"object"===typeof performance&&"function"===typeof performance.now){var o=performance;n.unstable_now=function(){return o.now()}}else{var l=Date,s=l.now();n.unstable_now=function(){return l.now()-s}}var u=[],c=[],d=1,f=null,p=3,h=!1,m=!1,g=!1,v=!1,y="function"===typeof setTimeout?setTimeout:null,b="function"===typeof clearTimeout?clearTimeout:null,w="undefined"!==typeof setImmediate?setImmediate:null;function x(e){for(var n=r(c);null!==n;){if(null===n.callback)a(c);else{if(!(n.startTime<=e))break;a(c),n.sortIndex=n.expirationTime,t(u,n)}n=r(c)}}function k(e){if(g=!1,x(e),!m)if(null!==r(u))m=!0,E||(E=!0,S());else{var n=r(c);null!==n&&N(k,n.startTime-e)}}var S,E=!1,C=-1,_=5,z=-1;function P(){return!!v||!(n.unstable_now()-z<_)}function j(){if(v=!1,E){var e=n.unstable_now();z=e;var t=!0;try{e:{m=!1,g&&(g=!1,b(C),C=-1),h=!0;var i=p;try{n:{for(x(e),f=r(u);null!==f&&!(f.expirationTime>e&&P());){var o=f.callback;if("function"===typeof o){f.callback=null,p=f.priorityLevel;var l=o(f.expirationTime<=e);if(e=n.unstable_now(),"function"===typeof l){f.callback=l,x(e),t=!0;break n}f===r(u)&&a(u),x(e)}else a(u);f=r(u)}if(null!==f)t=!0;else{var s=r(c);null!==s&&N(k,s.startTime-e),t=!1}}break e}finally{f=null,p=i,h=!1}t=void 0}}finally{t?S():E=!1}}}if("function"===typeof w)S=function(){w(j)};else if("undefined"!==typeof MessageChannel){var T=new MessageChannel,O=T.port2;T.port1.onmessage=j,S=function(){O.postMessage(null)}}else S=function(){y(j,0)};function N(e,t){C=y(function(){e(n.unstable_now())},t)}n.unstable_IdlePriority=5,n.unstable_ImmediatePriority=1,n.unstable_LowPriority=4,n.unstable_NormalPriority=3,n.unstable_Profiling=null,n.unstable_UserBlockingPriority=2,n.unstable_cancelCallback=function(e){e.callback=null},n.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):_=0<e?Math.floor(1e3/e):5},n.unstable_getCurrentPriorityLevel=function(){return p},n.unstable_next=function(e){switch(p){case 1:case 2:case 3:var n=3;break;default:n=p}var t=p;p=n;try{return e()}finally{p=t}},n.unstable_requestPaint=function(){v=!0},n.unstable_runWithPriority=function(e,n){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var t=p;p=e;try{return n()}finally{p=t}},n.unstable_scheduleCallback=function(e,a,i){var o=n.unstable_now();switch("object"===typeof i&&null!==i?i="number"===typeof(i=i.delay)&&0<i?o+i:o:i=o,e){case 1:var l=-1;break;case 2:l=250;break;case 5:l=1073741823;break;case 4:l=1e4;break;default:l=5e3}return e={id:d++,callback:a,priorityLevel:e,startTime:i,expirationTime:l=i+l,sortIndex:-1},i>o?(e.sortIndex=i,t(c,e),null===r(u)&&e===r(c)&&(g?(b(C),C=-1):g=!0,N(k,i-o))):(e.sortIndex=l,t(u,e),m||h||(m=!0,E||(E=!0,S()))),e},n.unstable_shouldYield=P,n.unstable_wrapCallback=function(e){var n=p;return function(){var t=p;p=n;try{return e.apply(this,arguments)}finally{p=t}}}},950:(e,n,t)=>{"use strict";!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(n){console.error(n)}}(),e.exports=t(672)}},n={};function t(r){var a=n[r];if(void 0!==a)return a.exports;var i=n[r]={exports:{}};return e[r].call(i.exports,i,i.exports,t),i.exports}t.m=e,t.n=e=>{var n=e&&e.__esModule?()=>e.default:()=>e;return t.d(n,{a:n}),n},t.d=(e,n)=>{for(var r in n)t.o(n,r)&&!t.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:n[r]})},t.f={},t.e=e=>Promise.all(Object.keys(t.f).reduce((n,r)=>(t.f[r](e,n),n),[])),t.u=e=>"static/js/"+e+".a21126ec.chunk.js",t.miniCssF=e=>{},t.o=(e,n)=>Object.prototype.hasOwnProperty.call(e,n),(()=>{var e={},n="training-frontend:";t.l=(r,a,i,o)=>{if(e[r])e[r].push(a);else{var l,s;if(void 0!==i)for(var u=document.getElementsByTagName("script"),c=0;c<u.length;c++){var d=u[c];if(d.getAttribute("src")==r||d.getAttribute("data-webpack")==n+i){l=d;break}}l||(s=!0,(l=document.createElement("script")).charset="utf-8",l.timeout=120,t.nc&&l.setAttribute("nonce",t.nc),l.setAttribute("data-webpack",n+i),l.src=r),e[r]=[a];var f=(n,t)=>{l.onerror=l.onload=null,clearTimeout(p);var a=e[r];if(delete e[r],l.parentNode&&l.parentNode.removeChild(l),a&&a.forEach(e=>e(t)),n)return n(t)},p=setTimeout(f.bind(null,void 0,{type:"timeout",target:l}),12e4);l.onerror=f.bind(null,l.onerror),l.onload=f.bind(null,l.onload),s&&document.head.appendChild(l)}}})(),t.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},t.p="/",(()=>{var e={792:0};t.f.j=(n,r)=>{var a=t.o(e,n)?e[n]:void 0;if(0!==a)if(a)r.push(a[2]);else{var i=new Promise((t,r)=>a=e[n]=[t,r]);r.push(a[2]=i);var o=t.p+t.u(n),l=new Error;t.l(o,r=>{if(t.o(e,n)&&(0!==(a=e[n])&&(e[n]=void 0),a)){var i=r&&("load"===r.type?"missing":r.type),o=r&&r.target&&r.target.src;l.message="Loading chunk "+n+" failed.\n("+i+": "+o+")",l.name="ChunkLoadError",l.type=i,l.request=o,a[1](l)}},"chunk-"+n,n)}};var n=(n,r)=>{var a,i,o=r[0],l=r[1],s=r[2],u=0;if(o.some(n=>0!==e[n])){for(a in l)t.o(l,a)&&(t.m[a]=l[a]);if(s)s(t)}for(n&&n(r);u<o.length;u++)i=o[u],t.o(e,i)&&e[i]&&e[i][0](),e[i]=0},r=self.webpackChunktraining_frontend=self.webpackChunktraining_frontend||[];r.forEach(n.bind(null,0)),r.push=n.bind(null,r.push.bind(r))})(),t.nc=void 0,(()=>{"use strict";var e=t(43),n=t(391);function r(e,n){return n||(n=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(n)}}))}var a=function(){return a=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++)for(var a in n=arguments[t])Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a]);return e},a.apply(this,arguments)};Object.create;function i(e,n,t){if(t||2===arguments.length)for(var r,a=0,i=n.length;a<i;a++)!r&&a in n||(r||(r=Array.prototype.slice.call(n,0,a)),r[a]=n[a]);return e.concat(r||Array.prototype.slice.call(n))}Object.create;"function"===typeof SuppressedError&&SuppressedError;var o=t(324),l=t.n(o),s="-ms-",u="-moz-",c="-webkit-",d="comm",f="rule",p="decl",h="@keyframes",m=Math.abs,g=String.fromCharCode,v=Object.assign;function y(e){return e.trim()}function b(e,n){return(e=n.exec(e))?e[0]:e}function w(e,n,t){return e.replace(n,t)}function x(e,n,t){return e.indexOf(n,t)}function k(e,n){return 0|e.charCodeAt(n)}function S(e,n,t){return e.slice(n,t)}function E(e){return e.length}function C(e){return e.length}function _(e,n){return n.push(e),e}function z(e,n){return e.filter(function(e){return!b(e,n)})}var P=1,j=1,T=0,O=0,N=0,A="";function R(e,n,t,r,a,i,o,l){return{value:e,root:n,parent:t,type:r,props:a,children:i,line:P,column:j,length:o,return:"",siblings:l}}function L(e,n){return v(R("",null,null,"",null,null,0,e.siblings),e,{length:-e.length},n)}function M(e){for(;e.root;)e=L(e.root,{children:[e]});_(e,e.siblings)}function D(){return N=O>0?k(A,--O):0,j--,10===N&&(j=1,P--),N}function I(){return N=O<T?k(A,O++):0,j++,10===N&&(j=1,P++),N}function F(){return k(A,O)}function U(){return O}function H(e,n){return S(A,e,n)}function W(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function $(e){return P=j=1,T=E(A=e),O=0,[]}function B(e){return A="",e}function V(e){return y(H(O-1,Q(91===e?e+2:40===e?e+1:e)))}function q(e){for(;(N=F())&&N<33;)I();return W(e)>2||W(N)>3?"":" "}function K(e,n){for(;--n&&I()&&!(N<48||N>102||N>57&&N<65||N>70&&N<97););return H(e,U()+(n<6&&32==F()&&32==I()))}function Q(e){for(;I();)switch(N){case e:return O;case 34:case 39:34!==e&&39!==e&&Q(N);break;case 40:41===e&&Q(e);break;case 92:I()}return O}function Y(e,n){for(;I()&&e+N!==57&&(e+N!==84||47!==F()););return"/*"+H(n,O-1)+"*"+g(47===e?e:I())}function G(e){for(;!W(F());)I();return H(e,O)}function X(e,n){for(var t="",r=0;r<e.length;r++)t+=n(e[r],r,e,n)||"";return t}function Z(e,n,t,r){switch(e.type){case"@layer":if(e.children.length)break;case"@import":case p:return e.return=e.return||e.value;case d:return"";case h:return e.return=e.value+"{"+X(e.children,r)+"}";case f:if(!E(e.value=e.props.join(",")))return""}return E(t=X(e.children,r))?e.return=e.value+"{"+t+"}":""}function J(e,n,t){switch(function(e,n){return 45^k(e,0)?(((n<<2^k(e,0))<<2^k(e,1))<<2^k(e,2))<<2^k(e,3):0}(e,n)){case 5103:return c+"print-"+e+e;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return c+e+e;case 4789:return u+e+e;case 5349:case 4246:case 4810:case 6968:case 2756:return c+e+u+e+s+e+e;case 5936:switch(k(e,n+11)){case 114:return c+e+s+w(e,/[svh]\w+-[tblr]{2}/,"tb")+e;case 108:return c+e+s+w(e,/[svh]\w+-[tblr]{2}/,"tb-rl")+e;case 45:return c+e+s+w(e,/[svh]\w+-[tblr]{2}/,"lr")+e}case 6828:case 4268:case 2903:return c+e+s+e+e;case 6165:return c+e+s+"flex-"+e+e;case 5187:return c+e+w(e,/(\w+).+(:[^]+)/,c+"box-$1$2"+s+"flex-$1$2")+e;case 5443:return c+e+s+"flex-item-"+w(e,/flex-|-self/g,"")+(b(e,/flex-|baseline/)?"":s+"grid-row-"+w(e,/flex-|-self/g,""))+e;case 4675:return c+e+s+"flex-line-pack"+w(e,/align-content|flex-|-self/g,"")+e;case 5548:return c+e+s+w(e,"shrink","negative")+e;case 5292:return c+e+s+w(e,"basis","preferred-size")+e;case 6060:return c+"box-"+w(e,"-grow","")+c+e+s+w(e,"grow","positive")+e;case 4554:return c+w(e,/([^-])(transform)/g,"$1"+c+"$2")+e;case 6187:return w(w(w(e,/(zoom-|grab)/,c+"$1"),/(image-set)/,c+"$1"),e,"")+e;case 5495:case 3959:return w(e,/(image-set\([^]*)/,c+"$1$`$1");case 4968:return w(w(e,/(.+:)(flex-)?(.*)/,c+"box-pack:$3"+s+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+c+e+e;case 4200:if(!b(e,/flex-|baseline/))return s+"grid-column-align"+S(e,n)+e;break;case 2592:case 3360:return s+w(e,"template-","")+e;case 4384:case 3616:return t&&t.some(function(e,t){return n=t,b(e.props,/grid-\w+-end/)})?~x(e+(t=t[n].value),"span",0)?e:s+w(e,"-start","")+e+s+"grid-row-span:"+(~x(t,"span",0)?b(t,/\d+/):+b(t,/\d+/)-+b(e,/\d+/))+";":s+w(e,"-start","")+e;case 4896:case 4128:return t&&t.some(function(e){return b(e.props,/grid-\w+-start/)})?e:s+w(w(e,"-end","-span"),"span ","")+e;case 4095:case 3583:case 4068:case 2532:return w(e,/(.+)-inline(.+)/,c+"$1$2")+e;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(E(e)-1-n>6)switch(k(e,n+1)){case 109:if(45!==k(e,n+4))break;case 102:return w(e,/(.+:)(.+)-([^]+)/,"$1"+c+"$2-$3$1"+u+(108==k(e,n+3)?"$3":"$2-$3"))+e;case 115:return~x(e,"stretch",0)?J(w(e,"stretch","fill-available"),n,t)+e:e}break;case 5152:case 5920:return w(e,/(.+?):(\d+)(\s*\/\s*(span)?\s*(\d+))?(.*)/,function(n,t,r,a,i,o,l){return s+t+":"+r+l+(a?s+t+"-span:"+(i?o:+o-+r)+l:"")+e});case 4949:if(121===k(e,n+6))return w(e,":",":"+c)+e;break;case 6444:switch(k(e,45===k(e,14)?18:11)){case 120:return w(e,/(.+:)([^;\s!]+)(;|(\s+)?!.+)?/,"$1"+c+(45===k(e,14)?"inline-":"")+"box$3$1"+c+"$2$3$1"+s+"$2box$3")+e;case 100:return w(e,":",":"+s)+e}break;case 5719:case 2647:case 2135:case 3927:case 2391:return w(e,"scroll-","scroll-snap-")+e}return e}function ee(e,n,t,r){if(e.length>-1&&!e.return)switch(e.type){case p:return void(e.return=J(e.value,e.length,t));case h:return X([L(e,{value:w(e.value,"@","@"+c)})],r);case f:if(e.length)return function(e,n){return e.map(n).join("")}(t=e.props,function(n){switch(b(n,r=/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":M(L(e,{props:[w(n,/:(read-\w+)/,":-moz-$1")]})),M(L(e,{props:[n]})),v(e,{props:z(t,r)});break;case"::placeholder":M(L(e,{props:[w(n,/:(plac\w+)/,":"+c+"input-$1")]})),M(L(e,{props:[w(n,/:(plac\w+)/,":-moz-$1")]})),M(L(e,{props:[w(n,/:(plac\w+)/,s+"input-$1")]})),M(L(e,{props:[n]})),v(e,{props:z(t,r)})}return""})}}function ne(e){return B(te("",null,null,null,[""],e=$(e),0,[0],e))}function te(e,n,t,r,a,i,o,l,s){for(var u=0,c=0,d=o,f=0,p=0,h=0,v=1,y=1,b=1,S=0,C="",z=a,P=i,j=r,T=C;y;)switch(h=S,S=I()){case 40:if(108!=h&&58==k(T,d-1)){-1!=x(T+=w(V(S),"&","&\f"),"&\f",m(u?l[u-1]:0))&&(b=-1);break}case 34:case 39:case 91:T+=V(S);break;case 9:case 10:case 13:case 32:T+=q(h);break;case 92:T+=K(U()-1,7);continue;case 47:switch(F()){case 42:case 47:_(ae(Y(I(),U()),n,t,s),s);break;default:T+="/"}break;case 123*v:l[u++]=E(T)*b;case 125*v:case 59:case 0:switch(S){case 0:case 125:y=0;case 59+c:-1==b&&(T=w(T,/\f/g,"")),p>0&&E(T)-d&&_(p>32?ie(T+";",r,t,d-1,s):ie(w(T," ","")+";",r,t,d-2,s),s);break;case 59:T+=";";default:if(_(j=re(T,n,t,u,c,a,l,C,z=[],P=[],d,i),i),123===S)if(0===c)te(T,n,j,j,z,i,d,l,P);else switch(99===f&&110===k(T,3)?100:f){case 100:case 108:case 109:case 115:te(e,j,j,r&&_(re(e,j,j,0,0,a,l,C,a,z=[],d,P),P),a,P,d,l,r?z:P);break;default:te(T,j,j,j,[""],P,0,l,P)}}u=c=p=0,v=b=1,C=T="",d=o;break;case 58:d=1+E(T),p=h;default:if(v<1)if(123==S)--v;else if(125==S&&0==v++&&125==D())continue;switch(T+=g(S),S*v){case 38:b=c>0?1:(T+="\f",-1);break;case 44:l[u++]=(E(T)-1)*b,b=1;break;case 64:45===F()&&(T+=V(I())),f=F(),c=d=E(C=T+=G(U())),S++;break;case 45:45===h&&2==E(T)&&(v=0)}}return i}function re(e,n,t,r,a,i,o,l,s,u,c,d){for(var p=a-1,h=0===a?i:[""],g=C(h),v=0,b=0,x=0;v<r;++v)for(var k=0,E=S(e,p+1,p=m(b=o[v])),_=e;k<g;++k)(_=y(b>0?h[k]+" "+E:w(E,/&\f/g,h[k])))&&(s[x++]=_);return R(e,n,t,0===a?f:l,s,u,c,d)}function ae(e,n,t,r){return R(e,n,t,d,g(N),S(e,2,-2),0,r)}function ie(e,n,t,r,a){return R(e,n,t,p,S(e,0,r),S(e,r+1,-1),r,a)}var oe={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},le="undefined"!=typeof process&&void 0!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}&&({NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.REACT_APP_SC_ATTR||{NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.SC_ATTR)||"data-styled",se="active",ue="data-styled-version",ce="6.1.19",de="/*!sc*/\n",fe="undefined"!=typeof window&&"undefined"!=typeof document,pe=Boolean("boolean"==typeof SC_DISABLE_SPEEDY?SC_DISABLE_SPEEDY:"undefined"!=typeof process&&void 0!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}&&void 0!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.REACT_APP_SC_DISABLE_SPEEDY&&""!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.REACT_APP_SC_DISABLE_SPEEDY?"false"!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.REACT_APP_SC_DISABLE_SPEEDY&&{NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.REACT_APP_SC_DISABLE_SPEEDY:"undefined"!=typeof process&&void 0!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}&&void 0!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.SC_DISABLE_SPEEDY&&""!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.SC_DISABLE_SPEEDY&&("false"!=={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.SC_DISABLE_SPEEDY&&{NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.SC_DISABLE_SPEEDY)),he=(new Set,Object.freeze([])),me=Object.freeze({});function ge(e,n,t){return void 0===t&&(t=me),e.theme!==t.theme&&e.theme||n||t.theme}var ve=new Set(["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","tr","track","u","ul","use","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","marker","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"]),ye=/[!"#$%&'()*+,./:;<=>?@[\\\]^`{|}~-]+/g,be=/(^-|-$)/g;function we(e){return e.replace(ye,"-").replace(be,"")}var xe=/(a)(d)/gi,ke=function(e){return String.fromCharCode(e+(e>25?39:97))};function Se(e){var n,t="";for(n=Math.abs(e);n>52;n=n/52|0)t=ke(n%52)+t;return(ke(n%52)+t).replace(xe,"$1-$2")}var Ee,Ce=function(e,n){for(var t=n.length;t;)e=33*e^n.charCodeAt(--t);return e},_e=function(e){return Ce(5381,e)};function ze(e){return Se(_e(e)>>>0)}function Pe(e){return e.displayName||e.name||"Component"}function je(e){return"string"==typeof e&&!0}var Te="function"==typeof Symbol&&Symbol.for,Oe=Te?Symbol.for("react.memo"):60115,Ne=Te?Symbol.for("react.forward_ref"):60112,Ae={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},Re={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},Le={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},Me=((Ee={})[Ne]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},Ee[Oe]=Le,Ee);function De(e){return("type"in(n=e)&&n.type.$$typeof)===Oe?Le:"$$typeof"in e?Me[e.$$typeof]:Ae;var n}var Ie=Object.defineProperty,Fe=Object.getOwnPropertyNames,Ue=Object.getOwnPropertySymbols,He=Object.getOwnPropertyDescriptor,We=Object.getPrototypeOf,$e=Object.prototype;function Be(e,n,t){if("string"!=typeof n){if($e){var r=We(n);r&&r!==$e&&Be(e,r,t)}var a=Fe(n);Ue&&(a=a.concat(Ue(n)));for(var i=De(e),o=De(n),l=0;l<a.length;++l){var s=a[l];if(!(s in Re||t&&t[s]||o&&s in o||i&&s in i)){var u=He(n,s);try{Ie(e,s,u)}catch(e){}}}}return e}function Ve(e){return"function"==typeof e}function qe(e){return"object"==typeof e&&"styledComponentId"in e}function Ke(e,n){return e&&n?"".concat(e," ").concat(n):e||n||""}function Qe(e,n){if(0===e.length)return"";for(var t=e[0],r=1;r<e.length;r++)t+=n?n+e[r]:e[r];return t}function Ye(e){return null!==e&&"object"==typeof e&&e.constructor.name===Object.name&&!("props"in e&&e.$$typeof)}function Ge(e,n,t){if(void 0===t&&(t=!1),!t&&!Ye(e)&&!Array.isArray(e))return n;if(Array.isArray(n))for(var r=0;r<n.length;r++)e[r]=Ge(e[r],n[r]);else if(Ye(n))for(var r in n)e[r]=Ge(e[r],n[r]);return e}function Xe(e,n){Object.defineProperty(e,"toString",{value:n})}function Ze(e){for(var n=[],t=1;t<arguments.length;t++)n[t-1]=arguments[t];return new Error("An error occurred. See https://github.com/styled-components/styled-components/blob/main/packages/styled-components/src/utils/errors.md#".concat(e," for more information.").concat(n.length>0?" Args: ".concat(n.join(", ")):""))}var Je=function(){function e(e){this.groupSizes=new Uint32Array(512),this.length=512,this.tag=e}return e.prototype.indexOfGroup=function(e){for(var n=0,t=0;t<e;t++)n+=this.groupSizes[t];return n},e.prototype.insertRules=function(e,n){if(e>=this.groupSizes.length){for(var t=this.groupSizes,r=t.length,a=r;e>=a;)if((a<<=1)<0)throw Ze(16,"".concat(e));this.groupSizes=new Uint32Array(a),this.groupSizes.set(t),this.length=a;for(var i=r;i<a;i++)this.groupSizes[i]=0}for(var o=this.indexOfGroup(e+1),l=(i=0,n.length);i<l;i++)this.tag.insertRule(o,n[i])&&(this.groupSizes[e]++,o++)},e.prototype.clearGroup=function(e){if(e<this.length){var n=this.groupSizes[e],t=this.indexOfGroup(e),r=t+n;this.groupSizes[e]=0;for(var a=t;a<r;a++)this.tag.deleteRule(t)}},e.prototype.getGroup=function(e){var n="";if(e>=this.length||0===this.groupSizes[e])return n;for(var t=this.groupSizes[e],r=this.indexOfGroup(e),a=r+t,i=r;i<a;i++)n+="".concat(this.tag.getRule(i)).concat(de);return n},e}(),en=new Map,nn=new Map,tn=1,rn=function(e){if(en.has(e))return en.get(e);for(;nn.has(tn);)tn++;var n=tn++;return en.set(e,n),nn.set(n,e),n},an=function(e,n){tn=n+1,en.set(e,n),nn.set(n,e)},on="style[".concat(le,"][").concat(ue,'="').concat(ce,'"]'),ln=new RegExp("^".concat(le,'\\.g(\\d+)\\[id="([\\w\\d-]+)"\\].*?"([^"]*)')),sn=function(e,n,t){for(var r,a=t.split(","),i=0,o=a.length;i<o;i++)(r=a[i])&&e.registerName(n,r)},un=function(e,n){for(var t,r=(null!==(t=n.textContent)&&void 0!==t?t:"").split(de),a=[],i=0,o=r.length;i<o;i++){var l=r[i].trim();if(l){var s=l.match(ln);if(s){var u=0|parseInt(s[1],10),c=s[2];0!==u&&(an(c,u),sn(e,c,s[3]),e.getTag().insertRules(u,a)),a.length=0}else a.push(l)}}},cn=function(e){for(var n=document.querySelectorAll(on),t=0,r=n.length;t<r;t++){var a=n[t];a&&a.getAttribute(le)!==se&&(un(e,a),a.parentNode&&a.parentNode.removeChild(a))}};function dn(){return t.nc}var fn=function(e){var n=document.head,t=e||n,r=document.createElement("style"),a=function(e){var n=Array.from(e.querySelectorAll("style[".concat(le,"]")));return n[n.length-1]}(t),i=void 0!==a?a.nextSibling:null;r.setAttribute(le,se),r.setAttribute(ue,ce);var o=dn();return o&&r.setAttribute("nonce",o),t.insertBefore(r,i),r},pn=function(){function e(e){this.element=fn(e),this.element.appendChild(document.createTextNode("")),this.sheet=function(e){if(e.sheet)return e.sheet;for(var n=document.styleSheets,t=0,r=n.length;t<r;t++){var a=n[t];if(a.ownerNode===e)return a}throw Ze(17)}(this.element),this.length=0}return e.prototype.insertRule=function(e,n){try{return this.sheet.insertRule(n,e),this.length++,!0}catch(e){return!1}},e.prototype.deleteRule=function(e){this.sheet.deleteRule(e),this.length--},e.prototype.getRule=function(e){var n=this.sheet.cssRules[e];return n&&n.cssText?n.cssText:""},e}(),hn=function(){function e(e){this.element=fn(e),this.nodes=this.element.childNodes,this.length=0}return e.prototype.insertRule=function(e,n){if(e<=this.length&&e>=0){var t=document.createTextNode(n);return this.element.insertBefore(t,this.nodes[e]||null),this.length++,!0}return!1},e.prototype.deleteRule=function(e){this.element.removeChild(this.nodes[e]),this.length--},e.prototype.getRule=function(e){return e<this.length?this.nodes[e].textContent:""},e}(),mn=function(){function e(e){this.rules=[],this.length=0}return e.prototype.insertRule=function(e,n){return e<=this.length&&(this.rules.splice(e,0,n),this.length++,!0)},e.prototype.deleteRule=function(e){this.rules.splice(e,1),this.length--},e.prototype.getRule=function(e){return e<this.length?this.rules[e]:""},e}(),gn=fe,vn={isServer:!fe,useCSSOMInjection:!pe},yn=function(){function e(e,n,t){void 0===e&&(e=me),void 0===n&&(n={});var r=this;this.options=a(a({},vn),e),this.gs=n,this.names=new Map(t),this.server=!!e.isServer,!this.server&&fe&&gn&&(gn=!1,cn(this)),Xe(this,function(){return function(e){for(var n=e.getTag(),t=n.length,r="",a=function(t){var a=function(e){return nn.get(e)}(t);if(void 0===a)return"continue";var i=e.names.get(a),o=n.getGroup(t);if(void 0===i||!i.size||0===o.length)return"continue";var l="".concat(le,".g").concat(t,'[id="').concat(a,'"]'),s="";void 0!==i&&i.forEach(function(e){e.length>0&&(s+="".concat(e,","))}),r+="".concat(o).concat(l,'{content:"').concat(s,'"}').concat(de)},i=0;i<t;i++)a(i);return r}(r)})}return e.registerId=function(e){return rn(e)},e.prototype.rehydrate=function(){!this.server&&fe&&cn(this)},e.prototype.reconstructWithOptions=function(n,t){return void 0===t&&(t=!0),new e(a(a({},this.options),n),this.gs,t&&this.names||void 0)},e.prototype.allocateGSInstance=function(e){return this.gs[e]=(this.gs[e]||0)+1},e.prototype.getTag=function(){return this.tag||(this.tag=(e=function(e){var n=e.useCSSOMInjection,t=e.target;return e.isServer?new mn(t):n?new pn(t):new hn(t)}(this.options),new Je(e)));var e},e.prototype.hasNameForId=function(e,n){return this.names.has(e)&&this.names.get(e).has(n)},e.prototype.registerName=function(e,n){if(rn(e),this.names.has(e))this.names.get(e).add(n);else{var t=new Set;t.add(n),this.names.set(e,t)}},e.prototype.insertRules=function(e,n,t){this.registerName(e,n),this.getTag().insertRules(rn(e),t)},e.prototype.clearNames=function(e){this.names.has(e)&&this.names.get(e).clear()},e.prototype.clearRules=function(e){this.getTag().clearGroup(rn(e)),this.clearNames(e)},e.prototype.clearTag=function(){this.tag=void 0},e}(),bn=/&/g,wn=/^\s*\/\/.*$/gm;function xn(e,n){return e.map(function(e){return"rule"===e.type&&(e.value="".concat(n," ").concat(e.value),e.value=e.value.replaceAll(",",",".concat(n," ")),e.props=e.props.map(function(e){return"".concat(n," ").concat(e)})),Array.isArray(e.children)&&"@keyframes"!==e.type&&(e.children=xn(e.children,n)),e})}function kn(e){var n,t,r,a=void 0===e?me:e,i=a.options,o=void 0===i?me:i,l=a.plugins,s=void 0===l?he:l,u=function(e,r,a){return a.startsWith(t)&&a.endsWith(t)&&a.replaceAll(t,"").length>0?".".concat(n):e},c=s.slice();c.push(function(e){e.type===f&&e.value.includes("&")&&(e.props[0]=e.props[0].replace(bn,t).replace(r,u))}),o.prefix&&c.push(ee),c.push(Z);var d=function(e,a,i,l){void 0===a&&(a=""),void 0===i&&(i=""),void 0===l&&(l="&"),n=l,t=a,r=new RegExp("\\".concat(t,"\\b"),"g");var s=e.replace(wn,""),u=ne(i||a?"".concat(i," ").concat(a," { ").concat(s," }"):s);o.namespace&&(u=xn(u,o.namespace));var d,f=[];return X(u,function(e){var n=C(e);return function(t,r,a,i){for(var o="",l=0;l<n;l++)o+=e[l](t,r,a,i)||"";return o}}(c.concat((d=function(e){return f.push(e)},function(e){e.root||(e=e.return)&&d(e)})))),f};return d.hash=s.length?s.reduce(function(e,n){return n.name||Ze(15),Ce(e,n.name)},5381).toString():"",d}var Sn=new yn,En=kn(),Cn=e.createContext({shouldForwardProp:void 0,styleSheet:Sn,stylis:En}),_n=(Cn.Consumer,e.createContext(void 0));function zn(){return(0,e.useContext)(Cn)}function Pn(n){var t=(0,e.useState)(n.stylisPlugins),r=t[0],a=t[1],i=zn().styleSheet,o=(0,e.useMemo)(function(){var e=i;return n.sheet?e=n.sheet:n.target&&(e=e.reconstructWithOptions({target:n.target},!1)),n.disableCSSOMInjection&&(e=e.reconstructWithOptions({useCSSOMInjection:!1})),e},[n.disableCSSOMInjection,n.sheet,n.target,i]),s=(0,e.useMemo)(function(){return kn({options:{namespace:n.namespace,prefix:n.enableVendorPrefixes},plugins:r})},[n.enableVendorPrefixes,n.namespace,r]);(0,e.useEffect)(function(){l()(r,n.stylisPlugins)||a(n.stylisPlugins)},[n.stylisPlugins]);var u=(0,e.useMemo)(function(){return{shouldForwardProp:n.shouldForwardProp,styleSheet:o,stylis:s}},[n.shouldForwardProp,o,s]);return e.createElement(Cn.Provider,{value:u},e.createElement(_n.Provider,{value:s},n.children))}var jn=function(){function e(e,n){var t=this;this.inject=function(e,n){void 0===n&&(n=En);var r=t.name+n.hash;e.hasNameForId(t.id,r)||e.insertRules(t.id,r,n(t.rules,r,"@keyframes"))},this.name=e,this.id="sc-keyframes-".concat(e),this.rules=n,Xe(this,function(){throw Ze(12,String(t.name))})}return e.prototype.getName=function(e){return void 0===e&&(e=En),this.name+e.hash},e}(),Tn=function(e){return e>="A"&&e<="Z"};function On(e){for(var n="",t=0;t<e.length;t++){var r=e[t];if(1===t&&"-"===r&&"-"===e[0])return e;Tn(r)?n+="-"+r.toLowerCase():n+=r}return n.startsWith("ms-")?"-"+n:n}var Nn=function(e){return null==e||!1===e||""===e},An=function(e){var n,t,r=[];for(var a in e){var o=e[a];e.hasOwnProperty(a)&&!Nn(o)&&(Array.isArray(o)&&o.isCss||Ve(o)?r.push("".concat(On(a),":"),o,";"):Ye(o)?r.push.apply(r,i(i(["".concat(a," {")],An(o),!1),["}"],!1)):r.push("".concat(On(a),": ").concat((n=a,null==(t=o)||"boolean"==typeof t||""===t?"":"number"!=typeof t||0===t||n in oe||n.startsWith("--")?String(t).trim():"".concat(t,"px")),";")))}return r};function Rn(e,n,t,r){return Nn(e)?[]:qe(e)?[".".concat(e.styledComponentId)]:Ve(e)?!Ve(a=e)||a.prototype&&a.prototype.isReactComponent||!n?[e]:Rn(e(n),n,t,r):e instanceof jn?t?(e.inject(t,r),[e.getName(r)]):[e]:Ye(e)?An(e):Array.isArray(e)?Array.prototype.concat.apply(he,e.map(function(e){return Rn(e,n,t,r)})):[e.toString()];var a}function Ln(e){for(var n=0;n<e.length;n+=1){var t=e[n];if(Ve(t)&&!qe(t))return!1}return!0}var Mn=_e(ce),Dn=function(){function e(e,n,t){this.rules=e,this.staticRulesId="",this.isStatic=(void 0===t||t.isStatic)&&Ln(e),this.componentId=n,this.baseHash=Ce(Mn,n),this.baseStyle=t,yn.registerId(n)}return e.prototype.generateAndInjectStyles=function(e,n,t){var r=this.baseStyle?this.baseStyle.generateAndInjectStyles(e,n,t):"";if(this.isStatic&&!t.hash)if(this.staticRulesId&&n.hasNameForId(this.componentId,this.staticRulesId))r=Ke(r,this.staticRulesId);else{var a=Qe(Rn(this.rules,e,n,t)),i=Se(Ce(this.baseHash,a)>>>0);if(!n.hasNameForId(this.componentId,i)){var o=t(a,".".concat(i),void 0,this.componentId);n.insertRules(this.componentId,i,o)}r=Ke(r,i),this.staticRulesId=i}else{for(var l=Ce(this.baseHash,t.hash),s="",u=0;u<this.rules.length;u++){var c=this.rules[u];if("string"==typeof c)s+=c;else if(c){var d=Qe(Rn(c,e,n,t));l=Ce(l,d+u),s+=d}}if(s){var f=Se(l>>>0);n.hasNameForId(this.componentId,f)||n.insertRules(this.componentId,f,t(s,".".concat(f),void 0,this.componentId)),r=Ke(r,f)}}return r},e}(),In=e.createContext(void 0);In.Consumer;var Fn={};new Set;function Un(n,t,r){var i=qe(n),o=n,l=!je(n),s=t.attrs,u=void 0===s?he:s,c=t.componentId,d=void 0===c?function(e,n){var t="string"!=typeof e?"sc":we(e);Fn[t]=(Fn[t]||0)+1;var r="".concat(t,"-").concat(ze(ce+t+Fn[t]));return n?"".concat(n,"-").concat(r):r}(t.displayName,t.parentComponentId):c,f=t.displayName,p=void 0===f?function(e){return je(e)?"styled.".concat(e):"Styled(".concat(Pe(e),")")}(n):f,h=t.displayName&&t.componentId?"".concat(we(t.displayName),"-").concat(t.componentId):t.componentId||d,m=i&&o.attrs?o.attrs.concat(u).filter(Boolean):u,g=t.shouldForwardProp;if(i&&o.shouldForwardProp){var v=o.shouldForwardProp;if(t.shouldForwardProp){var y=t.shouldForwardProp;g=function(e,n){return v(e,n)&&y(e,n)}}else g=v}var b=new Dn(r,h,i?o.componentStyle:void 0);function w(n,t){return function(n,t,r){var i=n.attrs,o=n.componentStyle,l=n.defaultProps,s=n.foldedComponentIds,u=n.styledComponentId,c=n.target,d=e.useContext(In),f=zn(),p=n.shouldForwardProp||f.shouldForwardProp,h=ge(t,d,l)||me,m=function(e,n,t){for(var r,i=a(a({},n),{className:void 0,theme:t}),o=0;o<e.length;o+=1){var l=Ve(r=e[o])?r(i):r;for(var s in l)i[s]="className"===s?Ke(i[s],l[s]):"style"===s?a(a({},i[s]),l[s]):l[s]}return n.className&&(i.className=Ke(i.className,n.className)),i}(i,t,h),g=m.as||c,v={};for(var y in m)void 0===m[y]||"$"===y[0]||"as"===y||"theme"===y&&m.theme===h||("forwardedAs"===y?v.as=m.forwardedAs:p&&!p(y,g)||(v[y]=m[y]));var b=function(e,n){var t=zn();return e.generateAndInjectStyles(n,t.styleSheet,t.stylis)}(o,m),w=Ke(s,u);return b&&(w+=" "+b),m.className&&(w+=" "+m.className),v[je(g)&&!ve.has(g)?"class":"className"]=w,r&&(v.ref=r),(0,e.createElement)(g,v)}(x,n,t)}w.displayName=p;var x=e.forwardRef(w);return x.attrs=m,x.componentStyle=b,x.displayName=p,x.shouldForwardProp=g,x.foldedComponentIds=i?Ke(o.foldedComponentIds,o.styledComponentId):"",x.styledComponentId=h,x.target=i?o.target:n,Object.defineProperty(x,"defaultProps",{get:function(){return this._foldedDefaultProps},set:function(e){this._foldedDefaultProps=i?function(e){for(var n=[],t=1;t<arguments.length;t++)n[t-1]=arguments[t];for(var r=0,a=n;r<a.length;r++)Ge(e,a[r],!0);return e}({},o.defaultProps,e):e}}),Xe(x,function(){return".".concat(x.styledComponentId)}),l&&Be(x,n,{attrs:!0,componentStyle:!0,displayName:!0,foldedComponentIds:!0,shouldForwardProp:!0,styledComponentId:!0,target:!0}),x}function Hn(e,n){for(var t=[e[0]],r=0,a=n.length;r<a;r+=1)t.push(n[r],e[r+1]);return t}var Wn=function(e){return Object.assign(e,{isCss:!0})};function $n(e){for(var n=[],t=1;t<arguments.length;t++)n[t-1]=arguments[t];if(Ve(e)||Ye(e))return Wn(Rn(Hn(he,i([e],n,!0))));var r=e;return 0===n.length&&1===r.length&&"string"==typeof r[0]?Rn(r):Wn(Rn(Hn(r,n)))}function Bn(e,n,t){if(void 0===t&&(t=me),!n)throw Ze(1,n);var r=function(r){for(var a=[],o=1;o<arguments.length;o++)a[o-1]=arguments[o];return e(n,t,$n.apply(void 0,i([r],a,!1)))};return r.attrs=function(r){return Bn(e,n,a(a({},t),{attrs:Array.prototype.concat(t.attrs,r).filter(Boolean)}))},r.withConfig=function(r){return Bn(e,n,a(a({},t),r))},r}var Vn=function(e){return Bn(Un,e)},qn=Vn;ve.forEach(function(e){qn[e]=Vn(e)});!function(){function e(e,n){this.rules=e,this.componentId=n,this.isStatic=Ln(e),yn.registerId(this.componentId+1)}e.prototype.createStyles=function(e,n,t,r){var a=r(Qe(Rn(this.rules,n,t,r)),""),i=this.componentId+e;t.insertRules(i,i,a)},e.prototype.removeStyles=function(e,n){n.clearRules(this.componentId+e)},e.prototype.renderStyles=function(e,n,t,r){e>2&&yn.registerId(this.componentId+e),this.removeStyles(e,t),this.createStyles(e,n,t,r)}}();(function(){function n(){var n=this;this._emitSheetCSS=function(){var e=n.instance.toString();if(!e)return"";var t=dn(),r=Qe([t&&'nonce="'.concat(t,'"'),"".concat(le,'="true"'),"".concat(ue,'="').concat(ce,'"')].filter(Boolean)," ");return"<style ".concat(r,">").concat(e,"</style>")},this.getStyleTags=function(){if(n.sealed)throw Ze(2);return n._emitSheetCSS()},this.getStyleElement=function(){var t;if(n.sealed)throw Ze(2);var r=n.instance.toString();if(!r)return[];var i=((t={})[le]="",t[ue]=ce,t.dangerouslySetInnerHTML={__html:r},t),o=dn();return o&&(i.nonce=o),[e.createElement("style",a({},i,{key:"sc-0-0"}))]},this.seal=function(){n.sealed=!0},this.instance=new yn({isServer:!0}),this.sealed=!1}n.prototype.collectStyles=function(n){if(this.sealed)throw Ze(2);return e.createElement(Pn,{sheet:this.instance},n)},n.prototype.interleaveWithNodeStream=function(e){throw Ze(3)}})(),"__sc-".concat(le,"__");function Kn(e){return Kn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Kn(e)}function Qn(e){var n=function(e,n){if("object"!=Kn(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,n||"default");if("object"!=Kn(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(e)}(e,"string");return"symbol"==Kn(n)?n:n+""}function Yn(e,n,t){return(n=Qn(n))in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function Gn(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable})),t.push.apply(t,r)}return t}function Xn(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?Gn(Object(t),!0).forEach(function(n){Yn(e,n,t[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):Gn(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})}return e}function Zn(e,n){if(null==e)return{};var t,r,a=function(e,n){if(null==e)return{};var t={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(-1!==n.indexOf(r))continue;t[r]=e[r]}return t}(e,n);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)t=i[r],-1===n.indexOf(t)&&{}.propertyIsEnumerable.call(e,t)&&(a[t]=e[t])}return a}const Jn=e=>{const n=(e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,n,t)=>t?t.toUpperCase():n.toLowerCase()))(e);return n.charAt(0).toUpperCase()+n.slice(1)},et=function(){for(var e=arguments.length,n=new Array(e),t=0;t<e;t++)n[t]=arguments[t];return n.filter((e,n,t)=>Boolean(e)&&""!==e.trim()&&t.indexOf(e)===n).join(" ").trim()};var nt={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};const tt=["color","size","strokeWidth","absoluteStrokeWidth","className","children","iconNode"],rt=(0,e.forwardRef)((n,t)=>{let{color:r="currentColor",size:a=24,strokeWidth:i=2,absoluteStrokeWidth:o,className:l="",children:s,iconNode:u}=n,c=Zn(n,tt);return(0,e.createElement)("svg",Xn(Xn(Xn({ref:t},nt),{},{width:a,height:a,stroke:r,strokeWidth:o?24*Number(i)/Number(a):i,className:et("lucide",l)},!s&&!(e=>{for(const n in e)if(n.startsWith("aria-")||"role"===n||"title"===n)return!0})(c)&&{"aria-hidden":"true"}),c),[...u.map(n=>{let[t,r]=n;return(0,e.createElement)(t,r)}),...Array.isArray(s)?s:[s]])}),at=["className"],it=(n,t)=>{const r=(0,e.forwardRef)((r,a)=>{let{className:i}=r,o=Zn(r,at);return(0,e.createElement)(rt,Xn({ref:a,iconNode:t,className:et("lucide-".concat((l=Jn(n),l.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase())),"lucide-".concat(n),i)},o));var l});return r.displayName=Jn(n),r},ot=it("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]]),lt=it("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]),st=it("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]]),ut=it("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]]),ct=it("camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]),dt=it("cpu",[["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M17 20v2",key:"1rnc9c"}],["path",{d:"M17 2v2",key:"11trls"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M2 17h2",key:"7oei6x"}],["path",{d:"M2 7h2",key:"asdhe0"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"M20 17h2",key:"1fpfkl"}],["path",{d:"M20 7h2",key:"1o8tra"}],["path",{d:"M7 20v2",key:"4gnj0m"}],["path",{d:"M7 2v2",key:"1i4yhu"}],["rect",{x:"4",y:"4",width:"16",height:"16",rx:"2",key:"1vbyd7"}],["rect",{x:"8",y:"8",width:"8",height:"8",rx:"1",key:"z9xiuo"}]]),ft=it("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]),pt=it("smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]]),ht=it("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]),mt=it("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]);var gt,vt,yt,bt,wt,xt,kt,St,Et,Ct,_t,zt,Pt,jt,Tt,Ot,Nt,At,Rt,Lt,Mt,Dt,It,Ft,Ut,Ht,Wt,$t,Bt,Vt=t(579);const qt=qn.div(gt||(gt=r(["\n  min-height: 100vh;\n  background: var(--bg-primary);\n  position: relative;\n  overflow: hidden;\n\n  &::before {\n    content: '';\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background:\n      radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),\n      radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.03) 0%, transparent 50%),\n      radial-gradient(circle at 40% 60%, rgba(16, 185, 129, 0.02) 0%, transparent 50%);\n    pointer-events: none;\n    z-index: 0;\n  }\n"]))),Kt=qn.nav(vt||(vt=r(["\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 50;\n  background: var(--bg-glass);\n  backdrop-filter: blur(20px);\n  border-bottom: 1px solid var(--border-neural);\n  padding: var(--space-4) 0;\n  transition: var(--transition-normal);\n\n  @media (max-width: 768px) {\n    padding: var(--space-3) 0;\n  }\n"]))),Qt=qn.div(yt||(yt=r(["\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 0 var(--space-6);\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n\n  @media (max-width: 768px) {\n    padding: 0 var(--space-4);\n  }\n"]))),Yt=qn.div(bt||(bt=r(["\n  font-family: var(--font-primary);\n  font-size: 1.5rem;\n  font-weight: 700;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n\n  @media (max-width: 768px) {\n    font-size: 1.25rem;\n    gap: var(--space-2);\n  }\n"]))),Gt=qn.div(wt||(wt=r(["\n  width: 40px;\n  height: 40px;\n  background: var(--bg-neural);\n  border-radius: var(--radius-lg);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  box-shadow: var(--shadow-neural);\n\n  @media (max-width: 768px) {\n    width: 36px;\n    height: 36px;\n  }\n"]))),Xt=qn.div(xt||(xt=r(["\n  display: flex;\n  align-items: center;\n  gap: var(--space-8);\n\n  @media (max-width: 768px) {\n    gap: var(--space-4);\n  }\n"]))),Zt=qn.a(kt||(kt=r(["\n  color: var(--text-secondary);\n  text-decoration: none;\n  font-weight: 500;\n  font-size: 0.9rem;\n  transition: var(--transition-fast);\n  position: relative;\n\n  &:hover {\n    color: var(--text-accent);\n  }\n\n  &::after {\n    content: '';\n    position: absolute;\n    bottom: -4px;\n    left: 0;\n    width: 0;\n    height: 2px;\n    background: var(--bg-neural);\n    transition: var(--transition-normal);\n  }\n\n  &:hover::after {\n    width: 100%;\n  }\n\n  @media (max-width: 768px) {\n    font-size: 0.85rem;\n  }\n"]))),Jt=qn.section(St||(St=r(["\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: var(--space-24) var(--space-6) var(--space-20);\n  text-align: center;\n  position: relative;\n  z-index: 1;\n\n  @media (max-width: 768px) {\n    padding: var(--space-20) var(--space-4) var(--space-16);\n    min-height: calc(100vh - 80px);\n  }\n"]))),er=qn.div(Et||(Et=r(["\n  max-width: 900px;\n  width: 100%;\n  position: relative;\n  z-index: 2;\n"]))),nr=qn.div(Ct||(Ct=r(["\n  display: inline-flex;\n  align-items: center;\n  gap: var(--space-2);\n  background: var(--bg-glass);\n  border: 1px solid var(--border-neural);\n  border-radius: var(--radius-full);\n  padding: var(--space-2) var(--space-4);\n  margin-bottom: var(--space-6);\n  font-size: 0.875rem;\n  font-weight: 500;\n  color: var(--text-accent);\n  backdrop-filter: blur(10px);\n  box-shadow: var(--shadow-glow);\n\n  @media (max-width: 768px) {\n    font-size: 0.8rem;\n    padding: var(--space-2) var(--space-3);\n  }\n"]))),tr=qn.h1(_t||(_t=r(["\n  font-family: var(--font-primary);\n  font-size: 3.5rem;\n  font-weight: 800;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  margin-bottom: var(--space-6);\n  line-height: 1.1;\n  letter-spacing: -0.02em;\n\n  @media (max-width: 768px) {\n    font-size: 2.5rem;\n    margin-bottom: var(--space-4);\n  }\n\n  @media (max-width: 480px) {\n    font-size: 2rem;\n  }\n"]))),rr=qn.p(zt||(zt=r(["\n  font-size: 1.25rem;\n  font-weight: 400;\n  color: var(--text-secondary);\n  margin-bottom: var(--space-8);\n  line-height: 1.6;\n  max-width: 700px;\n  margin-left: auto;\n  margin-right: auto;\n\n  @media (max-width: 768px) {\n    font-size: 1.125rem;\n    margin-bottom: var(--space-6);\n  }\n\n  @media (max-width: 480px) {\n    font-size: 1rem;\n  }\n"]))),ar=qn.p(Pt||(Pt=r(["\n  font-size: 1rem;\n  line-height: 1.7;\n  color: var(--text-tertiary);\n  margin-bottom: var(--space-10);\n  max-width: 600px;\n  margin-left: auto;\n  margin-right: auto;\n\n  @media (max-width: 768px) {\n    font-size: 0.95rem;\n    margin-bottom: var(--space-8);\n    line-height: 1.6;\n  }\n"]))),ir=qn.div(jt||(jt=r(["\n  display: flex;\n  gap: var(--space-5);\n  justify-content: center;\n  align-items: center;\n  flex-wrap: wrap;\n  margin-bottom: var(--space-16);\n\n  @media (max-width: 768px) {\n    gap: var(--space-4);\n    margin-bottom: var(--space-12);\n    flex-direction: column;\n  }\n"]))),or=qn.button(Tt||(Tt=r(["\n  background: var(--bg-neural);\n  color: white;\n  border: none;\n  padding: var(--space-4) var(--space-10);\n  border-radius: var(--radius-xl);\n  font-size: 1.125rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: var(--transition-normal);\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n  box-shadow: var(--shadow-neural);\n  position: relative;\n  overflow: hidden;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: -100%;\n    width: 100%;\n    height: 100%;\n    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n    transition: var(--transition-slow);\n  }\n\n  &:hover {\n    transform: translateY(-3px);\n    box-shadow: var(--shadow-xl), var(--shadow-glow);\n\n    &::before {\n      left: 100%;\n    }\n  }\n\n  &:active {\n    transform: translateY(-1px);\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-4) var(--space-8);\n    font-size: 1rem;\n    width: 100%;\n    max-width: 300px;\n  }\n"]))),lr=qn.button(Ot||(Ot=r(["\n  background: var(--bg-glass);\n  color: var(--text-primary);\n  border: 2px solid var(--border-neural);\n  padding: var(--space-4) var(--space-8);\n  border-radius: var(--radius-xl);\n  font-size: 1.125rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: var(--transition-normal);\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n  backdrop-filter: blur(10px);\n\n  &:hover {\n    border-color: var(--primary-600);\n    color: var(--primary-600);\n    background: var(--primary-50);\n    transform: translateY(-2px);\n    box-shadow: var(--shadow-lg);\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-4) var(--space-6);\n    font-size: 1rem;\n    width: 100%;\n    max-width: 300px;\n  }\n"]))),sr=qn.section(Nt||(Nt=r(["\n  padding: var(--space-24) var(--space-6);\n  background: var(--bg-tertiary);\n  position: relative;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background:\n      radial-gradient(circle at 30% 30%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),\n      radial-gradient(circle at 70% 70%, rgba(147, 51, 234, 0.05) 0%, transparent 50%);\n    pointer-events: none;\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-20) var(--space-4);\n  }\n"]))),ur=qn.div(At||(At=r(["\n  max-width: 1400px;\n  margin: 0 auto;\n  text-align: center;\n  position: relative;\n  z-index: 1;\n"]))),cr=qn.h2(Rt||(Rt=r(["\n  font-family: var(--font-primary);\n  font-size: 2.75rem;\n  font-weight: 700;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  margin-bottom: var(--space-6);\n  letter-spacing: -0.02em;\n\n  @media (max-width: 768px) {\n    font-size: 2rem;\n  }\n"]))),dr=qn.p(Lt||(Lt=r(["\n  font-size: 1.125rem;\n  color: var(--text-secondary);\n  margin-bottom: var(--space-16);\n  max-width: 700px;\n  margin-left: auto;\n  margin-right: auto;\n  line-height: 1.6;\n\n  @media (max-width: 768px) {\n    font-size: 1rem;\n    margin-bottom: var(--space-12);\n  }\n"]))),fr=qn.div(Mt||(Mt=r(["\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));\n  gap: var(--space-8);\n  margin-bottom: var(--space-20);\n\n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n    gap: var(--space-6);\n  }\n"]))),pr=qn.div(Dt||(Dt=r(["\n  background: var(--bg-glass);\n  border-radius: var(--radius-2xl);\n  padding: var(--space-10);\n  border: 1px solid var(--border-neural);\n  transition: var(--transition-normal);\n  text-align: left;\n  position: relative;\n  overflow: hidden;\n  backdrop-filter: blur(20px);\n  box-shadow: var(--shadow-lg);\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 4px;\n    background: var(--bg-neural);\n    transform: scaleX(0);\n    transition: var(--transition-normal);\n  }\n\n  &:hover {\n    transform: translateY(-6px);\n    box-shadow: var(--shadow-xl), var(--shadow-glow);\n    border-color: var(--primary-300);\n\n    &::before {\n      transform: scaleX(1);\n    }\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-8);\n    text-align: center;\n  }\n"]))),hr=qn.div(It||(It=r(["\n  width: 64px;\n  height: 64px;\n  background: var(--bg-neural);\n  border-radius: var(--radius-xl);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: var(--space-6);\n  box-shadow: var(--shadow-neural);\n\n  @media (max-width: 768px) {\n    margin: 0 auto var(--space-6);\n  }\n"]))),mr=qn.h3(Ft||(Ft=r(["\n  font-size: 1.5rem;\n  margin-bottom: var(--space-4);\n  color: var(--text-primary);\n  font-weight: 700;\n  font-family: var(--font-primary);\n\n  @media (max-width: 768px) {\n    text-align: center;\n  }\n"]))),gr=qn.p(Ut||(Ut=r(["\n  font-size: 1rem;\n  color: var(--text-secondary);\n  line-height: 1.7;\n  font-weight: 400;\n\n  @media (max-width: 768px) {\n    text-align: center;\n  }\n"]))),vr=qn.div(Ht||(Ht=r(["\n  display: flex;\n  justify-content: center;\n  gap: var(--space-8);\n  margin-top: var(--space-8);\n\n  @media (max-width: 768px) {\n    gap: var(--space-6);\n    flex-wrap: wrap;\n  }\n"]))),yr=qn.div(Wt||(Wt=r(["\n  text-align: center;\n"]))),br=qn.div($t||($t=r(["\n  font-size: 2rem;\n  font-weight: 700;\n  color: var(--primary-600);\n  font-family: var(--font-primary);\n\n  @media (max-width: 768px) {\n    font-size: 1.5rem;\n  }\n"]))),wr=qn.div(Bt||(Bt=r(["\n  font-size: 0.875rem;\n  color: var(--text-tertiary);\n  font-weight: 500;\n  margin-top: var(--space-1);\n"]))),xr=e=>{let{onStartTraining:n,onNavigateToAbout:t,onNavigateToContact:r}=e;return(0,Vt.jsxs)(qt,{children:[(0,Vt.jsx)(Kt,{children:(0,Vt.jsxs)(Qt,{children:[(0,Vt.jsxs)(Yt,{children:[(0,Vt.jsx)(Gt,{children:(0,Vt.jsx)(ot,{size:24})}),"ASL Neural"]}),(0,Vt.jsxs)(Xt,{children:[(0,Vt.jsx)(Zt,{href:"#features",children:"Features"}),(0,Vt.jsx)(Zt,{onClick:t,style:{cursor:"pointer"},children:"About"}),(0,Vt.jsx)(Zt,{onClick:r,style:{cursor:"pointer"},children:"Contact"})]})]})}),(0,Vt.jsx)(Jt,{children:(0,Vt.jsxs)(er,{children:[(0,Vt.jsxs)(nr,{children:[(0,Vt.jsx)(lt,{size:16}),"AI-Powered Learning Platform"]}),(0,Vt.jsx)(tr,{children:"Master Sign Language with Neural Intelligence"}),(0,Vt.jsx)(rr,{children:"Revolutionary AI platform that transforms sign language learning through real-time computer vision and adaptive neural networks"}),(0,Vt.jsx)(ar,{children:"Experience the future of accessibility education. Our advanced machine learning algorithms provide instant feedback while contributing to breakthrough AI research for the deaf and hard-of-hearing community."}),(0,Vt.jsxs)(ir,{children:[(0,Vt.jsxs)(or,{onClick:n,children:[(0,Vt.jsx)(st,{size:20}),"Start Neural Training"]}),(0,Vt.jsxs)(lr,{onClick:t,children:[(0,Vt.jsx)(ut,{size:20}),"Explore Technology"]})]}),(0,Vt.jsxs)(vr,{children:[(0,Vt.jsxs)(yr,{children:[(0,Vt.jsx)(br,{children:"25K+"}),(0,Vt.jsx)(wr,{children:"Neural Sessions"})]}),(0,Vt.jsxs)(yr,{children:[(0,Vt.jsx)(br,{children:"150+"}),(0,Vt.jsx)(wr,{children:"Sign Patterns"})]}),(0,Vt.jsxs)(yr,{children:[(0,Vt.jsx)(br,{children:"98.7%"}),(0,Vt.jsx)(wr,{children:"AI Accuracy"})]})]})]})}),(0,Vt.jsx)(sr,{id:"features",children:(0,Vt.jsxs)(ur,{children:[(0,Vt.jsx)(cr,{children:"Neural Network Capabilities"}),(0,Vt.jsx)(dr,{children:"Discover how our advanced AI technology revolutionizes sign language learning through cutting-edge computer vision and machine learning"}),(0,Vt.jsxs)(fr,{children:[(0,Vt.jsxs)(pr,{children:[(0,Vt.jsx)(hr,{children:(0,Vt.jsx)(ct,{size:28,color:"white"})}),(0,Vt.jsx)(mr,{children:"Real-time Computer Vision"}),(0,Vt.jsx)(gr,{children:"Advanced neural networks analyze your hand movements in real-time, providing instant feedback with 98.7% accuracy using state-of-the-art pose estimation algorithms"})]}),(0,Vt.jsxs)(pr,{children:[(0,Vt.jsx)(hr,{children:(0,Vt.jsx)(dt,{size:28,color:"white"})}),(0,Vt.jsx)(mr,{children:"Adaptive AI Learning"}),(0,Vt.jsx)(gr,{children:"Our deep learning models continuously adapt to your learning style, creating personalized training paths that optimize skill acquisition and retention rates"})]}),(0,Vt.jsxs)(pr,{children:[(0,Vt.jsx)(hr,{children:(0,Vt.jsx)(ft,{size:28,color:"white"})}),(0,Vt.jsx)(mr,{children:"Global Impact Network"}),(0,Vt.jsx)(gr,{children:"Join a worldwide community contributing to breakthrough AI research that advances accessibility technology for millions in the deaf and hard-of-hearing community"})]}),(0,Vt.jsxs)(pr,{children:[(0,Vt.jsx)(hr,{children:(0,Vt.jsx)(pt,{size:28,color:"white"})}),(0,Vt.jsx)(mr,{children:"Cross-Platform Intelligence"}),(0,Vt.jsx)(gr,{children:"Seamless AI-powered experience across all devices with cloud-synchronized progress and edge computing for lightning-fast response times"})]}),(0,Vt.jsxs)(pr,{children:[(0,Vt.jsx)(hr,{children:(0,Vt.jsx)(ht,{size:28,color:"white"})}),(0,Vt.jsx)(mr,{children:"Precision Learning Analytics"}),(0,Vt.jsx)(gr,{children:"Advanced analytics track micro-movements and learning patterns, providing data-driven insights to accelerate your mastery of sign language"})]}),(0,Vt.jsxs)(pr,{children:[(0,Vt.jsx)(hr,{children:(0,Vt.jsx)(mt,{size:28,color:"white"})}),(0,Vt.jsx)(mr,{children:"Privacy-First Architecture"}),(0,Vt.jsx)(gr,{children:"Enterprise-grade security with local processing ensures your data remains private while contributing anonymized insights to advance AI research"})]})]})]})})]})};var kr=t(29),Sr=t.n(kr);const Er=it("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]),Cr=it("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),_r=it("wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]]),zr=it("wifi-off",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}],["path",{d:"M5 12.859a10 10 0 0 1 5.17-2.69",key:"1dl1wf"}],["path",{d:"M19 12.859a10 10 0 0 0-2.007-1.523",key:"4k23kn"}],["path",{d:"M2 8.82a15 15 0 0 1 4.177-2.643",key:"1grhjp"}],["path",{d:"M22 8.82a15 15 0 0 0-11.288-3.764",key:"z3jwby"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]),Pr=it("square",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]]),jr=it("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]]);var Tr,Or,Nr,Ar,Rr,Lr,Mr,Dr,Ir,Fr,Ur,Hr,Wr,$r,Br,Vr,qr,Kr,Qr,Yr,Gr,Xr,Zr,Jr,ea,na,ta,ra,aa,ia,oa,la,sa,ua,ca,da,fa;const pa=qn.div(Tr||(Tr=r(["\n  min-height: 100vh;\n  background: var(--bg-primary);\n  position: relative;\n  overflow-x: hidden;\n\n  &::before {\n    content: '';\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background:\n      radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),\n      radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.03) 0%, transparent 50%);\n    pointer-events: none;\n    z-index: 0;\n  }\n"]))),ha=qn.nav(Or||(Or=r(["\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 50;\n  background: var(--bg-glass);\n  backdrop-filter: blur(20px);\n  border-bottom: 1px solid var(--border-neural);\n  padding: var(--space-4) 0;\n  transition: var(--transition-normal);\n"]))),ma=qn.div(Nr||(Nr=r(["\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 0 var(--space-6);\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n\n  @media (max-width: 768px) {\n    padding: 0 var(--space-4);\n  }\n"]))),ga=qn.div(Ar||(Ar=r(["\n  font-family: var(--font-primary);\n  font-size: 1.5rem;\n  font-weight: 700;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n\n  @media (max-width: 768px) {\n    font-size: 1.25rem;\n    gap: var(--space-2);\n  }\n"]))),va=qn.div(Rr||(Rr=r(["\n  width: 40px;\n  height: 40px;\n  background: var(--bg-neural);\n  border-radius: var(--radius-lg);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  box-shadow: var(--shadow-neural);\n\n  @media (max-width: 768px) {\n    width: 36px;\n    height: 36px;\n  }\n"]))),ya=qn.button(Lr||(Lr=r(["\n  background: var(--bg-glass);\n  color: var(--text-secondary);\n  border: 1px solid var(--border-neural);\n  padding: var(--space-3) var(--space-5);\n  border-radius: var(--radius-xl);\n  font-size: 0.9rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: var(--transition-normal);\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  backdrop-filter: blur(10px);\n\n  &:hover {\n    background: var(--primary-50);\n    color: var(--primary-600);\n    border-color: var(--primary-300);\n    transform: translateY(-1px);\n    box-shadow: var(--shadow-lg);\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-2) var(--space-4);\n    font-size: 0.85rem;\n  }\n"]))),ba=qn.h1(Mr||(Mr=r(["\n  font-family: var(--font-primary);\n  font-size: 2.5rem;\n  font-weight: 700;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  text-align: center;\n  margin-bottom: var(--space-4);\n  letter-spacing: -0.02em;\n\n  @media (max-width: 768px) {\n    font-size: 2rem;\n  }\n"]))),wa=qn.p(Dr||(Dr=r(["\n  font-size: 1.125rem;\n  color: var(--text-secondary);\n  text-align: center;\n  margin-bottom: var(--space-16);\n  max-width: 700px;\n  margin-left: auto;\n  margin-right: auto;\n  line-height: 1.6;\n\n  @media (max-width: 768px) {\n    margin-bottom: var(--space-12);\n    font-size: 1rem;\n  }\n"]))),xa=qn.div(Ir||(Ir=r(["\n  display: inline-flex;\n  align-items: center;\n  gap: var(--space-2);\n  background: var(--bg-glass);\n  border: 1px solid var(--border-neural);\n  border-radius: var(--radius-full);\n  padding: var(--space-2) var(--space-4);\n  margin-bottom: var(--space-8);\n  font-size: 0.875rem;\n  font-weight: 500;\n  color: var(--text-accent);\n  backdrop-filter: blur(10px);\n  box-shadow: var(--shadow-glow);\n\n  @media (max-width: 768px) {\n    font-size: 0.8rem;\n    padding: var(--space-2) var(--space-3);\n  }\n"]))),ka=qn.main(Fr||(Fr=r(["\n  padding: var(--space-20) var(--space-4) var(--space-16);\n  max-width: 1200px;\n  margin: 0 auto;\n\n  @media (max-width: 768px) {\n    padding: var(--space-12) var(--space-3) var(--space-8);\n    max-width: 100%;\n  }\n"]))),Sa=qn.div(Ur||(Ur=r(["\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: var(--space-8);\n  max-width: 1200px;\n  margin: 0 auto var(--space-12);\n\n  @media (max-width: 1024px) {\n    grid-template-columns: 1fr;\n    gap: var(--space-4);\n  }\n\n  @media (max-width: 768px) {\n    gap: var(--space-3);\n    margin: 0 auto var(--space-6);\n  }\n"]))),Ea=qn.div(Hr||(Hr=r(["\n  background: var(--bg-glass);\n  border-radius: var(--radius-2xl);\n  padding: var(--space-10);\n  border: 1px solid var(--border-neural);\n  backdrop-filter: blur(20px);\n  box-shadow: var(--shadow-lg);\n  transition: var(--transition-normal);\n  position: relative;\n  overflow: hidden;\n\n  @media (max-width: 768px) {\n    padding: var(--space-6);\n    border-radius: var(--radius-xl);\n  }\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 4px;\n    background: var(--bg-neural);\n    transform: scaleX(0);\n    transition: var(--transition-normal);\n  }\n\n  &:hover {\n    box-shadow: var(--shadow-xl), var(--shadow-glow);\n    border-color: var(--primary-300);\n\n    &::before {\n      transform: scaleX(1);\n    }\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-8);\n  }\n"]))),Ca=qn.h2(Wr||(Wr=r(["\n  font-family: var(--font-primary);\n  font-size: 1.25rem;\n  margin-bottom: var(--space-6);\n  color: var(--text-primary);\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n\n  @media (max-width: 768px) {\n    font-size: 1.125rem;\n    margin-bottom: var(--space-4);\n  }\n"]))),_a=qn.div($r||($r=r(["\n  width: 36px;\n  height: 36px;\n  background: var(--bg-neural);\n  border-radius: var(--radius-lg);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  box-shadow: var(--shadow-neural);\n\n  @media (max-width: 768px) {\n    width: 32px;\n    height: 32px;\n  }\n"]))),za=qn.div(Br||(Br=r(["\n  position: relative;\n  border-radius: var(--radius-2xl);\n  overflow: hidden;\n  background: var(--neural-100);\n  aspect-ratio: 4/3;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: 3px solid var(--border-neural);\n  margin-bottom: var(--space-6);\n  box-shadow: var(--shadow-lg);\n\n  @media (max-width: 768px) {\n    aspect-ratio: 3/4;\n    margin-bottom: var(--space-4);\n    border-radius: var(--radius-xl);\n    border-width: 2px;\n  }\n"]))),Pa=qn(Sr())(Vr||(Vr=r(["\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n"]))),ja=qn.div(qr||(qr=r(["\n  position: absolute;\n  top: var(--space-4);\n  right: var(--space-4);\n  background: ",";\n  color: white;\n  padding: var(--space-3) var(--space-5);\n  border-radius: var(--radius-full);\n  font-size: 0.9rem;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  box-shadow: var(--shadow-lg);\n  animation: ",";\n\n  @keyframes pulse {\n    0%, 100% { opacity: 1; transform: scale(1); }\n    50% { opacity: 0.8; transform: scale(1.05); }\n  }\n"])),e=>e.isRecording?"var(--error-500)":"var(--neural-600)",e=>e.isRecording?"pulse 1.5s infinite":"none"),Ta=qn.div(Kr||(Kr=r(["\n  background: var(--bg-primary);\n  border-radius: var(--radius-2xl);\n  padding: var(--space-8);\n  border: 1px solid var(--border-light);\n  box-shadow: var(--shadow-lg);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  transition: all 0.3s ease;\n\n  &:hover {\n    box-shadow: var(--shadow-xl);\n    border-color: var(--primary-200);\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-6);\n  }\n"]))),Oa=qn.select(Qr||(Qr=r(["\n  width: 100%;\n  padding: var(--space-3) var(--space-4);\n  border: 2px solid var(--border-light);\n  border-radius: var(--radius-lg);\n  background: var(--bg-primary);\n  color: var(--text-primary);\n  font-size: 1rem;\n  font-weight: 500;\n  margin-bottom: var(--space-4);\n  cursor: pointer;\n  transition: var(--transition-normal);\n\n  &:focus {\n    outline: none;\n    border-color: var(--primary-500);\n    box-shadow: 0 0 0 3px var(--primary-100);\n  }\n\n  &:hover {\n    border-color: var(--primary-300);\n  }\n\n  option {\n    padding: var(--space-2);\n    background: var(--bg-primary);\n    color: var(--text-primary);\n  }\n\n  @media (max-width: 768px) {\n    font-size: 1.125rem;\n    padding: var(--space-4);\n    margin-bottom: var(--space-3);\n  }\n"]))),Na=qn.div(Yr||(Yr=r(["\n  width: 300px;\n  height: 300px;\n  background: var(--primary-50);\n  border-radius: var(--radius-2xl);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: var(--space-6);\n  border: 2px solid var(--primary-200);\n  transition: all 0.3s ease;\n  overflow: hidden;\n\n  img {\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n    border-radius: var(--radius-xl);\n  }\n\n  &:hover {\n    transform: scale(1.02);\n    border-color: var(--primary-300);\n  }\n\n  @media (max-width: 768px) {\n    width: 250px;\n    height: 250px;\n  }\n"]))),Aa=qn.h3(Gr||(Gr=r(["\n  font-family: var(--font-primary);\n  font-size: 1.5rem;\n  margin-bottom: var(--space-3);\n  color: var(--text-primary);\n  font-weight: 700;\n\n  @media (max-width: 768px) {\n    font-size: 1.25rem;\n  }\n"]))),Ra=qn.p(Xr||(Xr=r(["\n  text-align: center;\n  line-height: 1.6;\n  color: var(--text-secondary);\n  font-size: 0.9rem;\n  font-weight: 400;\n  max-width: 280px;\n"]))),La=qn.div(Zr||(Zr=r(["\n  display: flex;\n  justify-content: center;\n  gap: var(--space-4);\n  margin-top: var(--space-8);\n\n  @media (max-width: 768px) {\n    flex-direction: column;\n    align-items: center;\n    gap: var(--space-3);\n  }\n"]))),Ma=qn.button(Jr||(Jr=r(["\n  background: ",";\n  border: ",";\n  color: ",";\n  padding: var(--space-3) var(--space-6);\n  border-radius: var(--radius-lg);\n  cursor: pointer;\n  font-size: 0.9rem;\n  font-weight: 600;\n  transition: all 0.2s ease;\n  min-width: 160px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: var(--space-2);\n\n  @media (max-width: 768px) {\n    padding: var(--space-4) var(--space-8);\n    font-size: 1rem;\n    min-width: 180px;\n    border-radius: var(--radius-xl);\n  }\n  box-shadow: ",";\n\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: ",";\n    background: ",";\n  }\n\n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n    transform: none;\n  }\n\n  @media (max-width: 768px) {\n    width: 100%;\n    max-width: 280px;\n  }\n"])),e=>"primary"===e.variant?"var(--primary-600)":"var(--bg-primary)",e=>"primary"===e.variant?"none":"1px solid var(--border-medium)",e=>"primary"===e.variant?"white":"var(--text-primary)",e=>"primary"===e.variant?"var(--shadow-lg)":"var(--shadow-sm)",e=>"primary"===e.variant?"var(--shadow-xl)":"var(--shadow-md)",e=>"primary"===e.variant?"var(--primary-700)":"var(--gray-50)"),Da=qn.div(ea||(ea=r(["\n  text-align: center;\n  margin-top: var(--space-6);\n  padding: var(--space-4) var(--space-6);\n  border-radius: var(--radius-lg);\n  background: ",";\n  color: white;\n  font-weight: 500;\n  font-size: 0.875rem;\n  max-width: 400px;\n  margin-left: auto;\n  margin-right: auto;\n"])),e=>"success"===e.type?"var(--success-500)":"error"===e.type?"var(--error-500)":"var(--primary-600)"),Ia=qn.div(na||(na=r(["\n  margin-top: var(--space-16);\n  background: var(--bg-secondary);\n  padding: var(--space-12) var(--space-4);\n  border-radius: var(--radius-2xl);\n  max-width: 1200px;\n  margin-left: auto;\n  margin-right: auto;\n"]))),Fa=qn.h3(ta||(ta=r(["\n  font-family: var(--font-primary);\n  color: var(--text-primary);\n  margin-bottom: var(--space-8);\n  font-size: 1.5rem;\n  font-weight: 600;\n  text-align: center;\n"]))),Ua=qn.div(ra||(ra=r(["\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n  gap: var(--space-6);\n\n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n    gap: var(--space-4);\n  }\n"]))),Ha=qn.div(aa||(aa=r(["\n  background: var(--bg-primary);\n  padding: var(--space-6);\n  border-radius: var(--radius-xl);\n  border: 1px solid var(--border-light);\n  box-shadow: var(--shadow-sm);\n  transition: all 0.3s ease;\n\n  &:hover {\n    transform: translateY(-2px);\n    border-color: var(--primary-200);\n    box-shadow: var(--shadow-lg);\n  }\n"]))),Wa=qn.p(ia||(ia=r(["\n  margin: 0 0 var(--space-2) 0;\n  color: var(--text-primary);\n  font-weight: 600;\n  font-size: 1rem;\n  font-family: var(--font-primary);\n"]))),$a=qn.p(oa||(oa=r(["\n  margin: 0 0 var(--space-4) 0;\n  font-size: 0.8rem;\n  color: var(--text-tertiary);\n"]))),Ba=qn.button(la||(la=r(["\n  background: var(--primary-600);\n  border: none;\n  border-radius: var(--radius-lg);\n  padding: var(--space-2) var(--space-4);\n  color: white;\n  cursor: pointer;\n  font-size: 0.8rem;\n  font-weight: 500;\n  transition: all 0.2s ease;\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  margin: 0 auto;\n\n  &:hover {\n    background: var(--primary-700);\n    transform: translateY(-1px);\n  }\n"]))),Va=qn.div(sa||(sa=r(["\n  background: var(--bg-glass);\n  border: 2px solid ",";\n  border-radius: var(--radius-xl);\n  padding: var(--space-4);\n  margin-bottom: var(--space-4);\n  text-align: center;\n  transition: var(--transition-normal);\n  backdrop-filter: blur(10px);\n  opacity: ",";\n  min-height: 80px;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n\n  ","\n\n  ","\n\n  @keyframes pulse {\n    0%, 100% { transform: scale(1); }\n    50% { transform: scale(1.02); }\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-3);\n    margin-bottom: var(--space-3);\n    min-height: 70px;\n  }\n"])),e=>e.matched?"var(--success-400)":e.isStale?"var(--warning-300)":"var(--border-light)",e=>e.isStale?.7:1,e=>e.matched&&"\n    background: var(--success-50);\n    box-shadow: 0 0 20px var(--success-200);\n    animation: pulse 1s ease-in-out;\n  ",e=>e.isStale&&"\n    background: var(--warning-50);\n  "),qa=qn.div(ua||(ua=r(["\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: ",";\n  margin-bottom: var(--space-2);\n\n  @media (max-width: 768px) {\n    font-size: 1.125rem;\n  }\n"])),e=>e.matched?"var(--success-700)":e.isStale?"var(--warning-700)":"var(--text-primary)"),Ka=qn.div(ca||(ca=r(["\n  width: 100%;\n  height: 8px;\n  background: var(--bg-secondary);\n  border-radius: var(--radius-full);\n  overflow: hidden;\n  margin-top: var(--space-2);\n"]))),Qa=qn.div(da||(da=r(["\n  height: 100%;\n  background: ",";\n  width: ","%;\n  transition: width 0.3s ease;\n"])),e=>e.confidence>.8?"var(--success-500)":e.confidence>.6?"var(--warning-500)":"var(--error-500)",e=>100*e.confidence),Ya=qn.div(fa||(fa=r(["\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  padding: var(--space-2) var(--space-3);\n  border-radius: var(--radius-lg);\n  font-size: 0.875rem;\n  font-weight: 500;\n  background: ",";\n  color: ",";\n  border: 1px solid ",";\n"])),e=>e.connected?"var(--success-50)":"var(--error-50)",e=>e.connected?"var(--success-700)":"var(--error-700)",e=>e.connected?"var(--success-200)":"var(--error-200)"),Ga={after:{name:"After",gif:"https://lifeprint.com/asl101/gifs/a/after-over-across.gif",description:"Move your dominant hand over and past your non-dominant hand"},airplane:{name:"Airplane",gif:"https://www.lifeprint.com/asl101/gifs/a/airplane-flying.gif",description:"Extend your hand like a plane and move it through the air"},all:{name:"All",gif:"https://lifeprint.com/asl101/gifs/a/all-whole.gif",description:"Circle your dominant hand around your non-dominant hand"},alligator:{name:"Alligator",gif:"https://lifeprint.com/asl101/gifs/a/alligator.gif",description:"Clap your hands together like an alligator's mouth"},animal:{name:"Animal",gif:"https://www.lifeprint.com/asl101/gifs-animated/animal.gif",description:"Place fingertips on chest and move hands back and forth"},any:{name:"Any",gif:"https://lifeprint.com/asl101/gifs/a/any.gif",description:"Point with index finger and twist your wrist"},apple:{name:"Apple",gif:"https://media.giphy.com/media/l0HlHb4dtZZiMYEA8/giphy.gif",description:"Twist your knuckle against your cheek"},arm:{name:"Arm",gif:"https://th.bing.com/th/id/OIP.KkJLqfbp6XEHiIe-jOUb2AHaEc?w=251&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7",description:"Pat your arm with your opposite hand"},aunt:{name:"Aunt",gif:"https://th.bing.com/th/id/OIP.Yz5UUZdNTrVWXf72we_N6wHaHa?rs=1&pid=ImgDetMain",description:"Make an 'A' handshape near your cheek and shake it"},baby:{name:"Baby",gif:"https://media.giphy.com/media/l0HlHb4dtZZiMYEA8/giphy.gif",description:"Rock your arms as if holding a baby"},ball:{name:"Ball",gif:"https://lifeprint.com/asl101/gifs/b/ball.gif",description:"Cup your hands as if holding a ball"},banana:{name:"Banana",gif:"https://media.giphy.com/media/l0HlHb4dtZZiMYEA8/giphy.gif",description:"Peel an imaginary banana with your fingers"},bear:{name:"Bear",gif:"https://lifeprint.com/asl101/gifs/b/bear.gif",description:"Cross your arms and scratch like a bear"},beautiful:{name:"Beautiful",gif:"https://media.giphy.com/media/l0HlHb4dtZZiMYEA8/giphy.gif",description:"Circle your face with your hand and close it into a fist"},bed:{name:"Bed",gif:"https://lifeprint.com/asl101/gifs/b/bed.gif",description:"Rest your head on your hands as if sleeping"},bee:{name:"Bee",gif:"https://lifeprint.com/asl101/gifs/b/bee.gif",description:"Pinch your cheek and brush away as if swatting a bee"},bird:{name:"Bird",gif:"https://lifeprint.com/asl101/gifs/b/bird.gif",description:"Pinch your fingers together near your mouth like a beak"},black:{name:"Black",gif:"https://lifeprint.com/asl101/gifs/b/black.gif",description:"Draw your index finger across your forehead"},blue:{name:"Blue",gif:"https://lifeprint.com/asl101/gifs/b/blue.gif",description:"Shake a 'B' handshape"},book:{name:"Book",gif:"https://lifeprint.com/asl101/gifs/b/book.gif",description:"Open your hands like opening a book"},boy:{name:"Boy",gif:"https://lifeprint.com/asl101/gifs/b/boy.gif",description:"Snap your fingers at your forehead"},brother:{name:"Brother",gif:"https://lifeprint.com/asl101/gifs/b/brother.gif",description:"Make an 'L' shape and point to your forehead, then point forward"},brown:{name:"Brown",gif:"https://lifeprint.com/asl101/gifs/b/brown.gif",description:"Slide your index finger down your cheek"},bug:{name:"Bug",gif:"https://lifeprint.com/asl101/gifs/b/bug.gif",description:"Pinch your nose with your thumb and index finger"},butterfly:{name:"Butterfly",gif:"https://lifeprint.com/asl101/gifs/b/butterfly.gif",description:"Cross your thumbs and flutter your fingers like wings"},car:{name:"Car",gif:"https://lifeprint.com/asl101/gifs/c/car.gif",description:"Pretend to steer a car with both hands"},cat:{name:"Cat",gif:"https://lifeprint.com/asl101/gifs/c/cat.gif",description:"Pinch your cheek and pull out like whiskers"},chair:{name:"Chair",gif:"https://lifeprint.com/asl101/gifs/c/chair.gif",description:"Tap your fingers on your other hand like sitting"},clean:{name:"Clean",gif:"https://lifeprint.com/asl101/gifs/c/clean.gif",description:"Wipe one palm with the other"},cold:{name:"Cold",gif:"https://media.giphy.com/media/l0HlHb4dtZZiMYEA8/giphy.gif",description:"Shiver with both hands in fists"},cow:{name:"Cow",gif:"https://lifeprint.com/asl101/gifs/c/cow.gif",description:"Twist your thumb at your temple like a horn"},cry:{name:"Cry",gif:"https://lifeprint.com/asl101/gifs/c/cry.gif",description:"Draw tears down your cheeks with your index fingers"},cute:{name:"Cute",gif:"https://lifeprint.com/asl101/gifs/c/cute-sugar.gif",description:"Brush your chin with your fingers"},dad:{name:"Dad",gif:"https://media.giphy.com/media/l0MYQcLKwtl5v6H1S/giphy.gif",description:"Tap your forehead with your thumb"},dance:{name:"Dance",gif:"https://www.bing.com/th/id/OGC.********************************?pid=1.7&rurl=https%3a%2f%2fmedia.giphy.com%2fmedia%2f3o7TKMspYQjQTbOz2U%2fgiphy.gif&ehk=h%2bdBHCxuoOT89ovSy5uTk6MCL9acaBEV6ld9lrVDRF4%3d",description:"Swing your fingers over your palm like dancing"},dirty:{name:"Dirty",gif:"https://th.bing.com/th/id/OIP.wRA7r1OPPUuEoLL4Hds9jAHaHa?rs=1&pid=ImgDetMain",description:"Wiggle your fingers under your chin"},dog:{name:"Dog",gif:"https://th.bing.com/th/id/R.********************************?rik=uVshGsOHXgldoQ&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fd%2fdog1.gif&ehk=Xnfrvg0xwy1u%2fae9vWdKYMT25%2bF6qoteW2FThCbVrOA%3d&risl=&pid=ImgRaw&r=0",description:"Pat your leg and snap your fingers"},eat:{name:"Eat",gif:"https://lifeprint.com/asl101/gifs/e/eat.gif",description:"Bring your fingers to your mouth as if eating"},elephant:{name:"Elephant",gif:"https://lifeprint.com/asl101/gifs/e/elephant.gif",description:"Trace the shape of an elephant's trunk with your hand"},fish:{name:"Fish",gif:"https://lifeprint.com/asl101/gifs/f/fish.gif",description:"Move your hand like a fish swimming"},flower:{name:"Flower",gif:"https://lifeprint.com/asl101/gifs/f/flower.gif",description:"Touch your nose with your fingertips"},friend:{name:"Friend",gif:"https://lifeprint.com/asl101/gifs/f/friend.gif",description:"Hook your index fingers together"},girl:{name:"Girl",gif:"https://lifeprint.com/asl101/gifs/g/girl.gif",description:"Trace your jawline with your thumb"},go:{name:"Go",gif:"https://lifeprint.com/asl101/gifs/g/go.gif",description:"Point both index fingers forward and bend them"},good:{name:"Good",gif:"https://lifeprint.com/asl101/gifs/g/good.gif",description:"Touch your chin and move your hand forward"},green:{name:"Green",gif:"https://lifeprint.com/asl101/gifs/g/green.gif",description:"Shake a 'G' handshape"},hair:{name:"Hair",gif:"https://www.lifeprint.com/asl101/gifs/h/hair-g-version.gif",description:"Pinch a strand of your hair"},happy:{name:"Happy",gif:"https://media0.giphy.com/media/3o7TKFpahYpUp4g0N2/giphy.gif?cid=790b7611bca188a92e2e759876756ef62ba95b7cdd337c77&rid=giphy.gif&ct=g",description:"Brush your chest upward with both hands"},hello:{name:"Hello",gif:"https://media.giphy.com/media/3o7TKNKOfKlIhbD3gY/giphy.gif",description:"Wave your hand from side to side with palm facing forward"},home:{name:"Home",gif:"https://th.bing.com/th/id/R.********************************?rik=%2bnBd%2foQjxnoPfg&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fh%2fhome-2.gif&ehk=7yD%2f%2fh6JN1Y4D4BOrUjgKW4Jccy2Y4GVYLf%2fzyk%2b5YY%3d&risl=&pid=ImgRaw&r=0",description:"Touch your mouth then your cheek"},horse:{name:"Horse",gif:"https://media.giphy.com/media/l0HlM5HffraiQaHUk/giphy.gif",description:"Extend your thumb and fingers at your temple like ears"},hot:{name:"Hot",gif:"https://media.giphy.com/media/3o6Zt99k5aDok347bG/giphy.gif",description:"Touch your mouth and quickly move your hand away"},hungry:{name:"Hungry",gif:"https://media.giphy.com/media/l3vR0xkdFEz4tnfTq/giphy.gif",description:"Move your hand down your chest like food going down"},jump:{name:"Jump",gif:"https://lifeprint.com/asl101/gifs-animated/jump.gif",description:"Bounce your fingers on your palm"},like:{name:"Like",gif:"https://lifeprint.com/asl101/gifs/l/like.gif",description:"Pull your thumb and middle finger from your chest"},look:{name:"Look",gif:"https://th.bing.com/th/id/R.********************************?rik=pYhzip7LqNs7qw&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fl%2flook-at-1.gif&ehk=rFJ7dBrMGFDK0nHLzrOPAzROVE7yqyDEcb%2btLqKqYOI%3d&risl=&pid=ImgRaw&r=0",description:"Point your fingers from your eyes forward"},love:{name:"Love",gif:"https://lifeprint.com/asl101/gifs/l/love.gif",description:"Cross your arms over your chest"},mom:{name:"Mom",gif:"https://media.giphy.com/media/l0MYQcLKwtl5v6H1S/giphy.gif",description:"Tap your chin with your thumb"},more:{name:"More",gif:"https://lifeprint.com/asl101/gifs/m/more.gif",description:"Tap your fingertips together"},no:{name:"No",gif:"https://lifeprint.com/asl101/gifs/n/no.gif",description:"Snap your fingers together"},orange:{name:"Orange",gif:"https://lifeprint.com/asl101/gifs/o/orange.gif",description:"Squeeze your hand at your mouth like squeezing an orange"},please:{name:"Please",gif:"https://lifeprint.com/asl101/gifs/p/please.gif",description:"Rub your chest in a circular motion"},red:{name:"Red",gif:"https://lifeprint.com/asl101/gifs/r/red.gif",description:"Brush your lips with your index finger"},run:{name:"Run",gif:"https://lifeprint.com/asl101/gifs/r/run.gif",description:"Hook your thumbs and wiggle your fingers"},sad:{name:"Sad",gif:"https://lifeprint.com/asl101/gifs/s/sad.gif",description:"Drop both hands down your face"},see:{name:"See",gif:"https://lifeprint.com/asl101/gifs/l/look-at-2.gif",description:"Point from your eyes forward"},sleep:{name:"Sleep",gif:"https://media4.giphy.com/media/3o7TKnRuBdakLslcaI/200.gif?cid=790b76110d8f185a9713f36dd65a0df801576e01b403c95c&rid=200.gif&ct=g",description:"Rest your head on your hands"},sorry:{name:"Sorry",gif:"https://lifeprint.com/asl101/gifs/s/sorry.gif",description:"Rub your fist in a circle on your chest"},thank:{name:"Thank You",gif:"https://lifeprint.com/asl101/gifs/t/thank-you.gif",description:"Touch your chin and move your hand forward"},water:{name:"Water",gif:"https://lifeprint.com/asl101/gifs/w/water.gif",description:"Tap your chin with a 'W' handshape"},white:{name:"White",gif:"https://lifeprint.com/asl101/gifs/w/white.gif",description:"Touch your chest and pull your hand away"},yellow:{name:"Yellow",gif:"https://lifeprint.com/asl101/gifs/y/yellow.gif",description:"Shake a 'Y' handshape"},yes:{name:"Yes",gif:"https://media.tenor.com/oYIirlyIih0AAAAC/yes-asl.gif",description:"Nod your fist up and down"}},Xa=n=>{let{onBackToHome:t}=n;const[r,a]=(0,e.useState)("hello"),[i,o]=(0,e.useState)(""),[l,s]=(0,e.useState)(!1),[u,c]=(0,e.useState)(!1),[d,f]=(0,e.useState)([]),p=(0,e.useRef)(null),h=(0,e.useRef)(null),m=(0,e.useRef)(0),{isConnected:g,prediction:v,isAIRecording:y,recordingStatus:b,signMatched:w,targetSign:x,startRecording:k,stopRecording:S,startFrameCapture:E}=(()=>{const[n,t]=(0,e.useState)(!1),[r,a]=(0,e.useState)(null),[i,o]=(0,e.useState)(null),[l,s]=(0,e.useState)(!1),[u,c]=(0,e.useState)(""),[d,f]=(0,e.useState)(null),[p,h]=(0,e.useState)(!1),[m,g]=(0,e.useState)(""),[v,y]=(0,e.useState)([]),b=(0,e.useRef)(null),w=(0,e.useRef)(null),x=(0,e.useRef)(null),k=(0,e.useCallback)(()=>{try{b.current=new WebSocket("ws://localhost:8001/ws/detect"),b.current.onopen=()=>{console.log("Connected to sign detection backend"),t(!0),c("Connected to AI backend")},b.current.onmessage=e=>{try{const n=JSON.parse(e.data);switch(console.log("Received from backend:",n.type,n.prediction),n.type){case"frame_processed":f(n.processed_frame),h(n.sign_matched||!1),g(n.target_sign||""),n.prediction?(a(n.prediction),o(n.prediction),y(e=>[n.prediction,...e.slice(0,4)]),console.log("Detected: ".concat(n.prediction.sign," (").concat(Math.round(100*n.prediction.confidence),"%)"))):i&&a(Xn(Xn({},i),{},{confidence:.7*i.confidence,isStale:!0}));break;case"recording_started":s(!0),c("Recording started for: ".concat(n.target_sign));break;case"recording_stopped":s(!1),n.result?c("Recording saved: ".concat(n.result.frame_count," frames")):c("Recording stopped");break;default:console.log("Unknown message type:",n.type)}}catch(n){console.error("Error parsing WebSocket message:",n)}},b.current.onclose=()=>{console.log("Disconnected from sign detection backend"),t(!1),c("Disconnected from backend"),w.current=setTimeout(()=>{console.log("Attempting to reconnect..."),k()},3e3)},b.current.onerror=e=>{console.error("WebSocket error:",e),c("Connection error")}}catch(e){console.error("Error connecting to WebSocket:",e),c("Failed to connect to backend")}},[]),S=(0,e.useCallback)(()=>{w.current&&clearTimeout(w.current),x.current&&clearInterval(x.current),b.current&&(b.current.close(),b.current=null),t(!1)},[]),E=(0,e.useCallback)(e=>{if(b.current&&b.current.readyState===WebSocket.OPEN){const n={type:"frame",frame:e};b.current.send(JSON.stringify(n))}},[]),C=(0,e.useCallback)(e=>{if(b.current&&b.current.readyState===WebSocket.OPEN){const n={type:"start_recording",target_sign:e};b.current.send(JSON.stringify(n)),g(e)}},[]),_=(0,e.useCallback)(()=>{if(b.current&&b.current.readyState===WebSocket.OPEN){const e={type:"stop_recording"};b.current.send(JSON.stringify(e))}},[]),z=(0,e.useCallback)(function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:200;x.current&&clearInterval(x.current),x.current=setInterval(()=>{if(e.current&&n){const n=e.current.getScreenshot();n&&(console.log("Sending frame to backend"),E(n))}},t)},[n,E]),P=(0,e.useCallback)(()=>{x.current&&(clearInterval(x.current),x.current=null)},[]);return(0,e.useEffect)(()=>(k(),()=>{S()}),[k,S]),{isConnected:n,prediction:r,lastPrediction:i,predictionHistory:v,isRecording:l,recordingStatus:u,processedFrame:d,signMatched:p,targetSign:m,connect:k,disconnect:S,sendFrame:E,startRecording:C,stopRecording:_,startFrameCapture:z,stopFrameCapture:P}})(),C=(0,e.useCallback)(e=>{a(e.target.value)},[]),_=(0,e.useCallback)(()=>{p.current?(s(!0),E(p,100),o("AI detection started")):o("Camera not available")},[E]),z=(0,e.useCallback)(()=>{g?p.current?(u?(c(!1),o("Auto-recording disabled"),m.current=0,h.current&&clearTimeout(h.current)):(c(!0),o('Get ready! Perform "'.concat(Ga[r].name,"\" sign when you're confident. Recording will start automatically when detected.")),m.current=0),l||_()):o("Camera not available"):o("AI backend not connected")},[r,g,l,_,u]),P=(0,e.useCallback)(()=>{y&&S(),c(!1),m.current=0,h.current&&clearTimeout(h.current),o("Recording stopped")},[S,y]);return(0,e.useEffect)(()=>{g&&p.current&&!l&&_()},[g,_,l]),(0,e.useEffect)(()=>{if(!v||!u)return void(m.current=0);const e=v.sign.toLowerCase(),n=Ga[r].name.toLowerCase(),t=v.confidence;return e===n&&t>.8?(m.current+=1,m.current>=3&&!y&&(o("Perfect! Recording ".concat(Ga[r].name,"...")),k(Ga[r].name),h.current=setTimeout(()=>{S(),o("Recording complete! Great job with ".concat(Ga[r].name)),c(!1),m.current=0},3e3))):m.current=0,()=>{h.current&&clearTimeout(h.current)}},[v,u,r,y,k,S]),(0,Vt.jsxs)(pa,{children:[(0,Vt.jsx)(ha,{children:(0,Vt.jsxs)(ma,{children:[(0,Vt.jsxs)(ga,{children:[(0,Vt.jsx)(va,{children:(0,Vt.jsx)(ot,{size:24})}),"ASL Neural"]}),(0,Vt.jsxs)(ya,{onClick:t,children:[(0,Vt.jsx)(Er,{size:18}),"Back to Home"]})]})}),(0,Vt.jsxs)(ka,{children:[(0,Vt.jsx)("div",{style:{textAlign:"center",marginBottom:"var(--space-12)"},children:(0,Vt.jsxs)(xa,{children:[(0,Vt.jsx)(Cr,{size:16}),"Neural Vision Active"]})}),(0,Vt.jsx)(ba,{children:"AI Training Session"}),(0,Vt.jsx)(wa,{children:"Experience real-time neural network analysis as our AI learns from your sign language practice"}),(0,Vt.jsxs)(Sa,{children:[(0,Vt.jsxs)(Ea,{children:[(0,Vt.jsxs)(Ca,{children:[(0,Vt.jsx)(_a,{children:(0,Vt.jsx)(ct,{size:24})}),"Neural Vision Feed"]}),(0,Vt.jsxs)(Ya,{connected:g,children:[g?(0,Vt.jsx)(_r,{size:16}):(0,Vt.jsx)(zr,{size:16}),g?"AI Connected":"AI Disconnected"]}),v&&(0,Vt.jsxs)(Va,{matched:w,isStale:v.isStale,children:[(0,Vt.jsxs)(qa,{matched:w,isStale:v.isStale,children:["Detected: ",v.sign,v.isStale&&" (previous)"]}),(0,Vt.jsx)(Ka,{children:(0,Vt.jsx)(Qa,{confidence:v.confidence})}),(0,Vt.jsxs)("div",{style:{fontSize:"0.875rem",marginTop:"8px",color:"var(--text-secondary)"},children:["Confidence: ",Math.round(100*v.confidence),"%",w&&x&&(0,Vt.jsx)("span",{style:{color:"var(--success-600)",marginLeft:"8px"},children:"\u2713 Match! Recording..."}),u&&!y&&(0,Vt.jsxs)("div",{style:{color:"var(--primary-600)",marginTop:"4px"},children:['\ud83c\udfaf Auto-record mode: Perform "',Ga[r].name,'" sign']})]})]}),!v&&u&&(0,Vt.jsxs)(Va,{children:[(0,Vt.jsxs)(qa,{children:['\ud83c\udfaf Ready to detect "',Ga[r].name,'"']}),(0,Vt.jsx)("div",{style:{fontSize:"0.875rem",color:"var(--text-secondary)"},children:"Perform the sign when you're confident. Recording will start automatically."})]}),(0,Vt.jsxs)(za,{children:[(0,Vt.jsx)(Pa,{ref:p,audio:!1,screenshotFormat:"image/jpeg",videoConstraints:{width:640,height:480,facingMode:"user"}}),(0,Vt.jsx)(ja,{isRecording:y,children:y?(0,Vt.jsxs)(Vt.Fragment,{children:[(0,Vt.jsx)("div",{style:{width:"8px",height:"8px",borderRadius:"50%",backgroundColor:"white",marginRight:"4px"}}),"Recording"]}):(0,Vt.jsxs)(Vt.Fragment,{children:[(0,Vt.jsx)(Cr,{size:16}),"Ready"]})})]})]}),(0,Vt.jsxs)(Ta,{children:[(0,Vt.jsxs)(Ca,{children:[(0,Vt.jsx)(_a,{children:(0,Vt.jsx)(ht,{size:24})}),"Select a Sign"]}),(0,Vt.jsx)(Oa,{value:r,onChange:C,disabled:y,children:Object.keys(Ga).map(e=>(0,Vt.jsx)("option",{value:e,children:Ga[e].name},e))}),(0,Vt.jsxs)(Na,{children:[(0,Vt.jsx)("img",{src:Ga[r].gif,alt:Ga[r].name,onError:e=>{e.target.style.display="none",e.target.nextSibling.style.display="flex"}}),(0,Vt.jsx)("div",{style:{display:"none",fontSize:"3rem"},children:"\ud83d\udcf7"})]}),(0,Vt.jsx)(Aa,{children:Ga[r].name}),(0,Vt.jsx)(Ra,{children:Ga[r].description})]})]}),(0,Vt.jsx)(La,{children:(0,Vt.jsx)(Ma,{variant:"primary",onClick:y||u?P:z,children:y?(0,Vt.jsxs)(Vt.Fragment,{children:[(0,Vt.jsx)(Pr,{size:18}),"Stop Recording"]}):u?(0,Vt.jsxs)(Vt.Fragment,{children:[(0,Vt.jsx)(Pr,{size:18}),"Cancel Auto-Record"]}):(0,Vt.jsxs)(Vt.Fragment,{children:[(0,Vt.jsx)(st,{size:18}),"Start Smart Recording"]})})}),(i||b)&&(0,Vt.jsx)(Da,{type:(i||b).includes("error")?"error":(i||b).includes("success")?"success":"info",children:b||i}),d.length>0&&(0,Vt.jsxs)(Ia,{children:[(0,Vt.jsx)(Fa,{children:"Your Practice Recordings"}),(0,Vt.jsx)(Ua,{children:d.map(e=>(0,Vt.jsxs)(Ha,{children:[(0,Vt.jsx)(Wa,{children:e.sign}),(0,Vt.jsx)($a,{children:new Date(e.timestamp).toLocaleString()}),(0,Vt.jsxs)(Ba,{onClick:()=>(e=>{const n=document.createElement("a");n.href=e.url,n.download="sign_".concat(e.sign,"_").concat(e.timestamp,".webm"),n.click()})(e),children:[(0,Vt.jsx)(jr,{size:16}),"Download"]})]},e.id))})]})]})]})},Za=it("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]),Ja=it("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]]),ei=it("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]);var ni,ti,ri,ai,ii,oi,li,si,ui,ci,di,fi,pi,hi,mi;const gi=qn.div(ni||(ni=r(["\n  min-height: 100vh;\n  background: var(--bg-primary);\n  position: relative;\n  overflow-x: hidden;\n  \n  &::before {\n    content: '';\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: \n      radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),\n      radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.03) 0%, transparent 50%);\n    pointer-events: none;\n    z-index: 0;\n  }\n"]))),vi=qn.nav(ti||(ti=r(["\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 50;\n  background: var(--bg-glass);\n  backdrop-filter: blur(20px);\n  border-bottom: 1px solid var(--border-neural);\n  padding: var(--space-4) 0;\n  transition: var(--transition-normal);\n"]))),yi=qn.div(ri||(ri=r(["\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 0 var(--space-6);\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  \n  @media (max-width: 768px) {\n    padding: 0 var(--space-4);\n  }\n"]))),bi=qn.div(ai||(ai=r(["\n  font-family: var(--font-primary);\n  font-size: 1.5rem;\n  font-weight: 700;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n  \n  @media (max-width: 768px) {\n    font-size: 1.25rem;\n    gap: var(--space-2);\n  }\n"]))),wi=qn.div(ii||(ii=r(["\n  width: 40px;\n  height: 40px;\n  background: var(--bg-neural);\n  border-radius: var(--radius-lg);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  box-shadow: var(--shadow-neural);\n  \n  @media (max-width: 768px) {\n    width: 36px;\n    height: 36px;\n  }\n"]))),xi=qn.button(oi||(oi=r(["\n  background: var(--bg-glass);\n  color: var(--text-secondary);\n  border: 1px solid var(--border-neural);\n  padding: var(--space-3) var(--space-5);\n  border-radius: var(--radius-xl);\n  font-size: 0.9rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: var(--transition-normal);\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  backdrop-filter: blur(10px);\n  \n  &:hover {\n    background: var(--primary-50);\n    color: var(--primary-600);\n    border-color: var(--primary-300);\n    transform: translateY(-1px);\n    box-shadow: var(--shadow-lg);\n  }\n"]))),ki=qn.main(li||(li=r(["\n  padding: var(--space-24) var(--space-6) var(--space-20);\n  max-width: 1400px;\n  margin: 0 auto;\n  position: relative;\n  z-index: 1;\n  \n  @media (max-width: 768px) {\n    padding: var(--space-20) var(--space-4) var(--space-16);\n  }\n"]))),Si=qn.h1(si||(si=r(["\n  font-family: var(--font-primary);\n  font-size: 3rem;\n  font-weight: 700;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  text-align: center;\n  margin-bottom: var(--space-4);\n  letter-spacing: -0.02em;\n  \n  @media (max-width: 768px) {\n    font-size: 2.25rem;\n  }\n"]))),Ei=qn.p(ui||(ui=r(["\n  font-size: 1.25rem;\n  color: var(--text-secondary);\n  text-align: center;\n  margin-bottom: var(--space-16);\n  max-width: 700px;\n  margin-left: auto;\n  margin-right: auto;\n  line-height: 1.6;\n  \n  @media (max-width: 768px) {\n    font-size: 1.125rem;\n    margin-bottom: var(--space-12);\n  }\n"]))),Ci=qn.div(ci||(ci=r(["\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: var(--space-8);\n  margin-bottom: var(--space-20);\n  \n  @media (max-width: 768px) {\n    gap: var(--space-6);\n    margin-bottom: var(--space-16);\n  }\n"]))),_i=qn.div(di||(di=r(["\n  background: var(--bg-glass);\n  border-radius: var(--radius-2xl);\n  padding: var(--space-8);\n  border: 1px solid var(--border-neural);\n  backdrop-filter: blur(20px);\n  box-shadow: var(--shadow-lg);\n  text-align: center;\n  transition: var(--transition-normal);\n  \n  &:hover {\n    transform: translateY(-4px);\n    box-shadow: var(--shadow-xl), var(--shadow-glow);\n    border-color: var(--primary-300);\n  }\n"]))),zi=qn.div(fi||(fi=r(["\n  width: 64px;\n  height: 64px;\n  background: var(--bg-neural);\n  border-radius: var(--radius-xl);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin: 0 auto var(--space-4);\n  box-shadow: var(--shadow-neural);\n"]))),Pi=qn.div(pi||(pi=r(["\n  font-size: 2rem;\n  font-weight: 700;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  margin-bottom: var(--space-2);\n  font-family: var(--font-primary);\n"]))),ji=qn.div(hi||(hi=r(["\n  font-size: 1rem;\n  color: var(--text-secondary);\n  font-weight: 500;\n"]))),Ti=(qn.h2(mi||(mi=r(["\n  font-family: var(--font-primary);\n  font-size: 2rem;\n  font-weight: 600;\n  color: var(--text-primary);\n  margin-bottom: var(--space-6);\n  text-align: center;\n\n  @media (max-width: 768px) {\n    font-size: 1.75rem;\n  }\n"]))),e=>{let{onBackToHome:n}=e;return(0,Vt.jsxs)(gi,{children:[(0,Vt.jsx)(vi,{children:(0,Vt.jsxs)(yi,{children:[(0,Vt.jsxs)(bi,{children:[(0,Vt.jsx)(wi,{children:(0,Vt.jsx)(ot,{size:24})}),"ASL Neural"]}),(0,Vt.jsxs)(xi,{onClick:n,children:[(0,Vt.jsx)(Er,{size:18}),"Back to Home"]})]})}),(0,Vt.jsxs)(ki,{children:[(0,Vt.jsx)(Si,{children:"About ASL Neural"}),(0,Vt.jsx)(Ei,{children:"Pioneering the future of accessibility through advanced artificial intelligence and computer vision technology for sign language education"}),(0,Vt.jsxs)(Ci,{children:[(0,Vt.jsxs)(_i,{children:[(0,Vt.jsx)(zi,{children:(0,Vt.jsx)(Za,{size:28,color:"white"})}),(0,Vt.jsx)(Pi,{children:"50K+"}),(0,Vt.jsx)(ji,{children:"Global Users"})]}),(0,Vt.jsxs)(_i,{children:[(0,Vt.jsx)(zi,{children:(0,Vt.jsx)(dt,{size:28,color:"white"})}),(0,Vt.jsx)(Pi,{children:"2.5M"}),(0,Vt.jsx)(ji,{children:"Neural Patterns"})]}),(0,Vt.jsxs)(_i,{children:[(0,Vt.jsx)(zi,{children:(0,Vt.jsx)(Ja,{size:28,color:"white"})}),(0,Vt.jsx)(Pi,{children:"99.2%"}),(0,Vt.jsx)(ji,{children:"AI Accuracy"})]}),(0,Vt.jsxs)(_i,{children:[(0,Vt.jsx)(zi,{children:(0,Vt.jsx)(ei,{size:28,color:"white"})}),(0,Vt.jsx)(Pi,{children:"15+"}),(0,Vt.jsx)(ji,{children:"Countries"})]})]})]})]})}),Oi=it("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]),Ni=it("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]]),Ai=it("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]]),Ri=it("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]),Li=it("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]);var Mi,Di,Ii,Fi,Ui,Hi,Wi,$i,Bi,Vi,qi,Ki,Qi,Yi,Gi,Xi,Zi,Ji,eo,no,to;const ro=qn.div(Mi||(Mi=r(["\n  min-height: 100vh;\n  background: var(--bg-primary);\n  position: relative;\n  overflow-x: hidden;\n  \n  &::before {\n    content: '';\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: \n      radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),\n      radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.03) 0%, transparent 50%);\n    pointer-events: none;\n    z-index: 0;\n  }\n"]))),ao=qn.nav(Di||(Di=r(["\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 50;\n  background: var(--bg-glass);\n  backdrop-filter: blur(20px);\n  border-bottom: 1px solid var(--border-neural);\n  padding: var(--space-4) 0;\n  transition: var(--transition-normal);\n"]))),io=qn.div(Ii||(Ii=r(["\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 0 var(--space-6);\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  \n  @media (max-width: 768px) {\n    padding: 0 var(--space-4);\n  }\n"]))),oo=qn.div(Fi||(Fi=r(["\n  font-family: var(--font-primary);\n  font-size: 1.5rem;\n  font-weight: 700;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n\n  @media (max-width: 768px) {\n    font-size: 1.25rem;\n    gap: var(--space-2);\n  }\n"]))),lo=qn.div(Ui||(Ui=r(["\n  width: 40px;\n  height: 40px;\n  background: var(--bg-neural);\n  border-radius: var(--radius-lg);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  box-shadow: var(--shadow-neural);\n"]))),so=qn.button(Hi||(Hi=r(["\n  background: var(--bg-glass);\n  color: var(--text-secondary);\n  border: 1px solid var(--border-neural);\n  padding: var(--space-3) var(--space-5);\n  border-radius: var(--radius-xl);\n  font-size: 0.9rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: var(--transition-normal);\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  backdrop-filter: blur(10px);\n  \n  &:hover {\n    background: var(--primary-50);\n    color: var(--primary-600);\n    border-color: var(--primary-300);\n    transform: translateY(-1px);\n    box-shadow: var(--shadow-lg);\n  }\n"]))),uo=qn.main(Wi||(Wi=r(["\n  padding: var(--space-24) var(--space-6) var(--space-20);\n  max-width: 1200px;\n  margin: 0 auto;\n  position: relative;\n  z-index: 1;\n  \n  @media (max-width: 768px) {\n    padding: var(--space-20) var(--space-4) var(--space-16);\n  }\n"]))),co=qn.h1($i||($i=r(["\n  font-family: var(--font-primary);\n  font-size: 2.75rem;\n  font-weight: 700;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  text-align: center;\n  margin-bottom: var(--space-4);\n  letter-spacing: -0.02em;\n\n  @media (max-width: 768px) {\n    font-size: 2.25rem;\n  }\n"]))),fo=qn.p(Bi||(Bi=r(["\n  font-size: 1.125rem;\n  color: var(--text-secondary);\n  text-align: center;\n  margin-bottom: var(--space-16);\n  max-width: 700px;\n  margin-left: auto;\n  margin-right: auto;\n  line-height: 1.6;\n\n  @media (max-width: 768px) {\n    font-size: 1rem;\n    margin-bottom: var(--space-12);\n  }\n"]))),po=qn.div(Vi||(Vi=r(["\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: var(--space-12);\n  \n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n    gap: var(--space-8);\n  }\n"]))),ho=qn.div(qi||(qi=r(["\n  background: var(--bg-glass);\n  border-radius: var(--radius-2xl);\n  padding: var(--space-10);\n  border: 1px solid var(--border-neural);\n  backdrop-filter: blur(20px);\n  box-shadow: var(--shadow-lg);\n"]))),mo=qn.form(Ki||(Ki=r(["\n  background: var(--bg-glass);\n  border-radius: var(--radius-2xl);\n  padding: var(--space-10);\n  border: 1px solid var(--border-neural);\n  backdrop-filter: blur(20px);\n  box-shadow: var(--shadow-lg);\n"]))),go=qn.h2(Qi||(Qi=r(["\n  font-family: var(--font-primary);\n  font-size: 1.75rem;\n  font-weight: 700;\n  color: var(--text-primary);\n  margin-bottom: var(--space-8);\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n"]))),vo=qn.div(Yi||(Yi=r(["\n  display: flex;\n  align-items: center;\n  gap: var(--space-4);\n  margin-bottom: var(--space-6);\n  padding: var(--space-4);\n  border-radius: var(--radius-lg);\n  transition: var(--transition-normal);\n  \n  &:hover {\n    background: var(--primary-50);\n  }\n"]))),yo=qn.div(Gi||(Gi=r(["\n  width: 48px;\n  height: 48px;\n  background: var(--bg-neural);\n  border-radius: var(--radius-lg);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  box-shadow: var(--shadow-neural);\n"]))),bo=qn.div(Xi||(Xi=r(["\n  h3 {\n    font-size: 1.125rem;\n    font-weight: 600;\n    color: var(--text-primary);\n    margin-bottom: var(--space-1);\n  }\n  \n  p {\n    color: var(--text-secondary);\n    font-size: 0.9rem;\n  }\n"]))),wo=qn.div(Zi||(Zi=r(["\n  margin-bottom: var(--space-6);\n"]))),xo=qn.label(Ji||(Ji=r(["\n  display: block;\n  font-weight: 500;\n  color: var(--text-primary);\n  margin-bottom: var(--space-2);\n  font-size: 0.9rem;\n"]))),ko=qn.input(eo||(eo=r(["\n  width: 100%;\n  padding: var(--space-4);\n  border: 2px solid var(--border-light);\n  border-radius: var(--radius-lg);\n  font-size: 1rem;\n  transition: var(--transition-normal);\n  background: var(--bg-primary);\n  \n  &:focus {\n    outline: none;\n    border-color: var(--primary-500);\n    box-shadow: var(--shadow-glow);\n  }\n"]))),So=qn.textarea(no||(no=r(["\n  width: 100%;\n  padding: var(--space-4);\n  border: 2px solid var(--border-light);\n  border-radius: var(--radius-lg);\n  font-size: 1rem;\n  transition: var(--transition-normal);\n  background: var(--bg-primary);\n  min-height: 120px;\n  resize: vertical;\n  \n  &:focus {\n    outline: none;\n    border-color: var(--primary-500);\n    box-shadow: var(--shadow-glow);\n  }\n"]))),Eo=qn.button(to||(to=r(["\n  background: var(--bg-neural);\n  color: white;\n  border: none;\n  padding: var(--space-4) var(--space-8);\n  border-radius: var(--radius-xl);\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: var(--transition-normal);\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  box-shadow: var(--shadow-neural);\n  width: 100%;\n  justify-content: center;\n  \n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: var(--shadow-xl), var(--shadow-glow);\n  }\n"]))),Co=n=>{let{onBackToHome:t}=n;const[r,a]=(0,e.useState)({name:"",email:"",subject:"",message:""}),i=e=>{a(Xn(Xn({},r),{},{[e.target.name]:e.target.value}))};return(0,Vt.jsxs)(ro,{children:[(0,Vt.jsx)(ao,{children:(0,Vt.jsxs)(io,{children:[(0,Vt.jsxs)(oo,{children:[(0,Vt.jsx)(lo,{children:(0,Vt.jsx)(ot,{size:24})}),"ASL Neural"]}),(0,Vt.jsxs)(so,{onClick:t,children:[(0,Vt.jsx)(Er,{size:18}),"Back to Home"]})]})}),(0,Vt.jsxs)(uo,{children:[(0,Vt.jsx)(co,{children:"Contact Us"}),(0,Vt.jsx)(fo,{children:"Get in touch with our team to learn more about ASL Neural technology or discuss partnership opportunities"}),(0,Vt.jsxs)(po,{children:[(0,Vt.jsxs)(ho,{children:[(0,Vt.jsxs)(go,{children:[(0,Vt.jsx)(Oi,{size:24}),"Get in Touch"]}),(0,Vt.jsxs)(vo,{children:[(0,Vt.jsx)(yo,{children:(0,Vt.jsx)(Ni,{size:20})}),(0,Vt.jsxs)(bo,{children:[(0,Vt.jsx)("h3",{children:"Email"}),(0,Vt.jsx)("p",{children:"<EMAIL>"})]})]}),(0,Vt.jsxs)(vo,{children:[(0,Vt.jsx)(yo,{children:(0,Vt.jsx)(Ai,{size:20})}),(0,Vt.jsxs)(bo,{children:[(0,Vt.jsx)("h3",{children:"Phone"}),(0,Vt.jsx)("p",{children:"+****************"})]})]}),(0,Vt.jsxs)(vo,{children:[(0,Vt.jsx)(yo,{children:(0,Vt.jsx)(Ri,{size:20})}),(0,Vt.jsxs)(bo,{children:[(0,Vt.jsx)("h3",{children:"Address"}),(0,Vt.jsxs)("p",{children:["123 AI Innovation Drive",(0,Vt.jsx)("br",{}),"San Francisco, CA 94105"]})]})]}),(0,Vt.jsxs)(vo,{children:[(0,Vt.jsx)(yo,{children:(0,Vt.jsx)(ft,{size:20})}),(0,Vt.jsxs)(bo,{children:[(0,Vt.jsx)("h3",{children:"Website"}),(0,Vt.jsx)("p",{children:"www.aslneural.ai"})]})]})]}),(0,Vt.jsxs)(mo,{onSubmit:e=>{e.preventDefault(),console.log("Form submitted:",r),alert("Thank you for your message! We'll get back to you soon."),a({name:"",email:"",subject:"",message:""})},children:[(0,Vt.jsxs)(go,{children:[(0,Vt.jsx)(Li,{size:24}),"Send Message"]}),(0,Vt.jsxs)(wo,{children:[(0,Vt.jsx)(xo,{htmlFor:"name",children:"Name"}),(0,Vt.jsx)(ko,{type:"text",id:"name",name:"name",value:r.name,onChange:i,required:!0})]}),(0,Vt.jsxs)(wo,{children:[(0,Vt.jsx)(xo,{htmlFor:"email",children:"Email"}),(0,Vt.jsx)(ko,{type:"email",id:"email",name:"email",value:r.email,onChange:i,required:!0})]}),(0,Vt.jsxs)(wo,{children:[(0,Vt.jsx)(xo,{htmlFor:"subject",children:"Subject"}),(0,Vt.jsx)(ko,{type:"text",id:"subject",name:"subject",value:r.subject,onChange:i,required:!0})]}),(0,Vt.jsxs)(wo,{children:[(0,Vt.jsx)(xo,{htmlFor:"message",children:"Message"}),(0,Vt.jsx)(So,{id:"message",name:"message",value:r.message,onChange:i,required:!0})]}),(0,Vt.jsxs)(Eo,{type:"submit",children:[(0,Vt.jsx)(Li,{size:18}),"Send Message"]})]})]})]})]})};var _o,zo;const Po=qn.div(_o||(_o=r(["\n  min-height: 100vh;\n  background: var(--bg-primary);\n  font-family: var(--font-secondary);\n  overflow-x: hidden;\n  position: relative;\n\n  /* Subtle background pattern for visual interest */\n  &::before {\n    content: '';\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background:\n      radial-gradient(circle at 25% 25%, var(--primary-50) 0%, transparent 50%),\n      radial-gradient(circle at 75% 75%, var(--secondary-50) 0%, transparent 50%);\n    pointer-events: none;\n    z-index: 0;\n  }\n"]))),jo=qn.div(zo||(zo=r(["\n  position: relative;\n  z-index: 1;\n  min-height: 100vh;\n"])));const To=function(){const[n,t]=(0,e.useState)("home"),r=()=>{t("training")},a=()=>{t("about")},i=()=>{t("contact")},o=()=>{t("home")};return(0,Vt.jsx)(Po,{children:(0,Vt.jsx)(jo,{children:(()=>{switch(n){case"training":return(0,Vt.jsx)(Xa,{onBackToHome:o});case"about":return(0,Vt.jsx)(Ti,{onBackToHome:o});case"contact":return(0,Vt.jsx)(Co,{onBackToHome:o});default:return(0,Vt.jsx)(xr,{onStartTraining:r,onNavigateToAbout:a,onNavigateToContact:i})}})()})})},Oo=e=>{e&&e instanceof Function&&t.e(453).then(t.bind(t,453)).then(n=>{let{getCLS:t,getFID:r,getFCP:a,getLCP:i,getTTFB:o}=n;t(e),r(e),a(e),i(e),o(e)})};n.createRoot(document.getElementById("root")).render((0,Vt.jsx)(e.StrictMode,{children:(0,Vt.jsx)(To,{})})),Oo()})()})();
//# sourceMappingURL=main.c7b06785.js.map