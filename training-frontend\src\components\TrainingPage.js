import { useState, useRef, useCallback, useEffect } from 'react';
import styled from 'styled-components';
import Webcam from 'react-webcam';
import {
  Brain,
  Camera,
  ArrowLeft,
  Play,
  Square,
  Download,
  Eye,
  Target,
  Wifi,
  WifiOff,
  RefreshCw,
  Zap,
  BarChart3,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { useSignDetection } from '../hooks/useSignDetection';

const TrainingContainer = styled.div`
  min-height: 100vh;
  background: var(--bg-primary);
  position: relative;
  overflow-x: hidden;

  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.03) 0%, transparent 50%);
    pointer-events: none;
    z-index: 0;
  }
`;

const Navigation = styled.nav`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-neural);
  padding: var(--space-4) 0;
  transition: var(--transition-normal);
`;

const NavContainer = styled.div`
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--space-6);
  display: flex;
  align-items: center;
  justify-content: space-between;

  @media (max-width: 768px) {
    padding: 0 var(--space-4);
  }
`;

const Logo = styled.div`
  font-family: var(--font-primary);
  font-size: 1.5rem;
  font-weight: 700;
  background: var(--text-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: flex;
  align-items: center;
  gap: var(--space-3);

  @media (max-width: 768px) {
    font-size: 1.25rem;
    gap: var(--space-2);
  }
`;

const LogoIcon = styled.div`
  width: 40px;
  height: 40px;
  background: var(--bg-neural);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: var(--shadow-neural);

  @media (max-width: 768px) {
    width: 36px;
    height: 36px;
  }
`;

const BackButton = styled.button`
  background: var(--bg-glass);
  color: var(--text-secondary);
  border: 1px solid var(--border-neural);
  padding: var(--space-3) var(--space-5);
  border-radius: var(--radius-xl);
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-normal);
  display: flex;
  align-items: center;
  gap: var(--space-2);
  backdrop-filter: blur(10px);

  &:hover {
    background: var(--primary-50);
    color: var(--primary-600);
    border-color: var(--primary-300);
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
  }

  @media (max-width: 768px) {
    padding: var(--space-2) var(--space-4);
    font-size: 0.85rem;
  }
`;

const PageTitle = styled.h1`
  font-family: var(--font-primary);
  font-size: 2.5rem;
  font-weight: 700;
  background: var(--text-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-align: center;
  margin-bottom: var(--space-4);
  letter-spacing: -0.02em;

  @media (max-width: 768px) {
    font-size: 2rem;
  }
`;

const PageSubtitle = styled.p`
  font-size: 1.125rem;
  color: var(--text-secondary);
  text-align: center;
  margin-bottom: var(--space-16);
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;

  @media (max-width: 768px) {
    margin-bottom: var(--space-12);
    font-size: 1rem;
  }
`;

const StatusBadge = styled.div`
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  background: var(--bg-glass);
  border: 1px solid var(--border-neural);
  border-radius: var(--radius-full);
  padding: var(--space-2) var(--space-4);
  margin-bottom: var(--space-8);
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-accent);
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-glow);

  @media (max-width: 768px) {
    font-size: 0.8rem;
    padding: var(--space-2) var(--space-3);
  }
`;

const MainContent = styled.main`
  padding: var(--space-20) var(--space-4) var(--space-16);
  max-width: 1200px;
  margin: 0 auto;

  @media (max-width: 768px) {
    padding: var(--space-12) var(--space-3) var(--space-8);
    max-width: 100%;
  }
`;

const TrainingGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-8);
  max-width: 1200px;
  margin: 0 auto var(--space-12);

  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  @media (max-width: 768px) {
    gap: var(--space-3);
    margin: 0 auto var(--space-6);
  }
`;

const CameraSection = styled.div`
  background: var(--bg-glass);
  border-radius: var(--radius-2xl);
  padding: var(--space-10);
  border: 1px solid var(--border-neural);
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-lg);
  transition: var(--transition-normal);
  position: relative;
  overflow: hidden;

  @media (max-width: 768px) {
    padding: var(--space-6);
    border-radius: var(--radius-xl);
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--bg-neural);
    transform: scaleX(0);
    transition: var(--transition-normal);
  }

  &:hover {
    box-shadow: var(--shadow-xl), var(--shadow-glow);
    border-color: var(--primary-300);

    &::before {
      transform: scaleX(1);
    }
  }

  @media (max-width: 768px) {
    padding: var(--space-8);
  }
`;

const SectionTitle = styled.h2`
  font-family: var(--font-primary);
  font-size: 1.25rem;
  margin-bottom: var(--space-6);
  color: var(--text-primary);
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: var(--space-3);

  @media (max-width: 768px) {
    font-size: 1.125rem;
    margin-bottom: var(--space-4);
  }
`;

const SectionIcon = styled.div`
  width: 36px;
  height: 36px;
  background: var(--bg-neural);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: var(--shadow-neural);

  @media (max-width: 768px) {
    width: 32px;
    height: 32px;
  }
`;

const WebcamContainer = styled.div`
  position: relative;
  border-radius: var(--radius-2xl);
  overflow: hidden;
  background: var(--neural-100);
  aspect-ratio: 4/3;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid var(--border-neural);
  margin-bottom: var(--space-6);
  box-shadow: var(--shadow-lg);

  @media (max-width: 768px) {
    aspect-ratio: 3/4;
    margin-bottom: var(--space-4);
    border-radius: var(--radius-xl);
    border-width: 2px;
  }
`;

const StyledWebcam = styled(Webcam)`
  width: 100%;
  height: 100%;
  object-fit: cover;
`;

const RecordingOverlay = styled.div`
  position: absolute;
  top: var(--space-4);
  right: var(--space-4);
  background: ${props => props.isRecording ?
    'var(--error-500)' :
    'var(--neural-600)'
  };
  color: white;
  padding: var(--space-3) var(--space-5);
  border-radius: var(--radius-full);
  font-size: 0.9rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: var(--space-2);
  box-shadow: var(--shadow-lg);
  animation: ${props => props.isRecording ? 'pulse 1.5s infinite' : 'none'};

  @keyframes pulse {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.8; transform: scale(1.05); }
  }
`;

const SignSection = styled.div`
  background: var(--bg-primary);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-lg);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-200);
  }

  @media (max-width: 768px) {
    padding: var(--space-6);
  }
`;

const SignSelector = styled.select`
  width: 100%;
  padding: var(--space-3) var(--space-4);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-lg);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: var(--space-4);
  cursor: pointer;
  transition: var(--transition-normal);

  &:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px var(--primary-100);
  }

  &:hover {
    border-color: var(--primary-300);
  }

  option {
    padding: var(--space-2);
    background: var(--bg-primary);
    color: var(--text-primary);
  }

  @media (max-width: 768px) {
    font-size: 1.125rem;
    padding: var(--space-4);
    margin-bottom: var(--space-3);
  }
`;

const SignDisplay = styled.div`
  width: 300px;
  height: 300px;
  background: var(--primary-50);
  border-radius: var(--radius-2xl);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--space-6);
  border: 2px solid var(--primary-200);
  transition: all 0.3s ease;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: var(--radius-xl);
  }

  &:hover {
    transform: scale(1.02);
    border-color: var(--primary-300);
  }

  @media (max-width: 768px) {
    width: 250px;
    height: 250px;
  }
`;

const SignName = styled.h3`
  font-family: var(--font-primary);
  font-size: 1.5rem;
  margin-bottom: var(--space-3);
  color: var(--text-primary);
  font-weight: 700;

  @media (max-width: 768px) {
    font-size: 1.25rem;
  }
`;

const SignDescription = styled.p`
  text-align: center;
  line-height: 1.6;
  color: var(--text-secondary);
  font-size: 0.9rem;
  font-weight: 400;
  max-width: 280px;
`;

const TopControlsSection = styled.div`
  display: flex;
  justify-content: center;
  gap: var(--space-4);
  margin-bottom: var(--space-8);
  padding: var(--space-4);
  background: var(--bg-glass);
  border-radius: var(--radius-xl);
  border: 1px solid var(--border-neural);
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-lg);

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: center;
    gap: var(--space-3);
    margin-bottom: var(--space-6);
    padding: var(--space-3);
  }
`;



const ControlButton = styled.button`
  background: ${props => props.variant === 'primary'
    ? 'var(--primary-600)'
    : props.variant === 'retry'
    ? 'var(--warning-500)'
    : 'var(--bg-primary)'};
  border: ${props => props.variant === 'primary' || props.variant === 'retry'
    ? 'none'
    : '1px solid var(--border-medium)'};
  color: ${props => props.variant === 'primary' || props.variant === 'retry'
    ? 'white'
    : 'var(--text-primary)'};
  padding: ${props => props.compact
    ? 'var(--space-2) var(--space-4)'
    : 'var(--space-3) var(--space-6)'};
  border-radius: var(--radius-lg);
  cursor: pointer;
  font-size: ${props => props.compact ? '0.8rem' : '0.9rem'};
  font-weight: 600;
  transition: all 0.2s ease;
  min-width: ${props => props.compact ? '120px' : '160px'};
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);

  @media (max-width: 768px) {
    padding: ${props => props.compact
      ? 'var(--space-3) var(--space-5)'
      : 'var(--space-4) var(--space-8)'};
    font-size: ${props => props.compact ? '0.9rem' : '1rem'};
    min-width: ${props => props.compact ? '140px' : '180px'};
    border-radius: var(--radius-xl);
  }
  box-shadow: ${props => props.variant === 'primary' || props.variant === 'retry'
    ? 'var(--shadow-lg)'
    : 'var(--shadow-sm)'};

  &:hover {
    transform: translateY(-2px);
    box-shadow: ${props => props.variant === 'primary' || props.variant === 'retry'
      ? 'var(--shadow-xl)'
      : 'var(--shadow-md)'};
    background: ${props => props.variant === 'primary'
      ? 'var(--primary-700)'
      : props.variant === 'retry'
      ? 'var(--warning-600)'
      : 'var(--gray-50)'};
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }

  @media (max-width: 768px) {
    width: 100%;
    max-width: ${props => props.compact ? '200px' : '280px'};
  }
`;

const StatusMessage = styled.div`
  text-align: center;
  margin-top: var(--space-6);
  padding: var(--space-4) var(--space-6);
  border-radius: var(--radius-lg);
  background: ${props =>
    props.type === 'success' ? 'var(--success-500)' :
    props.type === 'error' ? 'var(--error-500)' :
    'var(--primary-600)'
  };
  color: white;
  font-weight: 500;
  font-size: 0.875rem;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
`;

const RecordingsSection = styled.div`
  margin-top: var(--space-16);
  background: var(--bg-secondary);
  padding: var(--space-12) var(--space-4);
  border-radius: var(--radius-2xl);
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
`;

const RecordingsTitle = styled.h3`
  font-family: var(--font-primary);
  color: var(--text-primary);
  margin-bottom: var(--space-8);
  font-size: 1.5rem;
  font-weight: 600;
  text-align: center;
`;

const RecordingsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-6);

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }
`;

const RecordingCard = styled.div`
  background: var(--bg-primary);
  padding: var(--space-6);
  border-radius: var(--radius-xl);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    border-color: var(--primary-200);
    box-shadow: var(--shadow-lg);
  }
`;

const RecordingTitle = styled.p`
  margin: 0 0 var(--space-2) 0;
  color: var(--text-primary);
  font-weight: 600;
  font-size: 1rem;
  font-family: var(--font-primary);
`;

const RecordingTime = styled.p`
  margin: 0 0 var(--space-4) 0;
  font-size: 0.8rem;
  color: var(--text-tertiary);
`;

const DownloadButton = styled.button`
  background: var(--primary-600);
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--space-2) var(--space-4);
  color: white;
  cursor: pointer;
  font-size: 0.8rem;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin: 0 auto;

  &:hover {
    background: var(--primary-700);
    transform: translateY(-1px);
  }
`;

const PredictionDisplay = styled.div`
  background: var(--bg-glass);
  border: 2px solid ${props => {
    if (props.matched) return 'var(--success-400)';
    if (props.isStale) return 'var(--warning-300)';
    return 'var(--border-light)';
  }};
  border-radius: var(--radius-xl);
  padding: var(--space-4);
  margin-bottom: var(--space-4);
  text-align: center;
  transition: var(--transition-normal);
  backdrop-filter: blur(10px);
  opacity: ${props => props.isStale ? 0.7 : 1};
  min-height: 80px;
  display: flex;
  flex-direction: column;
  justify-content: center;

  ${props => props.matched && `
    background: var(--success-50);
    box-shadow: 0 0 20px var(--success-200);
    animation: pulse 1s ease-in-out;
  `}

  ${props => props.isStale && `
    background: var(--warning-50);
  `}

  @keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.02); }
  }

  @media (max-width: 768px) {
    padding: var(--space-3);
    margin-bottom: var(--space-3);
    min-height: 70px;
  }
`;

const PredictionText = styled.div`
  font-size: 1.25rem;
  font-weight: 600;
  color: ${props => {
    if (props.matched) return 'var(--success-700)';
    if (props.isStale) return 'var(--warning-700)';
    return 'var(--text-primary)';
  }};
  margin-bottom: var(--space-2);

  @media (max-width: 768px) {
    font-size: 1.125rem;
  }
`;

const ConfidenceBar = styled.div`
  width: 100%;
  height: 8px;
  background: var(--bg-secondary);
  border-radius: var(--radius-full);
  overflow: hidden;
  margin-top: var(--space-2);
`;

const ConfidenceFill = styled.div`
  height: 100%;
  background: ${props => {
    if (props.confidence > 0.8) return 'var(--success-500)';
    if (props.confidence > 0.6) return 'var(--warning-500)';
    return 'var(--error-500)';
  }};
  width: ${props => (props.confidence * 100)}%;
  transition: width 0.3s ease;
`;

const ConnectionStatus = styled.div`
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-lg);
  font-size: 0.875rem;
  font-weight: 500;
  background: ${props => props.connected ? 'var(--success-50)' : 'var(--error-50)'};
  color: ${props => props.connected ? 'var(--success-700)' : 'var(--error-700)'};
  border: 1px solid ${props => props.connected ? 'var(--success-200)' : 'var(--error-200)'};
`;

// Sign language data with GIFs (matching Streamlit app)
const signLanguageData = {
  "after": {
    name: "After",
    gif: "https://lifeprint.com/asl101/gifs/a/after-over-across.gif",
    description: "Move your dominant hand over and past your non-dominant hand"
  },
  "airplane": {
    name: "Airplane",
    gif: "https://www.lifeprint.com/asl101/gifs/a/airplane-flying.gif",
    description: "Extend your hand like a plane and move it through the air"
  },
  "all": {
    name: "All",
    gif: "https://lifeprint.com/asl101/gifs/a/all-whole.gif",
    description: "Circle your dominant hand around your non-dominant hand"
  },
  "alligator": {
    name: "Alligator",
    gif: "https://lifeprint.com/asl101/gifs/a/alligator.gif",
    description: "Clap your hands together like an alligator's mouth"
  },
  "animal": {
    name: "Animal",
    gif: "https://www.lifeprint.com/asl101/gifs-animated/animal.gif",
    description: "Place fingertips on chest and move hands back and forth"
  },
  "any": {
    name: "Any",
    gif: "https://lifeprint.com/asl101/gifs/a/any.gif",
    description: "Point with index finger and twist your wrist"
  },
  "apple": {
    name: "Apple",
    gif: "https://media.giphy.com/media/l0HlHb4dtZZiMYEA8/giphy.gif",
    description: "Twist your knuckle against your cheek"
  },
  "arm": {
    name: "Arm",
    gif: "https://th.bing.com/th/id/OIP.KkJLqfbp6XEHiIe-jOUb2AHaEc?w=251&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7",
    description: "Pat your arm with your opposite hand"
  },
  "aunt": {
    name: "Aunt",
    gif: "https://th.bing.com/th/id/OIP.Yz5UUZdNTrVWXf72we_N6wHaHa?rs=1&pid=ImgDetMain",
    description: "Make an 'A' handshape near your cheek and shake it"
  },
  "baby": {
    name: "Baby",
    gif: "https://media.giphy.com/media/l0HlHb4dtZZiMYEA8/giphy.gif",
    description: "Rock your arms as if holding a baby"
  },
  "ball": {
    name: "Ball",
    gif: "https://lifeprint.com/asl101/gifs/b/ball.gif",
    description: "Cup your hands as if holding a ball"
  },
  "banana": {
    name: "Banana",
    gif: "https://media.giphy.com/media/l0HlHb4dtZZiMYEA8/giphy.gif",
    description: "Peel an imaginary banana with your fingers"
  },
  "bear": {
    name: "Bear",
    gif: "https://lifeprint.com/asl101/gifs/b/bear.gif",
    description: "Cross your arms and scratch like a bear"
  },
  "beautiful": {
    name: "Beautiful",
    gif: "https://media.giphy.com/media/l0HlHb4dtZZiMYEA8/giphy.gif",
    description: "Circle your face with your hand and close it into a fist"
  },
  "bed": {
    name: "Bed",
    gif: "https://lifeprint.com/asl101/gifs/b/bed.gif",
    description: "Rest your head on your hands as if sleeping"
  },
  "bee": {
    name: "Bee",
    gif: "https://lifeprint.com/asl101/gifs/b/bee.gif",
    description: "Pinch your cheek and brush away as if swatting a bee"
  },
  "bird": {
    name: "Bird",
    gif: "https://lifeprint.com/asl101/gifs/b/bird.gif",
    description: "Pinch your fingers together near your mouth like a beak"
  },
  "black": {
    name: "Black",
    gif: "https://lifeprint.com/asl101/gifs/b/black.gif",
    description: "Draw your index finger across your forehead"
  },
  "blue": {
    name: "Blue",
    gif: "https://lifeprint.com/asl101/gifs/b/blue.gif",
    description: "Shake a 'B' handshape"
  },
  "book": {
    name: "Book",
    gif: "https://lifeprint.com/asl101/gifs/b/book.gif",
    description: "Open your hands like opening a book"
  },
  "boy": {
    name: "Boy",
    gif: "https://lifeprint.com/asl101/gifs/b/boy.gif",
    description: "Snap your fingers at your forehead"
  },
  "brother": {
    name: "Brother",
    gif: "https://lifeprint.com/asl101/gifs/b/brother.gif",
    description: "Make an 'L' shape and point to your forehead, then point forward"
  },
  "brown": {
    name: "Brown",
    gif: "https://lifeprint.com/asl101/gifs/b/brown.gif",
    description: "Slide your index finger down your cheek"
  },
  "bug": {
    name: "Bug",
    gif: "https://lifeprint.com/asl101/gifs/b/bug.gif",
    description: "Pinch your nose with your thumb and index finger"
  },
  "butterfly": {
    name: "Butterfly",
    gif: "https://lifeprint.com/asl101/gifs/b/butterfly.gif",
    description: "Cross your thumbs and flutter your fingers like wings"
  },
  "car": {
    name: "Car",
    gif: "https://lifeprint.com/asl101/gifs/c/car.gif",
    description: "Pretend to steer a car with both hands"
  },
  "cat": {
    name: "Cat",
    gif: "https://lifeprint.com/asl101/gifs/c/cat.gif",
    description: "Pinch your cheek and pull out like whiskers"
  },
  "chair": {
    name: "Chair",
    gif: "https://lifeprint.com/asl101/gifs/c/chair.gif",
    description: "Tap your fingers on your other hand like sitting"
  },
  "clean": {
    name: "Clean",
    gif: "https://lifeprint.com/asl101/gifs/c/clean.gif",
    description: "Wipe one palm with the other"
  },
  "cold": {
    name: "Cold",
    gif: "https://media.giphy.com/media/l0HlHb4dtZZiMYEA8/giphy.gif",
    description: "Shiver with both hands in fists"
  },
  "cow": {
    name: "Cow",
    gif: "https://lifeprint.com/asl101/gifs/c/cow.gif",
    description: "Twist your thumb at your temple like a horn"
  },
  "cry": {
    name: "Cry",
    gif: "https://lifeprint.com/asl101/gifs/c/cry.gif",
    description: "Draw tears down your cheeks with your index fingers"
  },
  "cute": {
    name: "Cute",
    gif: "https://lifeprint.com/asl101/gifs/c/cute-sugar.gif",
    description: "Brush your chin with your fingers"
  },
  "dad": {
    name: "Dad",
    gif: "https://media.giphy.com/media/l0MYQcLKwtl5v6H1S/giphy.gif",
    description: "Tap your forehead with your thumb"
  },
  "dance": {
    name: "Dance",
    gif: "https://www.bing.com/th/id/OGC.********************************?pid=1.7&rurl=https%3a%2f%2fmedia.giphy.com%2fmedia%2f3o7TKMspYQjQTbOz2U%2fgiphy.gif&ehk=h%2bdBHCxuoOT89ovSy5uTk6MCL9acaBEV6ld9lrVDRF4%3d",
    description: "Swing your fingers over your palm like dancing"
  },
  "dirty": {
    name: "Dirty",
    gif: "https://th.bing.com/th/id/OIP.wRA7r1OPPUuEoLL4Hds9jAHaHa?rs=1&pid=ImgDetMain",
    description: "Wiggle your fingers under your chin"
  },
  "dog": {
    name: "Dog",
    gif: "https://th.bing.com/th/id/R.********************************?rik=uVshGsOHXgldoQ&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fd%2fdog1.gif&ehk=Xnfrvg0xwy1u%2fae9vWdKYMT25%2bF6qoteW2FThCbVrOA%3d&risl=&pid=ImgRaw&r=0",
    description: "Pat your leg and snap your fingers"
  },
  "eat": {
    name: "Eat",
    gif: "https://lifeprint.com/asl101/gifs/e/eat.gif",
    description: "Bring your fingers to your mouth as if eating"
  },
  "elephant": {
    name: "Elephant",
    gif: "https://lifeprint.com/asl101/gifs/e/elephant.gif",
    description: "Trace the shape of an elephant's trunk with your hand"
  },
  "fish": {
    name: "Fish",
    gif: "https://lifeprint.com/asl101/gifs/f/fish.gif",
    description: "Move your hand like a fish swimming"
  },
  "flower": {
    name: "Flower",
    gif: "https://lifeprint.com/asl101/gifs/f/flower.gif",
    description: "Touch your nose with your fingertips"
  },
  "friend": {
    name: "Friend",
    gif: "https://lifeprint.com/asl101/gifs/f/friend.gif",
    description: "Hook your index fingers together"
  },
  "girl": {
    name: "Girl",
    gif: "https://lifeprint.com/asl101/gifs/g/girl.gif",
    description: "Trace your jawline with your thumb"
  },
  "go": {
    name: "Go",
    gif: "https://lifeprint.com/asl101/gifs/g/go.gif",
    description: "Point both index fingers forward and bend them"
  },
  "good": {
    name: "Good",
    gif: "https://lifeprint.com/asl101/gifs/g/good.gif",
    description: "Touch your chin and move your hand forward"
  },
  "green": {
    name: "Green",
    gif: "https://lifeprint.com/asl101/gifs/g/green.gif",
    description: "Shake a 'G' handshape"
  },
  "hair": {
    name: "Hair",
    gif: "https://www.lifeprint.com/asl101/gifs/h/hair-g-version.gif",
    description: "Pinch a strand of your hair"
  },
  "happy": {
    name: "Happy",
    gif: "https://media0.giphy.com/media/3o7TKFpahYpUp4g0N2/giphy.gif?cid=790b7611bca188a92e2e759876756ef62ba95b7cdd337c77&rid=giphy.gif&ct=g",
    description: "Brush your chest upward with both hands"
  },
  "hello": {
    name: "Hello",
    gif: "https://media.giphy.com/media/3o7TKNKOfKlIhbD3gY/giphy.gif",
    description: "Wave your hand from side to side with palm facing forward"
  },
  "home": {
    name: "Home",
    gif: "https://th.bing.com/th/id/R.********************************?rik=%2bnBd%2foQjxnoPfg&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fh%2fhome-2.gif&ehk=7yD%2f%2fh6JN1Y4D4BOrUjgKW4Jccy2Y4GVYLf%2fzyk%2b5YY%3d&risl=&pid=ImgRaw&r=0",
    description: "Touch your mouth then your cheek"
  },
  "horse": {
    name: "Horse",
    gif: "https://media.giphy.com/media/l0HlM5HffraiQaHUk/giphy.gif",
    description: "Extend your thumb and fingers at your temple like ears"
  },
  "hot": {
    name: "Hot",
    gif: "https://media.giphy.com/media/3o6Zt99k5aDok347bG/giphy.gif",
    description: "Touch your mouth and quickly move your hand away"
  },
  "hungry": {
    name: "Hungry",
    gif: "https://media.giphy.com/media/l3vR0xkdFEz4tnfTq/giphy.gif",
    description: "Move your hand down your chest like food going down"
  },
  "jump": {
    name: "Jump",
    gif: "https://lifeprint.com/asl101/gifs-animated/jump.gif",
    description: "Bounce your fingers on your palm"
  },
  "like": {
    name: "Like",
    gif: "https://lifeprint.com/asl101/gifs/l/like.gif",
    description: "Pull your thumb and middle finger from your chest"
  },
  "look": {
    name: "Look",
    gif: "https://th.bing.com/th/id/R.********************************?rik=pYhzip7LqNs7qw&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fl%2flook-at-1.gif&ehk=rFJ7dBrMGFDK0nHLzrOPAzROVE7yqyDEcb%2btLqKqYOI%3d&risl=&pid=ImgRaw&r=0",
    description: "Point your fingers from your eyes forward"
  },
  "love": {
    name: "Love",
    gif: "https://lifeprint.com/asl101/gifs/l/love.gif",
    description: "Cross your arms over your chest"
  },
  "mom": {
    name: "Mom",
    gif: "https://media.giphy.com/media/l0MYQcLKwtl5v6H1S/giphy.gif",
    description: "Tap your chin with your thumb"
  },
  "more": {
    name: "More",
    gif: "https://lifeprint.com/asl101/gifs/m/more.gif",
    description: "Tap your fingertips together"
  },
  "no": {
    name: "No",
    gif: "https://lifeprint.com/asl101/gifs/n/no.gif",
    description: "Snap your fingers together"
  },
  "orange": {
    name: "Orange",
    gif: "https://lifeprint.com/asl101/gifs/o/orange.gif",
    description: "Squeeze your hand at your mouth like squeezing an orange"
  },
  "please": {
    name: "Please",
    gif: "https://lifeprint.com/asl101/gifs/p/please.gif",
    description: "Rub your chest in a circular motion"
  },
  "red": {
    name: "Red",
    gif: "https://lifeprint.com/asl101/gifs/r/red.gif",
    description: "Brush your lips with your index finger"
  },
  "run": {
    name: "Run",
    gif: "https://lifeprint.com/asl101/gifs/r/run.gif",
    description: "Hook your thumbs and wiggle your fingers"
  },
  "sad": {
    name: "Sad",
    gif: "https://lifeprint.com/asl101/gifs/s/sad.gif",
    description: "Drop both hands down your face"
  },
  "see": {
    name: "See",
    gif: "https://lifeprint.com/asl101/gifs/l/look-at-2.gif",
    description: "Point from your eyes forward"
  },
  "sleep": {
    name: "Sleep",
    gif: "https://media4.giphy.com/media/3o7TKnRuBdakLslcaI/200.gif?cid=790b76110d8f185a9713f36dd65a0df801576e01b403c95c&rid=200.gif&ct=g",
    description: "Rest your head on your hands"
  },
  "sorry": {
    name: "Sorry",
    gif: "https://lifeprint.com/asl101/gifs/s/sorry.gif",
    description: "Rub your fist in a circle on your chest"
  },
  "thank": {
    name: "Thank You",
    gif: "https://lifeprint.com/asl101/gifs/t/thank-you.gif",
    description: "Touch your chin and move your hand forward"
  },
  "water": {
    name: "Water",
    gif: "https://lifeprint.com/asl101/gifs/w/water.gif",
    description: "Tap your chin with a 'W' handshape"
  },
  "white": {
    name: "White",
    gif: "https://lifeprint.com/asl101/gifs/w/white.gif",
    description: "Touch your chest and pull your hand away"
  },
  "yellow": {
    name: "Yellow",
    gif: "https://lifeprint.com/asl101/gifs/y/yellow.gif",
    description: "Shake a 'Y' handshape"
  },
  "yes": {
    name: "Yes",
    gif: "https://media.tenor.com/oYIirlyIih0AAAAC/yes-asl.gif",
    description: "Nod your fist up and down"
  }
};

const TrainingPage = ({ onBackToHome }) => {
  const [currentSign, setCurrentSign] = useState('hello');
  const [status, setStatus] = useState('');
  const [isCapturing, setIsCapturing] = useState(false);
  // Auto-recording is always on, no need for mode toggle
  // eslint-disable-next-line no-unused-vars
  const [recordedVideos, setRecordedVideos] = useState([]);

  const webcamRef = useRef(null);
  const autoRecordTimeoutRef = useRef(null);
  const matchCountRef = useRef(0);

  // Use sign detection hook
  const {
    isConnected,
    prediction,
    isAIRecording,
    recordingStatus,
    signMatched,
    targetSign,
    startRecording: startAIRecording,
    stopRecording: stopAIRecording,
    startFrameCapture,
    retryConnection
  } = useSignDetection();

  const handleSignChange = useCallback((event) => {
    setCurrentSign(event.target.value);
  }, []);

  const startDetection = useCallback(() => {
    if (!webcamRef.current) {
      setStatus('Camera not available');
      return;
    }

    setIsCapturing(true);
    startFrameCapture(webcamRef, 100); // Send frame every 100ms
    setStatus('AI detection started');
  }, [startFrameCapture]);

  const startManualRecording = useCallback(() => {
    if (!isConnected) {
      setStatus('AI backend not connected');
      return;
    }

    if (!webcamRef.current) {
      setStatus('Camera not available');
      return;
    }

    if (isAIRecording) {
      setStatus('Already recording...');
      return;
    }

    // Start manual 3-second recording
    setStatus(`Manual recording ${signLanguageData[currentSign].name} for 3 seconds...`);
    startAIRecording(signLanguageData[currentSign].name);

    // Auto-stop after 3 seconds
    autoRecordTimeoutRef.current = setTimeout(() => {
      stopAIRecording();
      setStatus(`Manual recording complete! ${signLanguageData[currentSign].name} saved to recordings folder`);
    }, 3000);

    // Also start frame capture if not already started
    if (!isCapturing) {
      startDetection();
    }
  }, [currentSign, isConnected, isCapturing, startDetection, isAIRecording, startAIRecording, stopAIRecording]);

  const stopManualRecording = useCallback(() => {
    // Stop current recording
    if (isAIRecording) {
      stopAIRecording();
    }
    matchCountRef.current = 0;
    if (autoRecordTimeoutRef.current) {
      clearTimeout(autoRecordTimeoutRef.current);
    }
    setStatus('Manual recording stopped');
  }, [stopAIRecording, isAIRecording]);

  const downloadRecording = (video) => {
    const a = document.createElement('a');
    a.href = video.url;
    a.download = `sign_${video.sign}_${video.timestamp}.webm`;
    a.click();
  };

  // Auto-start detection when connected
  useEffect(() => {
    if (isConnected && webcamRef.current && !isCapturing) {
      startDetection();
    }
  }, [isConnected, startDetection, isCapturing]);

  // Always-on auto-recording logic - records when confidence >= 50%
  useEffect(() => {
    if (!prediction || !isConnected) {
      matchCountRef.current = 0;
      return;
    }

    const predictedSign = prediction.sign.toLowerCase();
    const targetSignLower = signLanguageData[currentSign].name.toLowerCase();
    const confidence = prediction.confidence;

    // Auto-record when sign matches with >= 50% confidence
    if (predictedSign === targetSignLower && confidence >= 0.5) {
      matchCountRef.current += 1;

      // Start recording after 2 consecutive matches to avoid false positives
      if (matchCountRef.current >= 2 && !isAIRecording) {
        setStatus(`Auto-recording ${signLanguageData[currentSign].name}... (${Math.round(confidence * 100)}% confidence)`);
        startAIRecording(signLanguageData[currentSign].name);

        // Auto-stop recording after 3 seconds
        autoRecordTimeoutRef.current = setTimeout(() => {
          stopAIRecording();
          setStatus(`Auto-recording complete! ${signLanguageData[currentSign].name} saved to recordings folder`);
          matchCountRef.current = 0;
        }, 3000);
      }
    } else {
      // Reset match count if sign doesn't match or confidence is too low
      matchCountRef.current = 0;
    }

    return () => {
      if (autoRecordTimeoutRef.current) {
        clearTimeout(autoRecordTimeoutRef.current);
      }
    };
  }, [prediction, currentSign, isAIRecording, startAIRecording, stopAIRecording, isConnected]);

  return (
    <TrainingContainer>
      <Navigation>
        <NavContainer>
          <Logo>
            <LogoIcon>
              <Brain size={24} />
            </LogoIcon>
            ASL Neural
          </Logo>
          <BackButton onClick={onBackToHome}>
            <ArrowLeft size={18} />
            Back to Home
          </BackButton>
        </NavContainer>
      </Navigation>

      <MainContent>
        <div style={{ textAlign: 'center', marginBottom: 'var(--space-12)' }}>
          <StatusBadge>
            <Eye size={16} />
            Neural Vision Active
          </StatusBadge>
        </div>

        <PageTitle>AI Training Session</PageTitle>
        <PageSubtitle>
          Experience real-time neural network analysis as our AI learns from your sign language practice
        </PageSubtitle>

        <TopControlsSection>
          <ControlButton
            variant="primary"
            compact
            onClick={isAIRecording ? stopManualRecording : startManualRecording}
          >
            {isAIRecording ? (
              <>
                <Square size={16} />
                Stop Recording
              </>
            ) : (
              <>
                <Play size={16} />
                Record 3 Seconds
              </>
            )}
          </ControlButton>

          {!isConnected && (
            <ControlButton
              variant="retry"
              compact
              onClick={retryConnection}
            >
              <RefreshCw size={16} />
              Retry Connection
            </ControlButton>
          )}
        </TopControlsSection>

        <TrainingGrid>
          <CameraSection>
            <SectionTitle>
              <SectionIcon>
                <Camera size={24} />
              </SectionIcon>
              Neural Vision Feed
            </SectionTitle>

            <ConnectionStatus connected={isConnected}>
              {isConnected ? <Wifi size={16} /> : <WifiOff size={16} />}
              {isConnected ? 'AI Connected' : 'AI Disconnected'}
            </ConnectionStatus>

            {prediction && (
              <PredictionDisplay matched={signMatched} isStale={prediction.isStale}>
                <PredictionText matched={signMatched} isStale={prediction.isStale}>
                  Detected: {prediction.sign}
                  {prediction.isStale && ' (previous)'}
                </PredictionText>
                <ConfidenceBar>
                  <ConfidenceFill confidence={prediction.confidence} />
                </ConfidenceBar>
                <div style={{ fontSize: '0.875rem', marginTop: '8px', color: 'var(--text-secondary)' }}>
                  Confidence: {Math.round(prediction.confidence * 100)}%
                  {signMatched && targetSign && (
                    <span style={{ color: 'var(--success-600)', marginLeft: '8px' }}>
                      ✓ Match! Recording...
                    </span>
                  )}
                  {!isAIRecording && (
                    <div style={{ color: 'var(--primary-600)', marginTop: '4px' }}>
                      🎯 Auto-recording active: Perform "{signLanguageData[currentSign].name}" sign (≥50% confidence)
                    </div>
                  )}
                </div>
              </PredictionDisplay>
            )}

            {!prediction && (
              <PredictionDisplay>
                <PredictionText>
                  🎯 Ready to detect "{signLanguageData[currentSign].name}"
                </PredictionText>
                <div style={{ fontSize: '0.875rem', color: 'var(--text-secondary)' }}>
                  Auto-recording is active. Perform the sign with ≥50% confidence to trigger recording.
                </div>
              </PredictionDisplay>
            )}
            <WebcamContainer>
              <StyledWebcam
                ref={webcamRef}
                audio={false}
                screenshotFormat="image/jpeg"
                videoConstraints={{
                  width: 640,
                  height: 480,
                  facingMode: "user"
                }}
              />
              <RecordingOverlay isRecording={isAIRecording}>
                {isAIRecording ? (
                  <>
                    <div style={{
                      width: '8px',
                      height: '8px',
                      borderRadius: '50%',
                      backgroundColor: 'white',
                      marginRight: '4px'
                    }} />
                    Recording
                  </>
                ) : (
                  <>
                    <Eye size={16} />
                    Ready
                  </>
                )}
              </RecordingOverlay>
            </WebcamContainer>
          </CameraSection>

          <SignSection>
            <SectionTitle>
              <SectionIcon>
                <Target size={24} />
              </SectionIcon>
              Select a Sign
            </SectionTitle>
            <SignSelector
              value={currentSign}
              onChange={handleSignChange}
              disabled={isAIRecording}
            >
              {Object.keys(signLanguageData).map(signKey => (
                <option key={signKey} value={signKey}>
                  {signLanguageData[signKey].name}
                </option>
              ))}
            </SignSelector>
            <SignDisplay>
              <img
                src={signLanguageData[currentSign].gif}
                alt={signLanguageData[currentSign].name}
                onError={(e) => {
                  e.target.style.display = 'none';
                  e.target.nextSibling.style.display = 'flex';
                }}
              />
              <div style={{display: 'none', fontSize: '3rem'}}>
                📷
              </div>
            </SignDisplay>
            <SignName>{signLanguageData[currentSign].name}</SignName>
            <SignDescription>
              {signLanguageData[currentSign].description}
            </SignDescription>
          </SignSection>
        </TrainingGrid>



        {(status || recordingStatus) && (
          <StatusMessage type={(status || recordingStatus).includes('error') ? 'error' : (status || recordingStatus).includes('success') ? 'success' : 'info'}>
            {recordingStatus || status}
          </StatusMessage>
        )}

        {recordedVideos.length > 0 && (
          <RecordingsSection>
            <RecordingsTitle>Your Practice Recordings</RecordingsTitle>
            <RecordingsGrid>
              {recordedVideos.map((video) => (
                <RecordingCard key={video.id}>
                  <RecordingTitle>{video.sign}</RecordingTitle>
                  <RecordingTime>
                    {new Date(video.timestamp).toLocaleString()}
                  </RecordingTime>
                  <DownloadButton onClick={() => downloadRecording(video)}>
                    <Download size={16} />
                    Download
                  </DownloadButton>
                </RecordingCard>
              ))}
            </RecordingsGrid>
          </RecordingsSection>
        )}
      </MainContent>
    </TrainingContainer>
  );
};

export default TrainingPage; 