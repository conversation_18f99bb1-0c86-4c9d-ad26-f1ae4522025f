{"ast": null, "code": "// 100 real ASL signs from the model\nconst signLanguageData = {\n  \"TV\": {\n    name: \"TV\",\n    gif: \"https://lifeprint.com/asl101/gifs/t/tv.gif\",\n    description: \"Sign for TV\"\n  },\n  \"alligator\": {\n    name: \"alligator\",\n    gif: \"https://lifeprint.com/asl101/gifs/a/alligator.gif\",\n    description: \"Sign for alligator\"\n  },\n  \"another\": {\n    name: \"another\",\n    gif: \"https://lifeprint.com/asl101/gifs/a/another.gif\",\n    description: \"Sign for another\"\n  },\n  \"apple\": {\n    name: \"apple\",\n    gif: \"https://lifeprint.com/asl101/gifs/a/apple.gif\",\n    description: \"Sign for apple\"\n  },\n  \"backyard\": {\n    name: \"backyard\",\n    gif: \"https://lifeprint.com/asl101/gifs/b/backyard.gif\",\n    description: \"Sign for backyard\"\n  },\n  \"bad\": {\n    name: \"bad\",\n    gif: \"https://lifeprint.com/asl101/gifs/b/bad.gif\",\n    description: \"Sign for bad\"\n  },\n  \"balloon\": {\n    name: \"balloon\",\n    gif: \"https://lifeprint.com/asl101/gifs/b/balloon.gif\",\n    description: \"Sign for balloon\"\n  },\n  \"bee\": {\n    name: \"bee\",\n    gif: \"https://lifeprint.com/asl101/gifs/b/bee.gif\",\n    description: \"Sign for bee\"\n  },\n  \"bird\": {\n    name: \"bird\",\n    gif: \"https://lifeprint.com/asl101/gifs/b/bird.gif\",\n    description: \"Sign for bird\"\n  },\n  \"black\": {\n    name: \"black\",\n    gif: \"https://lifeprint.com/asl101/gifs/b/black.gif\",\n    description: \"Sign for black\"\n  },\n  \"blow\": {\n    name: \"blow\",\n    gif: \"https://lifeprint.com/asl101/gifs/b/blow.gif\",\n    description: \"Sign for blow\"\n  },\n  \"brother\": {\n    name: \"brother\",\n    gif: \"https://lifeprint.com/asl101/gifs/b/brother.gif\",\n    description: \"Sign for brother\"\n  },\n  \"brown\": {\n    name: \"brown\",\n    gif: \"https://lifeprint.com/asl101/gifs/b/brown.gif\",\n    description: \"Sign for brown\"\n  },\n  \"bug\": {\n    name: \"bug\",\n    gif: \"https://lifeprint.com/asl101/gifs/b/bug.gif\",\n    description: \"Sign for bug\"\n  },\n  \"callonphone\": {\n    name: \"callonphone\",\n    gif: \"https://lifeprint.com/asl101/gifs/c/callonphone.gif\",\n    description: \"Sign for callonphone\"\n  },\n  \"can\": {\n    name: \"can\",\n    gif: \"https://lifeprint.com/asl101/gifs/c/can.gif\",\n    description: \"Sign for can\"\n  },\n  \"cheek\": {\n    name: \"cheek\",\n    gif: \"https://lifeprint.com/asl101/gifs/c/cheek.gif\",\n    description: \"Sign for cheek\"\n  },\n  \"clean\": {\n    name: \"clean\",\n    gif: \"https://lifeprint.com/asl101/gifs/c/clean.gif\",\n    description: \"Sign for clean\"\n  },\n  \"closet\": {\n    name: \"closet\",\n    gif: \"https://lifeprint.com/asl101/gifs/c/closet.gif\",\n    description: \"Sign for closet\"\n  },\n  \"cloud\": {\n    name: \"cloud\",\n    gif: \"https://lifeprint.com/asl101/gifs/c/cloud.gif\",\n    description: \"Sign for cloud\"\n  },\n  \"clown\": {\n    name: \"clown\",\n    gif: \"https://lifeprint.com/asl101/gifs/c/clown.gif\",\n    description: \"Sign for clown\"\n  },\n  \"cowboy\": {\n    name: \"cowboy\",\n    gif: \"https://lifeprint.com/asl101/gifs/c/cowboy.gif\",\n    description: \"Sign for cowboy\"\n  },\n  \"cry\": {\n    name: \"cry\",\n    gif: \"https://lifeprint.com/asl101/gifs/c/cry.gif\",\n    description: \"Sign for cry\"\n  },\n  \"cute\": {\n    name: \"cute\",\n    gif: \"https://lifeprint.com/asl101/gifs/c/cute.gif\",\n    description: \"Sign for cute\"\n  },\n  \"drawer\": {\n    name: \"drawer\",\n    gif: \"https://lifeprint.com/asl101/gifs/d/drawer.gif\",\n    description: \"Sign for drawer\"\n  },\n  \"dry\": {\n    name: \"dry\",\n    gif: \"https://lifeprint.com/asl101/gifs/d/dry.gif\",\n    description: \"Sign for dry\"\n  },\n  \"dryer\": {\n    name: \"dryer\",\n    gif: \"https://lifeprint.com/asl101/gifs/d/dryer.gif\",\n    description: \"Sign for dryer\"\n  },\n  \"duck\": {\n    name: \"duck\",\n    gif: \"https://lifeprint.com/asl101/gifs/d/duck.gif\",\n    description: \"Sign for duck\"\n  },\n  \"empty\": {\n    name: \"empty\",\n    gif: \"https://lifeprint.com/asl101/gifs/e/empty.gif\",\n    description: \"Sign for empty\"\n  },\n  \"face\": {\n    name: \"face\",\n    gif: \"https://lifeprint.com/asl101/gifs/f/face.gif\",\n    description: \"Sign for face\"\n  },\n  \"fall\": {\n    name: \"fall\",\n    gif: \"https://lifeprint.com/asl101/gifs/f/fall.gif\",\n    description: \"Sign for fall\"\n  },\n  \"fast\": {\n    name: \"fast\",\n    gif: \"https://lifeprint.com/asl101/gifs/f/fast.gif\",\n    description: \"Sign for fast\"\n  },\n  \"feet\": {\n    name: \"feet\",\n    gif: \"https://lifeprint.com/asl101/gifs/f/feet.gif\",\n    description: \"Sign for feet\"\n  },\n  \"find\": {\n    name: \"find\",\n    gif: \"https://lifeprint.com/asl101/gifs/f/find.gif\",\n    description: \"Sign for find\"\n  },\n  \"finger\": {\n    name: \"finger\",\n    gif: \"https://lifeprint.com/asl101/gifs/f/finger.gif\",\n    description: \"Sign for finger\"\n  },\n  \"finish\": {\n    name: \"finish\",\n    gif: \"https://lifeprint.com/asl101/gifs/f/finish.gif\",\n    description: \"Sign for finish\"\n  },\n  \"first\": {\n    name: \"first\",\n    gif: \"https://lifeprint.com/asl101/gifs/f/first.gif\",\n    description: \"Sign for first\"\n  },\n  \"fish\": {\n    name: \"fish\",\n    gif: \"https://lifeprint.com/asl101/gifs/f/fish.gif\",\n    description: \"Sign for fish\"\n  },\n  \"flower\": {\n    name: \"flower\",\n    gif: \"https://lifeprint.com/asl101/gifs/f/flower.gif\",\n    description: \"Sign for flower\"\n  },\n  \"frenchfries\": {\n    name: \"frenchfries\",\n    gif: \"https://lifeprint.com/asl101/gifs/f/frenchfries.gif\",\n    description: \"Sign for frenchfries\"\n  },\n  \"gift\": {\n    name: \"gift\",\n    gif: \"https://lifeprint.com/asl101/gifs/g/gift.gif\",\n    description: \"Sign for gift\"\n  },\n  \"giraffe\": {\n    name: \"giraffe\",\n    gif: \"https://lifeprint.com/asl101/gifs/g/giraffe.gif\",\n    description: \"Sign for giraffe\"\n  },\n  \"go\": {\n    name: \"go\",\n    gif: \"https://lifeprint.com/asl101/gifs/g/go.gif\",\n    description: \"Sign for go\"\n  },\n  \"grass\": {\n    name: \"grass\",\n    gif: \"https://lifeprint.com/asl101/gifs/g/grass.gif\",\n    description: \"Sign for grass\"\n  },\n  \"green\": {\n    name: \"green\",\n    gif: \"https://lifeprint.com/asl101/gifs/g/green.gif\",\n    description: \"Sign for green\"\n  },\n  \"hair\": {\n    name: \"hair\",\n    gif: \"https://lifeprint.com/asl101/gifs/h/hair.gif\",\n    description: \"Sign for hair\"\n  },\n  \"have\": {\n    name: \"have\",\n    gif: \"https://lifeprint.com/asl101/gifs/h/have.gif\",\n    description: \"Sign for have\"\n  },\n  \"hungry\": {\n    name: \"hungry\",\n    gif: \"https://lifeprint.com/asl101/gifs/h/hungry.gif\",\n    description: \"Sign for hungry\"\n  },\n  \"jacket\": {\n    name: \"jacket\",\n    gif: \"https://lifeprint.com/asl101/gifs/j/jacket.gif\",\n    description: \"Sign for jacket\"\n  },\n  \"jump\": {\n    name: \"jump\",\n    gif: \"https://lifeprint.com/asl101/gifs/j/jump.gif\",\n    description: \"Sign for jump\"\n  },\n  \"lips\": {\n    name: \"lips\",\n    gif: \"https://lifeprint.com/asl101/gifs/l/lips.gif\",\n    description: \"Sign for lips\"\n  },\n  \"look\": {\n    name: \"look\",\n    gif: \"https://lifeprint.com/asl101/gifs/l/look.gif\",\n    description: \"Sign for look\"\n  },\n  \"mad\": {\n    name: \"mad\",\n    gif: \"https://lifeprint.com/asl101/gifs/m/mad.gif\",\n    description: \"Sign for mad\"\n  },\n  \"man\": {\n    name: \"man\",\n    gif: \"https://lifeprint.com/asl101/gifs/m/man.gif\",\n    description: \"Sign for man\"\n  },\n  \"milk\": {\n    name: \"milk\",\n    gif: \"https://lifeprint.com/asl101/gifs/m/milk.gif\",\n    description: \"Sign for milk\"\n  },\n  \"minemy\": {\n    name: \"minemy\",\n    gif: \"https://lifeprint.com/asl101/gifs/m/minemy.gif\",\n    description: \"Sign for minemy\"\n  },\n  \"mitten\": {\n    name: \"mitten\",\n    gif: \"https://lifeprint.com/asl101/gifs/m/mitten.gif\",\n    description: \"Sign for mitten\"\n  },\n  \"moon\": {\n    name: \"moon\",\n    gif: \"https://lifeprint.com/asl101/gifs/m/moon.gif\",\n    description: \"Sign for moon\"\n  },\n  \"morning\": {\n    name: \"morning\",\n    gif: \"https://lifeprint.com/asl101/gifs/m/morning.gif\",\n    description: \"Sign for morning\"\n  },\n  \"nap\": {\n    name: \"nap\",\n    gif: \"https://lifeprint.com/asl101/gifs/n/nap.gif\",\n    description: \"Sign for nap\"\n  },\n  \"night\": {\n    name: \"night\",\n    gif: \"https://lifeprint.com/asl101/gifs/n/night.gif\",\n    description: \"Sign for night\"\n  },\n  \"not\": {\n    name: \"not\",\n    gif: \"https://lifeprint.com/asl101/gifs/n/not.gif\",\n    description: \"Sign for not\"\n  },\n  \"now\": {\n    name: \"now\",\n    gif: \"https://lifeprint.com/asl101/gifs/n/now.gif\",\n    description: \"Sign for now\"\n  },\n  \"old\": {\n    name: \"old\",\n    gif: \"https://lifeprint.com/asl101/gifs/o/old.gif\",\n    description: \"Sign for old\"\n  },\n  \"open\": {\n    name: \"open\",\n    gif: \"https://lifeprint.com/asl101/gifs/o/open.gif\",\n    description: \"Sign for open\"\n  },\n  \"orange\": {\n    name: \"orange\",\n    gif: \"https://lifeprint.com/asl101/gifs/o/orange.gif\",\n    description: \"Sign for orange\"\n  },\n  \"owie\": {\n    name: \"owie\",\n    gif: \"https://lifeprint.com/asl101/gifs/o/owie.gif\",\n    description: \"Sign for owie\"\n  },\n  \"owl\": {\n    name: \"owl\",\n    gif: \"https://lifeprint.com/asl101/gifs/o/owl.gif\",\n    description: \"Sign for owl\"\n  },\n  \"penny\": {\n    name: \"penny\",\n    gif: \"https://lifeprint.com/asl101/gifs/p/penny.gif\",\n    description: \"Sign for penny\"\n  },\n  \"pizza\": {\n    name: \"pizza\",\n    gif: \"https://lifeprint.com/asl101/gifs/p/pizza.gif\",\n    description: \"Sign for pizza\"\n  },\n  \"puzzle\": {\n    name: \"puzzle\",\n    gif: \"https://lifeprint.com/asl101/gifs/p/puzzle.gif\",\n    description: \"Sign for puzzle\"\n  },\n  \"quiet\": {\n    name: \"quiet\",\n    gif: \"https://lifeprint.com/asl101/gifs/q/quiet.gif\",\n    description: \"Sign for quiet\"\n  },\n  \"sad\": {\n    name: \"sad\",\n    gif: \"https://lifeprint.com/asl101/gifs/s/sad.gif\",\n    description: \"Sign for sad\"\n  },\n  \"same\": {\n    name: \"same\",\n    gif: \"https://lifeprint.com/asl101/gifs/s/same.gif\",\n    description: \"Sign for same\"\n  },\n  \"say\": {\n    name: \"say\",\n    gif: \"https://lifeprint.com/asl101/gifs/s/say.gif\",\n    description: \"Sign for say\"\n  },\n  \"scissors\": {\n    name: \"scissors\",\n    gif: \"https://lifeprint.com/asl101/gifs/s/scissors.gif\",\n    description: \"Sign for scissors\"\n  },\n  \"see\": {\n    name: \"see\",\n    gif: \"https://lifeprint.com/asl101/gifs/s/see.gif\",\n    description: \"Sign for see\"\n  },\n  \"shhh\": {\n    name: \"shhh\",\n    gif: \"https://lifeprint.com/asl101/gifs/s/shhh.gif\",\n    description: \"Sign for shhh\"\n  },\n  \"shirt\": {\n    name: \"shirt\",\n    gif: \"https://lifeprint.com/asl101/gifs/s/shirt.gif\",\n    description: \"Sign for shirt\"\n  },\n  \"shoe\": {\n    name: \"shoe\",\n    gif: \"https://lifeprint.com/asl101/gifs/s/shoe.gif\",\n    description: \"Sign for shoe\"\n  },\n  \"sick\": {\n    name: \"sick\",\n    gif: \"https://lifeprint.com/asl101/gifs/s/sick.gif\",\n    description: \"Sign for sick\"\n  },\n  \"sleep\": {\n    name: \"sleep\",\n    gif: \"https://lifeprint.com/asl101/gifs/s/sleep.gif\",\n    description: \"Sign for sleep\"\n  },\n  \"snow\": {\n    name: \"snow\",\n    gif: \"https://lifeprint.com/asl101/gifs/s/snow.gif\",\n    description: \"Sign for snow\"\n  },\n  \"sticky\": {\n    name: \"sticky\",\n    gif: \"https://lifeprint.com/asl101/gifs/s/sticky.gif\",\n    description: \"Sign for sticky\"\n  },\n  \"store\": {\n    name: \"store\",\n    gif: \"https://lifeprint.com/asl101/gifs/s/store.gif\",\n    description: \"Sign for store\"\n  },\n  \"sun\": {\n    name: \"sun\",\n    gif: \"https://lifeprint.com/asl101/gifs/s/sun.gif\",\n    description: \"Sign for sun\"\n  },\n  \"that\": {\n    name: \"that\",\n    gif: \"https://lifeprint.com/asl101/gifs/t/that.gif\",\n    description: \"Sign for that\"\n  },\n  \"there\": {\n    name: \"there\",\n    gif: \"https://lifeprint.com/asl101/gifs/t/there.gif\",\n    description: \"Sign for there\"\n  },\n  \"think\": {\n    name: \"think\",\n    gif: \"https://lifeprint.com/asl101/gifs/t/think.gif\",\n    description: \"Sign for think\"\n  },\n  \"time\": {\n    name: \"time\",\n    gif: \"https://lifeprint.com/asl101/gifs/t/time.gif\",\n    description: \"Sign for time\"\n  },\n  \"uncle\": {\n    name: \"uncle\",\n    gif: \"https://lifeprint.com/asl101/gifs/u/uncle.gif\",\n    description: \"Sign for uncle\"\n  },\n  \"vacuum\": {\n    name: \"vacuum\",\n    gif: \"https://lifeprint.com/asl101/gifs/v/vacuum.gif\",\n    description: \"Sign for vacuum\"\n  },\n  \"wait\": {\n    name: \"wait\",\n    gif: \"https://lifeprint.com/asl101/gifs/w/wait.gif\",\n    description: \"Sign for wait\"\n  },\n  \"wake\": {\n    name: \"wake\",\n    gif: \"https://lifeprint.com/asl101/gifs/w/wake.gif\",\n    description: \"Sign for wake\"\n  },\n  \"wet\": {\n    name: \"wet\",\n    gif: \"https://lifeprint.com/asl101/gifs/w/wet.gif\",\n    description: \"Sign for wet\"\n  },\n  \"who\": {\n    name: \"who\",\n    gif: \"https://lifeprint.com/asl101/gifs/w/who.gif\",\n    description: \"Sign for who\"\n  },\n  \"yellow\": {\n    name: \"yellow\",\n    gif: \"https://lifeprint.com/asl101/gifs/y/yellow.gif\",\n    description: \"Sign for yellow\"\n  },\n  \"yourself\": {\n    name: \"yourself\",\n    gif: \"https://lifeprint.com/asl101/gifs/y/yourself.gif\",\n    description: \"Sign for yourself\"\n  },\n  \"yucky\": {\n    name: \"yucky\",\n    gif: \"https://lifeprint.com/asl101/gifs/y/yucky.gif\",\n    description: \"Sign for yucky\"\n  },\n  \"zipper\": {\n    name: \"zipper\",\n    gif: \"https://lifeprint.com/asl101/gifs/z/zipper.gif\",\n    description: \"Sign for zipper\"\n  }\n};\nexport default signLanguageData;", "map": {"version": 3, "names": ["signLanguageData", "name", "gif", "description"], "sources": ["D:/ASL/training-frontend/src/components/signs.js"], "sourcesContent": ["// 100 real ASL signs from the model\r\nconst signLanguageData = {\r\n  \"TV\": { name: \"TV\", gif: \"https://lifeprint.com/asl101/gifs/t/tv.gif\", description: \"Sign for TV\" },\r\n  \"alligator\": { name: \"alligator\", gif: \"https://lifeprint.com/asl101/gifs/a/alligator.gif\", description: \"Sign for alligator\" },\r\n  \"another\": { name: \"another\", gif: \"https://lifeprint.com/asl101/gifs/a/another.gif\", description: \"Sign for another\" },\r\n  \"apple\": { name: \"apple\", gif: \"https://lifeprint.com/asl101/gifs/a/apple.gif\", description: \"Sign for apple\" },\r\n  \"backyard\": { name: \"backyard\", gif: \"https://lifeprint.com/asl101/gifs/b/backyard.gif\", description: \"Sign for backyard\" },\r\n  \"bad\": { name: \"bad\", gif: \"https://lifeprint.com/asl101/gifs/b/bad.gif\", description: \"Sign for bad\" },\r\n  \"balloon\": { name: \"balloon\", gif: \"https://lifeprint.com/asl101/gifs/b/balloon.gif\", description: \"Sign for balloon\" },\r\n  \"bee\": { name: \"bee\", gif: \"https://lifeprint.com/asl101/gifs/b/bee.gif\", description: \"Sign for bee\" },\r\n  \"bird\": { name: \"bird\", gif: \"https://lifeprint.com/asl101/gifs/b/bird.gif\", description: \"Sign for bird\" },\r\n  \"black\": { name: \"black\", gif: \"https://lifeprint.com/asl101/gifs/b/black.gif\", description: \"Sign for black\" },\r\n  \"blow\": { name: \"blow\", gif: \"https://lifeprint.com/asl101/gifs/b/blow.gif\", description: \"Sign for blow\" },\r\n  \"brother\": { name: \"brother\", gif: \"https://lifeprint.com/asl101/gifs/b/brother.gif\", description: \"Sign for brother\" },\r\n  \"brown\": { name: \"brown\", gif: \"https://lifeprint.com/asl101/gifs/b/brown.gif\", description: \"Sign for brown\" },\r\n  \"bug\": { name: \"bug\", gif: \"https://lifeprint.com/asl101/gifs/b/bug.gif\", description: \"Sign for bug\" },\r\n  \"callonphone\": { name: \"callonphone\", gif: \"https://lifeprint.com/asl101/gifs/c/callonphone.gif\", description: \"Sign for callonphone\" },\r\n  \"can\": { name: \"can\", gif: \"https://lifeprint.com/asl101/gifs/c/can.gif\", description: \"Sign for can\" },\r\n  \"cheek\": { name: \"cheek\", gif: \"https://lifeprint.com/asl101/gifs/c/cheek.gif\", description: \"Sign for cheek\" },\r\n  \"clean\": { name: \"clean\", gif: \"https://lifeprint.com/asl101/gifs/c/clean.gif\", description: \"Sign for clean\" },\r\n  \"closet\": { name: \"closet\", gif: \"https://lifeprint.com/asl101/gifs/c/closet.gif\", description: \"Sign for closet\" },\r\n  \"cloud\": { name: \"cloud\", gif: \"https://lifeprint.com/asl101/gifs/c/cloud.gif\", description: \"Sign for cloud\" },\r\n  \"clown\": { name: \"clown\", gif: \"https://lifeprint.com/asl101/gifs/c/clown.gif\", description: \"Sign for clown\" },\r\n  \"cowboy\": { name: \"cowboy\", gif: \"https://lifeprint.com/asl101/gifs/c/cowboy.gif\", description: \"Sign for cowboy\" },\r\n  \"cry\": { name: \"cry\", gif: \"https://lifeprint.com/asl101/gifs/c/cry.gif\", description: \"Sign for cry\" },\r\n  \"cute\": { name: \"cute\", gif: \"https://lifeprint.com/asl101/gifs/c/cute.gif\", description: \"Sign for cute\" },\r\n  \"drawer\": { name: \"drawer\", gif: \"https://lifeprint.com/asl101/gifs/d/drawer.gif\", description: \"Sign for drawer\" },\r\n  \"dry\": { name: \"dry\", gif: \"https://lifeprint.com/asl101/gifs/d/dry.gif\", description: \"Sign for dry\" },\r\n  \"dryer\": { name: \"dryer\", gif: \"https://lifeprint.com/asl101/gifs/d/dryer.gif\", description: \"Sign for dryer\" },\r\n  \"duck\": { name: \"duck\", gif: \"https://lifeprint.com/asl101/gifs/d/duck.gif\", description: \"Sign for duck\" },\r\n  \"empty\": { name: \"empty\", gif: \"https://lifeprint.com/asl101/gifs/e/empty.gif\", description: \"Sign for empty\" },\r\n  \"face\": { name: \"face\", gif: \"https://lifeprint.com/asl101/gifs/f/face.gif\", description: \"Sign for face\" },\r\n  \"fall\": { name: \"fall\", gif: \"https://lifeprint.com/asl101/gifs/f/fall.gif\", description: \"Sign for fall\" },\r\n  \"fast\": { name: \"fast\", gif: \"https://lifeprint.com/asl101/gifs/f/fast.gif\", description: \"Sign for fast\" },\r\n  \"feet\": { name: \"feet\", gif: \"https://lifeprint.com/asl101/gifs/f/feet.gif\", description: \"Sign for feet\" },\r\n  \"find\": { name: \"find\", gif: \"https://lifeprint.com/asl101/gifs/f/find.gif\", description: \"Sign for find\" },\r\n  \"finger\": { name: \"finger\", gif: \"https://lifeprint.com/asl101/gifs/f/finger.gif\", description: \"Sign for finger\" },\r\n  \"finish\": { name: \"finish\", gif: \"https://lifeprint.com/asl101/gifs/f/finish.gif\", description: \"Sign for finish\" },\r\n  \"first\": { name: \"first\", gif: \"https://lifeprint.com/asl101/gifs/f/first.gif\", description: \"Sign for first\" },\r\n  \"fish\": { name: \"fish\", gif: \"https://lifeprint.com/asl101/gifs/f/fish.gif\", description: \"Sign for fish\" },\r\n  \"flower\": { name: \"flower\", gif: \"https://lifeprint.com/asl101/gifs/f/flower.gif\", description: \"Sign for flower\" },\r\n  \"frenchfries\": { name: \"frenchfries\", gif: \"https://lifeprint.com/asl101/gifs/f/frenchfries.gif\", description: \"Sign for frenchfries\" },\r\n  \"gift\": { name: \"gift\", gif: \"https://lifeprint.com/asl101/gifs/g/gift.gif\", description: \"Sign for gift\" },\r\n  \"giraffe\": { name: \"giraffe\", gif: \"https://lifeprint.com/asl101/gifs/g/giraffe.gif\", description: \"Sign for giraffe\" },\r\n  \"go\": { name: \"go\", gif: \"https://lifeprint.com/asl101/gifs/g/go.gif\", description: \"Sign for go\" },\r\n  \"grass\": { name: \"grass\", gif: \"https://lifeprint.com/asl101/gifs/g/grass.gif\", description: \"Sign for grass\" },\r\n  \"green\": { name: \"green\", gif: \"https://lifeprint.com/asl101/gifs/g/green.gif\", description: \"Sign for green\" },\r\n  \"hair\": { name: \"hair\", gif: \"https://lifeprint.com/asl101/gifs/h/hair.gif\", description: \"Sign for hair\" },\r\n  \"have\": { name: \"have\", gif: \"https://lifeprint.com/asl101/gifs/h/have.gif\", description: \"Sign for have\" },\r\n  \"hungry\": { name: \"hungry\", gif: \"https://lifeprint.com/asl101/gifs/h/hungry.gif\", description: \"Sign for hungry\" },\r\n  \"jacket\": { name: \"jacket\", gif: \"https://lifeprint.com/asl101/gifs/j/jacket.gif\", description: \"Sign for jacket\" },\r\n  \"jump\": { name: \"jump\", gif: \"https://lifeprint.com/asl101/gifs/j/jump.gif\", description: \"Sign for jump\" },\r\n  \"lips\": { name: \"lips\", gif: \"https://lifeprint.com/asl101/gifs/l/lips.gif\", description: \"Sign for lips\" },\r\n  \"look\": { name: \"look\", gif: \"https://lifeprint.com/asl101/gifs/l/look.gif\", description: \"Sign for look\" },\r\n  \"mad\": { name: \"mad\", gif: \"https://lifeprint.com/asl101/gifs/m/mad.gif\", description: \"Sign for mad\" },\r\n  \"man\": { name: \"man\", gif: \"https://lifeprint.com/asl101/gifs/m/man.gif\", description: \"Sign for man\" },\r\n  \"milk\": { name: \"milk\", gif: \"https://lifeprint.com/asl101/gifs/m/milk.gif\", description: \"Sign for milk\" },\r\n  \"minemy\": { name: \"minemy\", gif: \"https://lifeprint.com/asl101/gifs/m/minemy.gif\", description: \"Sign for minemy\" },\r\n  \"mitten\": { name: \"mitten\", gif: \"https://lifeprint.com/asl101/gifs/m/mitten.gif\", description: \"Sign for mitten\" },\r\n  \"moon\": { name: \"moon\", gif: \"https://lifeprint.com/asl101/gifs/m/moon.gif\", description: \"Sign for moon\" },\r\n  \"morning\": { name: \"morning\", gif: \"https://lifeprint.com/asl101/gifs/m/morning.gif\", description: \"Sign for morning\" },\r\n  \"nap\": { name: \"nap\", gif: \"https://lifeprint.com/asl101/gifs/n/nap.gif\", description: \"Sign for nap\" },\r\n  \"night\": { name: \"night\", gif: \"https://lifeprint.com/asl101/gifs/n/night.gif\", description: \"Sign for night\" },\r\n  \"not\": { name: \"not\", gif: \"https://lifeprint.com/asl101/gifs/n/not.gif\", description: \"Sign for not\" },\r\n  \"now\": { name: \"now\", gif: \"https://lifeprint.com/asl101/gifs/n/now.gif\", description: \"Sign for now\" },\r\n  \"old\": { name: \"old\", gif: \"https://lifeprint.com/asl101/gifs/o/old.gif\", description: \"Sign for old\" },\r\n  \"open\": { name: \"open\", gif: \"https://lifeprint.com/asl101/gifs/o/open.gif\", description: \"Sign for open\" },\r\n  \"orange\": { name: \"orange\", gif: \"https://lifeprint.com/asl101/gifs/o/orange.gif\", description: \"Sign for orange\" },\r\n  \"owie\": { name: \"owie\", gif: \"https://lifeprint.com/asl101/gifs/o/owie.gif\", description: \"Sign for owie\" },\r\n  \"owl\": { name: \"owl\", gif: \"https://lifeprint.com/asl101/gifs/o/owl.gif\", description: \"Sign for owl\" },\r\n  \"penny\": { name: \"penny\", gif: \"https://lifeprint.com/asl101/gifs/p/penny.gif\", description: \"Sign for penny\" },\r\n  \"pizza\": { name: \"pizza\", gif: \"https://lifeprint.com/asl101/gifs/p/pizza.gif\", description: \"Sign for pizza\" },\r\n  \"puzzle\": { name: \"puzzle\", gif: \"https://lifeprint.com/asl101/gifs/p/puzzle.gif\", description: \"Sign for puzzle\" },\r\n  \"quiet\": { name: \"quiet\", gif: \"https://lifeprint.com/asl101/gifs/q/quiet.gif\", description: \"Sign for quiet\" },\r\n  \"sad\": { name: \"sad\", gif: \"https://lifeprint.com/asl101/gifs/s/sad.gif\", description: \"Sign for sad\" },\r\n  \"same\": { name: \"same\", gif: \"https://lifeprint.com/asl101/gifs/s/same.gif\", description: \"Sign for same\" },\r\n  \"say\": { name: \"say\", gif: \"https://lifeprint.com/asl101/gifs/s/say.gif\", description: \"Sign for say\" },\r\n  \"scissors\": { name: \"scissors\", gif: \"https://lifeprint.com/asl101/gifs/s/scissors.gif\", description: \"Sign for scissors\" },\r\n  \"see\": { name: \"see\", gif: \"https://lifeprint.com/asl101/gifs/s/see.gif\", description: \"Sign for see\" },\r\n  \"shhh\": { name: \"shhh\", gif: \"https://lifeprint.com/asl101/gifs/s/shhh.gif\", description: \"Sign for shhh\" },\r\n  \"shirt\": { name: \"shirt\", gif: \"https://lifeprint.com/asl101/gifs/s/shirt.gif\", description: \"Sign for shirt\" },\r\n  \"shoe\": { name: \"shoe\", gif: \"https://lifeprint.com/asl101/gifs/s/shoe.gif\", description: \"Sign for shoe\" },\r\n  \"sick\": { name: \"sick\", gif: \"https://lifeprint.com/asl101/gifs/s/sick.gif\", description: \"Sign for sick\" },\r\n  \"sleep\": { name: \"sleep\", gif: \"https://lifeprint.com/asl101/gifs/s/sleep.gif\", description: \"Sign for sleep\" },\r\n  \"snow\": { name: \"snow\", gif: \"https://lifeprint.com/asl101/gifs/s/snow.gif\", description: \"Sign for snow\" },\r\n  \"sticky\": { name: \"sticky\", gif: \"https://lifeprint.com/asl101/gifs/s/sticky.gif\", description: \"Sign for sticky\" },\r\n  \"store\": { name: \"store\", gif: \"https://lifeprint.com/asl101/gifs/s/store.gif\", description: \"Sign for store\" },\r\n  \"sun\": { name: \"sun\", gif: \"https://lifeprint.com/asl101/gifs/s/sun.gif\", description: \"Sign for sun\" },\r\n  \"that\": { name: \"that\", gif: \"https://lifeprint.com/asl101/gifs/t/that.gif\", description: \"Sign for that\" },\r\n  \"there\": { name: \"there\", gif: \"https://lifeprint.com/asl101/gifs/t/there.gif\", description: \"Sign for there\" },\r\n  \"think\": { name: \"think\", gif: \"https://lifeprint.com/asl101/gifs/t/think.gif\", description: \"Sign for think\" },\r\n  \"time\": { name: \"time\", gif: \"https://lifeprint.com/asl101/gifs/t/time.gif\", description: \"Sign for time\" },\r\n  \"uncle\": { name: \"uncle\", gif: \"https://lifeprint.com/asl101/gifs/u/uncle.gif\", description: \"Sign for uncle\" },\r\n  \"vacuum\": { name: \"vacuum\", gif: \"https://lifeprint.com/asl101/gifs/v/vacuum.gif\", description: \"Sign for vacuum\" },\r\n  \"wait\": { name: \"wait\", gif: \"https://lifeprint.com/asl101/gifs/w/wait.gif\", description: \"Sign for wait\" },\r\n  \"wake\": { name: \"wake\", gif: \"https://lifeprint.com/asl101/gifs/w/wake.gif\", description: \"Sign for wake\" },\r\n  \"wet\": { name: \"wet\", gif: \"https://lifeprint.com/asl101/gifs/w/wet.gif\", description: \"Sign for wet\" },\r\n  \"who\": { name: \"who\", gif: \"https://lifeprint.com/asl101/gifs/w/who.gif\", description: \"Sign for who\" },\r\n  \"yellow\": { name: \"yellow\", gif: \"https://lifeprint.com/asl101/gifs/y/yellow.gif\", description: \"Sign for yellow\" },\r\n  \"yourself\": { name: \"yourself\", gif: \"https://lifeprint.com/asl101/gifs/y/yourself.gif\", description: \"Sign for yourself\" },\r\n  \"yucky\": { name: \"yucky\", gif: \"https://lifeprint.com/asl101/gifs/y/yucky.gif\", description: \"Sign for yucky\" },\r\n  \"zipper\": { name: \"zipper\", gif: \"https://lifeprint.com/asl101/gifs/z/zipper.gif\", description: \"Sign for zipper\" }\r\n};\r\nexport default signLanguageData; "], "mappings": "AAAA;AACA,MAAMA,gBAAgB,GAAG;EACvB,IAAI,EAAE;IAAEC,IAAI,EAAE,IAAI;IAAEC,GAAG,EAAE,4CAA4C;IAAEC,WAAW,EAAE;EAAc,CAAC;EACnG,WAAW,EAAE;IAAEF,IAAI,EAAE,WAAW;IAAEC,GAAG,EAAE,mDAAmD;IAAEC,WAAW,EAAE;EAAqB,CAAC;EAC/H,SAAS,EAAE;IAAEF,IAAI,EAAE,SAAS;IAAEC,GAAG,EAAE,iDAAiD;IAAEC,WAAW,EAAE;EAAmB,CAAC;EACvH,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC/G,UAAU,EAAE;IAAEF,IAAI,EAAE,UAAU;IAAEC,GAAG,EAAE,kDAAkD;IAAEC,WAAW,EAAE;EAAoB,CAAC;EAC3H,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAe,CAAC;EACvG,SAAS,EAAE;IAAEF,IAAI,EAAE,SAAS;IAAEC,GAAG,EAAE,iDAAiD;IAAEC,WAAW,EAAE;EAAmB,CAAC;EACvH,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAe,CAAC;EACvG,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAC3G,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC/G,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAC3G,SAAS,EAAE;IAAEF,IAAI,EAAE,SAAS;IAAEC,GAAG,EAAE,iDAAiD;IAAEC,WAAW,EAAE;EAAmB,CAAC;EACvH,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC/G,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAe,CAAC;EACvG,aAAa,EAAE;IAAEF,IAAI,EAAE,aAAa;IAAEC,GAAG,EAAE,qDAAqD;IAAEC,WAAW,EAAE;EAAuB,CAAC;EACvI,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAe,CAAC;EACvG,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC/G,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC/G,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,gDAAgD;IAAEC,WAAW,EAAE;EAAkB,CAAC;EACnH,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC/G,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC/G,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,gDAAgD;IAAEC,WAAW,EAAE;EAAkB,CAAC;EACnH,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAe,CAAC;EACvG,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAC3G,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,gDAAgD;IAAEC,WAAW,EAAE;EAAkB,CAAC;EACnH,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAe,CAAC;EACvG,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC/G,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAC3G,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC/G,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAC3G,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAC3G,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAC3G,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAC3G,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAC3G,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,gDAAgD;IAAEC,WAAW,EAAE;EAAkB,CAAC;EACnH,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,gDAAgD;IAAEC,WAAW,EAAE;EAAkB,CAAC;EACnH,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC/G,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAC3G,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,gDAAgD;IAAEC,WAAW,EAAE;EAAkB,CAAC;EACnH,aAAa,EAAE;IAAEF,IAAI,EAAE,aAAa;IAAEC,GAAG,EAAE,qDAAqD;IAAEC,WAAW,EAAE;EAAuB,CAAC;EACvI,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAC3G,SAAS,EAAE;IAAEF,IAAI,EAAE,SAAS;IAAEC,GAAG,EAAE,iDAAiD;IAAEC,WAAW,EAAE;EAAmB,CAAC;EACvH,IAAI,EAAE;IAAEF,IAAI,EAAE,IAAI;IAAEC,GAAG,EAAE,4CAA4C;IAAEC,WAAW,EAAE;EAAc,CAAC;EACnG,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC/G,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC/G,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAC3G,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAC3G,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,gDAAgD;IAAEC,WAAW,EAAE;EAAkB,CAAC;EACnH,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,gDAAgD;IAAEC,WAAW,EAAE;EAAkB,CAAC;EACnH,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAC3G,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAC3G,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAC3G,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAe,CAAC;EACvG,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAe,CAAC;EACvG,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAC3G,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,gDAAgD;IAAEC,WAAW,EAAE;EAAkB,CAAC;EACnH,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,gDAAgD;IAAEC,WAAW,EAAE;EAAkB,CAAC;EACnH,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAC3G,SAAS,EAAE;IAAEF,IAAI,EAAE,SAAS;IAAEC,GAAG,EAAE,iDAAiD;IAAEC,WAAW,EAAE;EAAmB,CAAC;EACvH,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAe,CAAC;EACvG,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC/G,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAe,CAAC;EACvG,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAe,CAAC;EACvG,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAe,CAAC;EACvG,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAC3G,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,gDAAgD;IAAEC,WAAW,EAAE;EAAkB,CAAC;EACnH,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAC3G,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAe,CAAC;EACvG,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC/G,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC/G,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,gDAAgD;IAAEC,WAAW,EAAE;EAAkB,CAAC;EACnH,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC/G,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAe,CAAC;EACvG,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAC3G,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAe,CAAC;EACvG,UAAU,EAAE;IAAEF,IAAI,EAAE,UAAU;IAAEC,GAAG,EAAE,kDAAkD;IAAEC,WAAW,EAAE;EAAoB,CAAC;EAC3H,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAe,CAAC;EACvG,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAC3G,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC/G,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAC3G,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAC3G,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC/G,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAC3G,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,gDAAgD;IAAEC,WAAW,EAAE;EAAkB,CAAC;EACnH,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC/G,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAe,CAAC;EACvG,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAC3G,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC/G,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC/G,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAC3G,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC/G,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,gDAAgD;IAAEC,WAAW,EAAE;EAAkB,CAAC;EACnH,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAC3G,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,GAAG,EAAE,8CAA8C;IAAEC,WAAW,EAAE;EAAgB,CAAC;EAC3G,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAe,CAAC;EACvG,KAAK,EAAE;IAAEF,IAAI,EAAE,KAAK;IAAEC,GAAG,EAAE,6CAA6C;IAAEC,WAAW,EAAE;EAAe,CAAC;EACvG,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,gDAAgD;IAAEC,WAAW,EAAE;EAAkB,CAAC;EACnH,UAAU,EAAE;IAAEF,IAAI,EAAE,UAAU;IAAEC,GAAG,EAAE,kDAAkD;IAAEC,WAAW,EAAE;EAAoB,CAAC;EAC3H,OAAO,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,GAAG,EAAE,+CAA+C;IAAEC,WAAW,EAAE;EAAiB,CAAC;EAC/G,QAAQ,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,GAAG,EAAE,gDAAgD;IAAEC,WAAW,EAAE;EAAkB;AACpH,CAAC;AACD,eAAeH,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}