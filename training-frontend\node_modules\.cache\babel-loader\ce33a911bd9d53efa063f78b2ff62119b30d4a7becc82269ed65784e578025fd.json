{"ast": null, "code": "import _objectSpread from\"D:/ASL/training-frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import{useState,useRef,useCallback,useEffect}from'react';const BACKEND_URL='ws://localhost:8001/ws/detect';export const useSignDetection=()=>{const[isConnected,setIsConnected]=useState(false);const[prediction,setPrediction]=useState(null);const[lastPrediction,setLastPrediction]=useState(null);const[isRecording,setIsRecording]=useState(false);const[recordingStatus,setRecordingStatus]=useState('');const[processedFrame,setProcessedFrame]=useState(null);const[signMatched,setSignMatched]=useState(false);const[targetSign,setTargetSign]=useState('');const[predictionHistory,setPredictionHistory]=useState([]);const wsRef=useRef(null);const reconnectTimeoutRef=useRef(null);const frameIntervalRef=useRef(null);const connect=useCallback(()=>{try{wsRef.current=new WebSocket(BACKEND_URL);wsRef.current.onopen=()=>{console.log('Connected to sign detection backend');setIsConnected(true);setRecordingStatus('Connected to AI backend');};wsRef.current.onmessage=event=>{try{const data=JSON.parse(event.data);console.log('Received from backend:',data.type,data.prediction);switch(data.type){case'frame_processed':// Always update processed frame\nsetProcessedFrame(data.processed_frame);setSignMatched(data.sign_matched||false);setTargetSign(data.target_sign||'');// Update prediction - keep last prediction if new one is null\nif(data.prediction){setPrediction(data.prediction);setLastPrediction(data.prediction);// Add to prediction history (keep last 5)\nsetPredictionHistory(prev=>{const newHistory=[data.prediction,...prev.slice(0,4)];return newHistory;});console.log(\"Detected: \".concat(data.prediction.sign,\" (\").concat(Math.round(data.prediction.confidence*100),\"%)\"));}else if(lastPrediction){// Keep showing last prediction with reduced opacity\nsetPrediction(_objectSpread(_objectSpread({},lastPrediction),{},{confidence:lastPrediction.confidence*0.7,isStale:true}));}break;case'recording_started':setIsRecording(true);setRecordingStatus(\"Recording started for: \".concat(data.target_sign));break;case'recording_stopped':setIsRecording(false);if(data.result){setRecordingStatus(\"Recording saved: \".concat(data.result.frame_count,\" frames\"));}else{setRecordingStatus('Recording stopped');}break;default:console.log('Unknown message type:',data.type);}}catch(error){console.error('Error parsing WebSocket message:',error);}};wsRef.current.onclose=()=>{console.log('Disconnected from sign detection backend');setIsConnected(false);setRecordingStatus('Disconnected from backend');// Attempt to reconnect after 3 seconds\nreconnectTimeoutRef.current=setTimeout(()=>{console.log('Attempting to reconnect...');connect();},3000);};wsRef.current.onerror=error=>{console.error('WebSocket error:',error);setRecordingStatus('Connection error');};}catch(error){console.error('Error connecting to WebSocket:',error);setRecordingStatus('Failed to connect to backend');}},[]);const disconnect=useCallback(()=>{if(reconnectTimeoutRef.current){clearTimeout(reconnectTimeoutRef.current);}if(frameIntervalRef.current){clearInterval(frameIntervalRef.current);}if(wsRef.current){wsRef.current.close();wsRef.current=null;}setIsConnected(false);},[]);const sendFrame=useCallback(frameData=>{if(wsRef.current&&wsRef.current.readyState===WebSocket.OPEN){const message={type:'frame',frame:frameData};wsRef.current.send(JSON.stringify(message));}},[]);const startRecording=useCallback(targetSignName=>{if(wsRef.current&&wsRef.current.readyState===WebSocket.OPEN){const message={type:'start_recording',target_sign:targetSignName};wsRef.current.send(JSON.stringify(message));setTargetSign(targetSignName);}},[]);const stopRecording=useCallback(()=>{if(wsRef.current&&wsRef.current.readyState===WebSocket.OPEN){const message={type:'stop_recording'};wsRef.current.send(JSON.stringify(message));}},[]);const startFrameCapture=useCallback(function(webcamRef){let interval=arguments.length>1&&arguments[1]!==undefined?arguments[1]:200;if(frameIntervalRef.current){clearInterval(frameIntervalRef.current);}frameIntervalRef.current=setInterval(()=>{if(webcamRef.current&&isConnected){const imageSrc=webcamRef.current.getScreenshot();if(imageSrc){console.log('Sending frame to backend');sendFrame(imageSrc);}}},interval);},[isConnected,sendFrame]);const stopFrameCapture=useCallback(()=>{if(frameIntervalRef.current){clearInterval(frameIntervalRef.current);frameIntervalRef.current=null;}},[]);// Auto-connect on mount\nuseEffect(()=>{connect();return()=>{disconnect();};},[connect,disconnect]);return{isConnected,prediction,lastPrediction,predictionHistory,isRecording,recordingStatus,processedFrame,signMatched,targetSign,connect,disconnect,sendFrame,startRecording,stopRecording,startFrameCapture,stopFrameCapture};};", "map": {"version": 3, "names": ["useState", "useRef", "useCallback", "useEffect", "BACKEND_URL", "useSignDetection", "isConnected", "setIsConnected", "prediction", "setPrediction", "lastPrediction", "setLastPrediction", "isRecording", "setIsRecording", "recordingStatus", "setRecordingStatus", "processedFrame", "setProcessedFrame", "signMatched", "setSignMatched", "targetSign", "setTargetSign", "predictionHistory", "setPredictionHistory", "wsRef", "reconnectTimeoutRef", "frameIntervalRef", "connect", "current", "WebSocket", "onopen", "console", "log", "onmessage", "event", "data", "JSON", "parse", "type", "processed_frame", "sign_matched", "target_sign", "prev", "newHistory", "slice", "concat", "sign", "Math", "round", "confidence", "_objectSpread", "isStale", "result", "frame_count", "error", "onclose", "setTimeout", "onerror", "disconnect", "clearTimeout", "clearInterval", "close", "sendFrame", "frameData", "readyState", "OPEN", "message", "frame", "send", "stringify", "startRecording", "targetSignName", "stopRecording", "startFrameCapture", "webcamRef", "interval", "arguments", "length", "undefined", "setInterval", "imageSrc", "getScreenshot", "stopFrameCapture"], "sources": ["D:/ASL/training-frontend/src/hooks/useSignDetection.js"], "sourcesContent": ["import { useState, useRef, useCallback, useEffect } from 'react';\n\nconst BACKEND_URL = 'ws://localhost:8001/ws/detect';\n\nexport const useSignDetection = () => {\n  const [isConnected, setIsConnected] = useState(false);\n  const [prediction, setPrediction] = useState(null);\n  const [lastPrediction, setLastPrediction] = useState(null);\n  const [isRecording, setIsRecording] = useState(false);\n  const [recordingStatus, setRecordingStatus] = useState('');\n  const [processedFrame, setProcessedFrame] = useState(null);\n  const [signMatched, setSignMatched] = useState(false);\n  const [targetSign, setTargetSign] = useState('');\n  const [predictionHistory, setPredictionHistory] = useState([]);\n  \n  const wsRef = useRef(null);\n  const reconnectTimeoutRef = useRef(null);\n  const frameIntervalRef = useRef(null);\n  \n  const connect = useCallback(() => {\n    try {\n      wsRef.current = new WebSocket(BACKEND_URL);\n      \n      wsRef.current.onopen = () => {\n        console.log('Connected to sign detection backend');\n        setIsConnected(true);\n        setRecordingStatus('Connected to AI backend');\n      };\n      \n      wsRef.current.onmessage = (event) => {\n        try {\n          const data = JSON.parse(event.data);\n          console.log('Received from backend:', data.type, data.prediction);\n\n          switch (data.type) {\n            case 'frame_processed':\n              // Always update processed frame\n              setProcessedFrame(data.processed_frame);\n              setSignMatched(data.sign_matched || false);\n              setTargetSign(data.target_sign || '');\n\n              // Update prediction - keep last prediction if new one is null\n              if (data.prediction) {\n                setPrediction(data.prediction);\n                setLastPrediction(data.prediction);\n\n                // Add to prediction history (keep last 5)\n                setPredictionHistory(prev => {\n                  const newHistory = [data.prediction, ...prev.slice(0, 4)];\n                  return newHistory;\n                });\n\n                console.log(`Detected: ${data.prediction.sign} (${Math.round(data.prediction.confidence * 100)}%)`);\n              } else if (lastPrediction) {\n                // Keep showing last prediction with reduced opacity\n                setPrediction({\n                  ...lastPrediction,\n                  confidence: lastPrediction.confidence * 0.7,\n                  isStale: true\n                });\n              }\n              break;\n              \n            case 'recording_started':\n              setIsRecording(true);\n              setRecordingStatus(`Recording started for: ${data.target_sign}`);\n              break;\n              \n            case 'recording_stopped':\n              setIsRecording(false);\n              if (data.result) {\n                setRecordingStatus(`Recording saved: ${data.result.frame_count} frames`);\n              } else {\n                setRecordingStatus('Recording stopped');\n              }\n              break;\n              \n            default:\n              console.log('Unknown message type:', data.type);\n          }\n        } catch (error) {\n          console.error('Error parsing WebSocket message:', error);\n        }\n      };\n      \n      wsRef.current.onclose = () => {\n        console.log('Disconnected from sign detection backend');\n        setIsConnected(false);\n        setRecordingStatus('Disconnected from backend');\n        \n        // Attempt to reconnect after 3 seconds\n        reconnectTimeoutRef.current = setTimeout(() => {\n          console.log('Attempting to reconnect...');\n          connect();\n        }, 3000);\n      };\n      \n      wsRef.current.onerror = (error) => {\n        console.error('WebSocket error:', error);\n        setRecordingStatus('Connection error');\n      };\n      \n    } catch (error) {\n      console.error('Error connecting to WebSocket:', error);\n      setRecordingStatus('Failed to connect to backend');\n    }\n  }, []);\n  \n  const disconnect = useCallback(() => {\n    if (reconnectTimeoutRef.current) {\n      clearTimeout(reconnectTimeoutRef.current);\n    }\n    \n    if (frameIntervalRef.current) {\n      clearInterval(frameIntervalRef.current);\n    }\n    \n    if (wsRef.current) {\n      wsRef.current.close();\n      wsRef.current = null;\n    }\n    \n    setIsConnected(false);\n  }, []);\n  \n  const sendFrame = useCallback((frameData) => {\n    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {\n      const message = {\n        type: 'frame',\n        frame: frameData\n      };\n      wsRef.current.send(JSON.stringify(message));\n    }\n  }, []);\n  \n  const startRecording = useCallback((targetSignName) => {\n    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {\n      const message = {\n        type: 'start_recording',\n        target_sign: targetSignName\n      };\n      wsRef.current.send(JSON.stringify(message));\n      setTargetSign(targetSignName);\n    }\n  }, []);\n  \n  const stopRecording = useCallback(() => {\n    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {\n      const message = {\n        type: 'stop_recording'\n      };\n      wsRef.current.send(JSON.stringify(message));\n    }\n  }, []);\n  \n  const startFrameCapture = useCallback((webcamRef, interval = 200) => {\n    if (frameIntervalRef.current) {\n      clearInterval(frameIntervalRef.current);\n    }\n\n    frameIntervalRef.current = setInterval(() => {\n      if (webcamRef.current && isConnected) {\n        const imageSrc = webcamRef.current.getScreenshot();\n        if (imageSrc) {\n          console.log('Sending frame to backend');\n          sendFrame(imageSrc);\n        }\n      }\n    }, interval);\n  }, [isConnected, sendFrame]);\n  \n  const stopFrameCapture = useCallback(() => {\n    if (frameIntervalRef.current) {\n      clearInterval(frameIntervalRef.current);\n      frameIntervalRef.current = null;\n    }\n  }, []);\n  \n  // Auto-connect on mount\n  useEffect(() => {\n    connect();\n    \n    return () => {\n      disconnect();\n    };\n  }, [connect, disconnect]);\n  \n  return {\n    isConnected,\n    prediction,\n    lastPrediction,\n    predictionHistory,\n    isRecording,\n    recordingStatus,\n    processedFrame,\n    signMatched,\n    targetSign,\n    connect,\n    disconnect,\n    sendFrame,\n    startRecording,\n    stopRecording,\n    startFrameCapture,\n    stopFrameCapture\n  };\n};\n"], "mappings": "6GAAA,OAASA,QAAQ,CAAEC,MAAM,CAAEC,WAAW,CAAEC,SAAS,KAAQ,OAAO,CAEhE,KAAM,CAAAC,WAAW,CAAG,+BAA+B,CAEnD,MAAO,MAAM,CAAAC,gBAAgB,CAAGA,CAAA,GAAM,CACpC,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAGP,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAACQ,UAAU,CAAEC,aAAa,CAAC,CAAGT,QAAQ,CAAC,IAAI,CAAC,CAClD,KAAM,CAACU,cAAc,CAAEC,iBAAiB,CAAC,CAAGX,QAAQ,CAAC,IAAI,CAAC,CAC1D,KAAM,CAACY,WAAW,CAAEC,cAAc,CAAC,CAAGb,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAACc,eAAe,CAAEC,kBAAkB,CAAC,CAAGf,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACgB,cAAc,CAAEC,iBAAiB,CAAC,CAAGjB,QAAQ,CAAC,IAAI,CAAC,CAC1D,KAAM,CAACkB,WAAW,CAAEC,cAAc,CAAC,CAAGnB,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAACoB,UAAU,CAAEC,aAAa,CAAC,CAAGrB,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACsB,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGvB,QAAQ,CAAC,EAAE,CAAC,CAE9D,KAAM,CAAAwB,KAAK,CAAGvB,MAAM,CAAC,IAAI,CAAC,CAC1B,KAAM,CAAAwB,mBAAmB,CAAGxB,MAAM,CAAC,IAAI,CAAC,CACxC,KAAM,CAAAyB,gBAAgB,CAAGzB,MAAM,CAAC,IAAI,CAAC,CAErC,KAAM,CAAA0B,OAAO,CAAGzB,WAAW,CAAC,IAAM,CAChC,GAAI,CACFsB,KAAK,CAACI,OAAO,CAAG,GAAI,CAAAC,SAAS,CAACzB,WAAW,CAAC,CAE1CoB,KAAK,CAACI,OAAO,CAACE,MAAM,CAAG,IAAM,CAC3BC,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC,CAClDzB,cAAc,CAAC,IAAI,CAAC,CACpBQ,kBAAkB,CAAC,yBAAyB,CAAC,CAC/C,CAAC,CAEDS,KAAK,CAACI,OAAO,CAACK,SAAS,CAAIC,KAAK,EAAK,CACnC,GAAI,CACF,KAAM,CAAAC,IAAI,CAAGC,IAAI,CAACC,KAAK,CAACH,KAAK,CAACC,IAAI,CAAC,CACnCJ,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAEG,IAAI,CAACG,IAAI,CAAEH,IAAI,CAAC3B,UAAU,CAAC,CAEjE,OAAQ2B,IAAI,CAACG,IAAI,EACf,IAAK,iBAAiB,CACpB;AACArB,iBAAiB,CAACkB,IAAI,CAACI,eAAe,CAAC,CACvCpB,cAAc,CAACgB,IAAI,CAACK,YAAY,EAAI,KAAK,CAAC,CAC1CnB,aAAa,CAACc,IAAI,CAACM,WAAW,EAAI,EAAE,CAAC,CAErC;AACA,GAAIN,IAAI,CAAC3B,UAAU,CAAE,CACnBC,aAAa,CAAC0B,IAAI,CAAC3B,UAAU,CAAC,CAC9BG,iBAAiB,CAACwB,IAAI,CAAC3B,UAAU,CAAC,CAElC;AACAe,oBAAoB,CAACmB,IAAI,EAAI,CAC3B,KAAM,CAAAC,UAAU,CAAG,CAACR,IAAI,CAAC3B,UAAU,CAAE,GAAGkC,IAAI,CAACE,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAAC,CACzD,MAAO,CAAAD,UAAU,CACnB,CAAC,CAAC,CAEFZ,OAAO,CAACC,GAAG,cAAAa,MAAA,CAAcV,IAAI,CAAC3B,UAAU,CAACsC,IAAI,OAAAD,MAAA,CAAKE,IAAI,CAACC,KAAK,CAACb,IAAI,CAAC3B,UAAU,CAACyC,UAAU,CAAG,GAAG,CAAC,MAAI,CAAC,CACrG,CAAC,IAAM,IAAIvC,cAAc,CAAE,CACzB;AACAD,aAAa,CAAAyC,aAAA,CAAAA,aAAA,IACRxC,cAAc,MACjBuC,UAAU,CAAEvC,cAAc,CAACuC,UAAU,CAAG,GAAG,CAC3CE,OAAO,CAAE,IAAI,EACd,CAAC,CACJ,CACA,MAEF,IAAK,mBAAmB,CACtBtC,cAAc,CAAC,IAAI,CAAC,CACpBE,kBAAkB,2BAAA8B,MAAA,CAA2BV,IAAI,CAACM,WAAW,CAAE,CAAC,CAChE,MAEF,IAAK,mBAAmB,CACtB5B,cAAc,CAAC,KAAK,CAAC,CACrB,GAAIsB,IAAI,CAACiB,MAAM,CAAE,CACfrC,kBAAkB,qBAAA8B,MAAA,CAAqBV,IAAI,CAACiB,MAAM,CAACC,WAAW,WAAS,CAAC,CAC1E,CAAC,IAAM,CACLtC,kBAAkB,CAAC,mBAAmB,CAAC,CACzC,CACA,MAEF,QACEgB,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAEG,IAAI,CAACG,IAAI,CAAC,CACnD,CACF,CAAE,MAAOgB,KAAK,CAAE,CACdvB,OAAO,CAACuB,KAAK,CAAC,kCAAkC,CAAEA,KAAK,CAAC,CAC1D,CACF,CAAC,CAED9B,KAAK,CAACI,OAAO,CAAC2B,OAAO,CAAG,IAAM,CAC5BxB,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC,CACvDzB,cAAc,CAAC,KAAK,CAAC,CACrBQ,kBAAkB,CAAC,2BAA2B,CAAC,CAE/C;AACAU,mBAAmB,CAACG,OAAO,CAAG4B,UAAU,CAAC,IAAM,CAC7CzB,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC,CACzCL,OAAO,CAAC,CAAC,CACX,CAAC,CAAE,IAAI,CAAC,CACV,CAAC,CAEDH,KAAK,CAACI,OAAO,CAAC6B,OAAO,CAAIH,KAAK,EAAK,CACjCvB,OAAO,CAACuB,KAAK,CAAC,kBAAkB,CAAEA,KAAK,CAAC,CACxCvC,kBAAkB,CAAC,kBAAkB,CAAC,CACxC,CAAC,CAEH,CAAE,MAAOuC,KAAK,CAAE,CACdvB,OAAO,CAACuB,KAAK,CAAC,gCAAgC,CAAEA,KAAK,CAAC,CACtDvC,kBAAkB,CAAC,8BAA8B,CAAC,CACpD,CACF,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAA2C,UAAU,CAAGxD,WAAW,CAAC,IAAM,CACnC,GAAIuB,mBAAmB,CAACG,OAAO,CAAE,CAC/B+B,YAAY,CAAClC,mBAAmB,CAACG,OAAO,CAAC,CAC3C,CAEA,GAAIF,gBAAgB,CAACE,OAAO,CAAE,CAC5BgC,aAAa,CAAClC,gBAAgB,CAACE,OAAO,CAAC,CACzC,CAEA,GAAIJ,KAAK,CAACI,OAAO,CAAE,CACjBJ,KAAK,CAACI,OAAO,CAACiC,KAAK,CAAC,CAAC,CACrBrC,KAAK,CAACI,OAAO,CAAG,IAAI,CACtB,CAEArB,cAAc,CAAC,KAAK,CAAC,CACvB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAuD,SAAS,CAAG5D,WAAW,CAAE6D,SAAS,EAAK,CAC3C,GAAIvC,KAAK,CAACI,OAAO,EAAIJ,KAAK,CAACI,OAAO,CAACoC,UAAU,GAAKnC,SAAS,CAACoC,IAAI,CAAE,CAChE,KAAM,CAAAC,OAAO,CAAG,CACd5B,IAAI,CAAE,OAAO,CACb6B,KAAK,CAAEJ,SACT,CAAC,CACDvC,KAAK,CAACI,OAAO,CAACwC,IAAI,CAAChC,IAAI,CAACiC,SAAS,CAACH,OAAO,CAAC,CAAC,CAC7C,CACF,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAI,cAAc,CAAGpE,WAAW,CAAEqE,cAAc,EAAK,CACrD,GAAI/C,KAAK,CAACI,OAAO,EAAIJ,KAAK,CAACI,OAAO,CAACoC,UAAU,GAAKnC,SAAS,CAACoC,IAAI,CAAE,CAChE,KAAM,CAAAC,OAAO,CAAG,CACd5B,IAAI,CAAE,iBAAiB,CACvBG,WAAW,CAAE8B,cACf,CAAC,CACD/C,KAAK,CAACI,OAAO,CAACwC,IAAI,CAAChC,IAAI,CAACiC,SAAS,CAACH,OAAO,CAAC,CAAC,CAC3C7C,aAAa,CAACkD,cAAc,CAAC,CAC/B,CACF,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAC,aAAa,CAAGtE,WAAW,CAAC,IAAM,CACtC,GAAIsB,KAAK,CAACI,OAAO,EAAIJ,KAAK,CAACI,OAAO,CAACoC,UAAU,GAAKnC,SAAS,CAACoC,IAAI,CAAE,CAChE,KAAM,CAAAC,OAAO,CAAG,CACd5B,IAAI,CAAE,gBACR,CAAC,CACDd,KAAK,CAACI,OAAO,CAACwC,IAAI,CAAChC,IAAI,CAACiC,SAAS,CAACH,OAAO,CAAC,CAAC,CAC7C,CACF,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAO,iBAAiB,CAAGvE,WAAW,CAAC,SAACwE,SAAS,CAAqB,IAAnB,CAAAC,QAAQ,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,GAAG,CAC9D,GAAIlD,gBAAgB,CAACE,OAAO,CAAE,CAC5BgC,aAAa,CAAClC,gBAAgB,CAACE,OAAO,CAAC,CACzC,CAEAF,gBAAgB,CAACE,OAAO,CAAGmD,WAAW,CAAC,IAAM,CAC3C,GAAIL,SAAS,CAAC9C,OAAO,EAAItB,WAAW,CAAE,CACpC,KAAM,CAAA0E,QAAQ,CAAGN,SAAS,CAAC9C,OAAO,CAACqD,aAAa,CAAC,CAAC,CAClD,GAAID,QAAQ,CAAE,CACZjD,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC,CACvC8B,SAAS,CAACkB,QAAQ,CAAC,CACrB,CACF,CACF,CAAC,CAAEL,QAAQ,CAAC,CACd,CAAC,CAAE,CAACrE,WAAW,CAAEwD,SAAS,CAAC,CAAC,CAE5B,KAAM,CAAAoB,gBAAgB,CAAGhF,WAAW,CAAC,IAAM,CACzC,GAAIwB,gBAAgB,CAACE,OAAO,CAAE,CAC5BgC,aAAa,CAAClC,gBAAgB,CAACE,OAAO,CAAC,CACvCF,gBAAgB,CAACE,OAAO,CAAG,IAAI,CACjC,CACF,CAAC,CAAE,EAAE,CAAC,CAEN;AACAzB,SAAS,CAAC,IAAM,CACdwB,OAAO,CAAC,CAAC,CAET,MAAO,IAAM,CACX+B,UAAU,CAAC,CAAC,CACd,CAAC,CACH,CAAC,CAAE,CAAC/B,OAAO,CAAE+B,UAAU,CAAC,CAAC,CAEzB,MAAO,CACLpD,WAAW,CACXE,UAAU,CACVE,cAAc,CACdY,iBAAiB,CACjBV,WAAW,CACXE,eAAe,CACfE,cAAc,CACdE,WAAW,CACXE,UAAU,CACVO,OAAO,CACP+B,UAAU,CACVI,SAAS,CACTQ,cAAc,CACdE,aAAa,CACbC,iBAAiB,CACjBS,gBACF,CAAC,CACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}