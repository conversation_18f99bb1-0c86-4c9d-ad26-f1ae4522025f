{"ast": null, "code": "/**\n * @license lucide-react v0.523.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10.17 4.193a2 2 0 0 1 3.666.013\",\n  key: \"pltmmw\"\n}], [\"path\", {\n  d: \"M14 21h2\",\n  key: \"v4qezv\"\n}], [\"path\", {\n  d: \"m15.874 7.743 1 1.732\",\n  key: \"10m0iw\"\n}], [\"path\", {\n  d: \"m18.849 12.952 1 1.732\",\n  key: \"zadnam\"\n}], [\"path\", {\n  d: \"M21.824 18.18a2 2 0 0 1-1.835 2.824\",\n  key: \"fvwuk4\"\n}], [\"path\", {\n  d: \"M4.024 21a2 2 0 0 1-1.839-2.839\",\n  key: \"1e1kah\"\n}], [\"path\", {\n  d: \"m5.136 12.952-1 1.732\",\n  key: \"1u4ldi\"\n}], [\"path\", {\n  d: \"M8 21h2\",\n  key: \"i9zjee\"\n}], [\"path\", {\n  d: \"m8.102 7.743-1 1.732\",\n  key: \"1zzo4u\"\n}]];\nconst TriangleDashed = createLucideIcon(\"triangle-dashed\", __iconNode);\nexport { __iconNode, TriangleDashed as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "TriangleDashed", "createLucideIcon"], "sources": ["D:\\ASL\\training-frontend\\node_modules\\lucide-react\\src\\icons\\triangle-dashed.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M10.17 4.193a2 2 0 0 1 3.666.013', key: 'pltmmw' }],\n  ['path', { d: 'M14 21h2', key: 'v4qezv' }],\n  ['path', { d: 'm15.874 7.743 1 1.732', key: '10m0iw' }],\n  ['path', { d: 'm18.849 12.952 1 1.732', key: 'zadnam' }],\n  ['path', { d: 'M21.824 18.18a2 2 0 0 1-1.835 2.824', key: 'fvwuk4' }],\n  ['path', { d: 'M4.024 21a2 2 0 0 1-1.839-2.839', key: '1e1kah' }],\n  ['path', { d: 'm5.136 12.952-1 1.732', key: '1u4ldi' }],\n  ['path', { d: 'M8 21h2', key: 'i9zjee' }],\n  ['path', { d: 'm8.102 7.743-1 1.732', key: '1zzo4u' }],\n];\n\n/**\n * @component @name TriangleDashed\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAuMTcgNC4xOTNhMiAyIDAgMCAxIDMuNjY2LjAxMyIgLz4KICA8cGF0aCBkPSJNMTQgMjFoMiIgLz4KICA8cGF0aCBkPSJtMTUuODc0IDcuNzQzIDEgMS43MzIiIC8+CiAgPHBhdGggZD0ibTE4Ljg0OSAxMi45NTIgMSAxLjczMiIgLz4KICA8cGF0aCBkPSJNMjEuODI0IDE4LjE4YTIgMiAwIDAgMS0xLjgzNSAyLjgyNCIgLz4KICA8cGF0aCBkPSJNNC4wMjQgMjFhMiAyIDAgMCAxLTEuODM5LTIuODM5IiAvPgogIDxwYXRoIGQ9Im01LjEzNiAxMi45NTItMSAxLjczMiIgLz4KICA8cGF0aCBkPSJNOCAyMWgyIiAvPgogIDxwYXRoIGQ9Im04LjEwMiA3Ljc0My0xIDEuNzMyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/triangle-dashed\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TriangleDashed = createLucideIcon('triangle-dashed', __iconNode);\n\nexport default TriangleDashed;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,kCAAoC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjE,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,uBAAyB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACtD,CAAC,MAAQ;EAAED,CAAA,EAAG,wBAA0B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAED,CAAA,EAAG,qCAAuC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACpE,CAAC,MAAQ;EAAED,CAAA,EAAG,iCAAmC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChE,CAAC,MAAQ;EAAED,CAAA,EAAG,uBAAyB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACtD,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,sBAAwB;EAAAC,GAAA,EAAK;AAAU,GACvD;AAaM,MAAAC,cAAA,GAAiBC,gBAAiB,oBAAmBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}