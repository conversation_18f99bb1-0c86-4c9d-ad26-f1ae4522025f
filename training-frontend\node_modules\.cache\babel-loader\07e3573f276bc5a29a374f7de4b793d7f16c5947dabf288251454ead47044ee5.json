{"ast": null, "code": "var _jsxFileName = \"D:\\\\ASL\\\\training-frontend\\\\src\\\\components\\\\TrainingPage.js\",\n  _s = $RefreshSig$();\nimport { useState, useRef, useCallback } from 'react';\nimport styled from 'styled-components';\nimport Webcam from 'react-webcam';\nimport { Brain, Camera, ArrowLeft, Play, Square, Download, Zap, Eye, Target } from 'lucide-react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst TrainingContainer = styled.div`\n  min-height: 100vh;\n  background: var(--bg-primary);\n  position: relative;\n  overflow-x: hidden;\n\n  &::before {\n    content: '';\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background:\n      radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),\n      radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.03) 0%, transparent 50%);\n    pointer-events: none;\n    z-index: 0;\n  }\n`;\n_c = TrainingContainer;\nconst Navigation = styled.nav`\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 50;\n  background: var(--bg-glass);\n  backdrop-filter: blur(20px);\n  border-bottom: 1px solid var(--border-neural);\n  padding: var(--space-4) 0;\n  transition: var(--transition-normal);\n`;\n_c2 = Navigation;\nconst NavContainer = styled.div`\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 0 var(--space-6);\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n\n  @media (max-width: 768px) {\n    padding: 0 var(--space-4);\n  }\n`;\n_c3 = NavContainer;\nconst Logo = styled.div`\n  font-family: var(--font-primary);\n  font-size: 1.5rem;\n  font-weight: 700;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n\n  @media (max-width: 768px) {\n    font-size: 1.25rem;\n    gap: var(--space-2);\n  }\n`;\n_c4 = Logo;\nconst LogoIcon = styled.div`\n  width: 40px;\n  height: 40px;\n  background: var(--bg-neural);\n  border-radius: var(--radius-lg);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  box-shadow: var(--shadow-neural);\n\n  @media (max-width: 768px) {\n    width: 36px;\n    height: 36px;\n  }\n`;\n_c5 = LogoIcon;\nconst BackButton = styled.button`\n  background: var(--bg-glass);\n  color: var(--text-secondary);\n  border: 1px solid var(--border-neural);\n  padding: var(--space-3) var(--space-5);\n  border-radius: var(--radius-xl);\n  font-size: 0.9rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: var(--transition-normal);\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  backdrop-filter: blur(10px);\n\n  &:hover {\n    background: var(--primary-50);\n    color: var(--primary-600);\n    border-color: var(--primary-300);\n    transform: translateY(-1px);\n    box-shadow: var(--shadow-lg);\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-2) var(--space-4);\n    font-size: 0.85rem;\n  }\n`;\n_c6 = BackButton;\nconst PageTitle = styled.h1`\n  font-family: var(--font-primary);\n  font-size: 2.5rem;\n  font-weight: 700;\n  background: var(--text-gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  text-align: center;\n  margin-bottom: var(--space-4);\n  letter-spacing: -0.02em;\n\n  @media (max-width: 768px) {\n    font-size: 2rem;\n  }\n`;\n_c7 = PageTitle;\nconst PageSubtitle = styled.p`\n  font-size: 1.125rem;\n  color: var(--text-secondary);\n  text-align: center;\n  margin-bottom: var(--space-16);\n  max-width: 700px;\n  margin-left: auto;\n  margin-right: auto;\n  line-height: 1.6;\n\n  @media (max-width: 768px) {\n    margin-bottom: var(--space-12);\n    font-size: 1rem;\n  }\n`;\n_c8 = PageSubtitle;\nconst StatusBadge = styled.div`\n  display: inline-flex;\n  align-items: center;\n  gap: var(--space-2);\n  background: var(--bg-glass);\n  border: 1px solid var(--border-neural);\n  border-radius: var(--radius-full);\n  padding: var(--space-2) var(--space-4);\n  margin-bottom: var(--space-8);\n  font-size: 0.875rem;\n  font-weight: 500;\n  color: var(--text-accent);\n  backdrop-filter: blur(10px);\n  box-shadow: var(--shadow-glow);\n\n  @media (max-width: 768px) {\n    font-size: 0.8rem;\n    padding: var(--space-2) var(--space-3);\n  }\n`;\n_c9 = StatusBadge;\nconst MainContent = styled.main`\n  padding: var(--space-20) var(--space-4) var(--space-16);\n  max-width: 1200px;\n  margin: 0 auto;\n\n  @media (max-width: 768px) {\n    padding: var(--space-16) var(--space-4) var(--space-12);\n  }\n`;\n_c0 = MainContent;\nconst TrainingGrid = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: var(--space-8);\n  max-width: 1200px;\n  margin: 0 auto var(--space-12);\n\n  @media (max-width: 1024px) {\n    grid-template-columns: 1fr;\n    gap: var(--space-6);\n  }\n`;\n_c1 = TrainingGrid;\nconst CameraSection = styled.div`\n  background: var(--bg-glass);\n  border-radius: var(--radius-2xl);\n  padding: var(--space-10);\n  border: 1px solid var(--border-neural);\n  backdrop-filter: blur(20px);\n  box-shadow: var(--shadow-lg);\n  transition: var(--transition-normal);\n  position: relative;\n  overflow: hidden;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 4px;\n    background: var(--bg-neural);\n    transform: scaleX(0);\n    transition: var(--transition-normal);\n  }\n\n  &:hover {\n    box-shadow: var(--shadow-xl), var(--shadow-glow);\n    border-color: var(--primary-300);\n\n    &::before {\n      transform: scaleX(1);\n    }\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-8);\n  }\n`;\n_c10 = CameraSection;\nconst SectionTitle = styled.h2`\n  font-family: var(--font-primary);\n  font-size: 1.25rem;\n  margin-bottom: var(--space-6);\n  color: var(--text-primary);\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n\n  @media (max-width: 768px) {\n    font-size: 1.125rem;\n    margin-bottom: var(--space-4);\n  }\n`;\n_c11 = SectionTitle;\nconst SectionIcon = styled.div`\n  width: 48px;\n  height: 48px;\n  background: var(--bg-neural);\n  border-radius: var(--radius-lg);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  box-shadow: var(--shadow-neural);\n\n  @media (max-width: 768px) {\n    width: 40px;\n    height: 40px;\n  }\n`;\n_c12 = SectionIcon;\nconst WebcamContainer = styled.div`\n  position: relative;\n  border-radius: var(--radius-2xl);\n  overflow: hidden;\n  background: var(--neural-100);\n  aspect-ratio: 4/3;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: 3px solid var(--border-neural);\n  margin-bottom: var(--space-6);\n  box-shadow: var(--shadow-lg);\n`;\n_c13 = WebcamContainer;\nconst StyledWebcam = styled(Webcam)`\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n`;\n_c14 = StyledWebcam;\nconst RecordingOverlay = styled.div`\n  position: absolute;\n  top: var(--space-4);\n  right: var(--space-4);\n  background: ${props => props.isRecording ? 'var(--error-500)' : 'var(--neural-600)'};\n  color: white;\n  padding: var(--space-3) var(--space-5);\n  border-radius: var(--radius-full);\n  font-size: 0.9rem;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  box-shadow: var(--shadow-lg);\n  animation: ${props => props.isRecording ? 'pulse 1.5s infinite' : 'none'};\n\n  @keyframes pulse {\n    0%, 100% { opacity: 1; transform: scale(1); }\n    50% { opacity: 0.8; transform: scale(1.05); }\n  }\n`;\n_c15 = RecordingOverlay;\nconst SignSection = styled.div`\n  background: var(--bg-primary);\n  border-radius: var(--radius-2xl);\n  padding: var(--space-8);\n  border: 1px solid var(--border-light);\n  box-shadow: var(--shadow-lg);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  transition: all 0.3s ease;\n\n  &:hover {\n    box-shadow: var(--shadow-xl);\n    border-color: var(--primary-200);\n  }\n\n  @media (max-width: 768px) {\n    padding: var(--space-6);\n  }\n`;\n_c16 = SignSection;\nconst SignSelector = styled.select`\n  width: 100%;\n  padding: var(--space-3) var(--space-4);\n  border: 2px solid var(--border-light);\n  border-radius: var(--radius-lg);\n  background: var(--bg-primary);\n  color: var(--text-primary);\n  font-size: 1rem;\n  font-weight: 500;\n  margin-bottom: var(--space-4);\n  cursor: pointer;\n  transition: var(--transition-normal);\n\n  &:focus {\n    outline: none;\n    border-color: var(--primary-500);\n    box-shadow: 0 0 0 3px var(--primary-100);\n  }\n\n  &:hover {\n    border-color: var(--primary-300);\n  }\n\n  option {\n    padding: var(--space-2);\n    background: var(--bg-primary);\n    color: var(--text-primary);\n  }\n`;\n_c17 = SignSelector;\nconst SignDisplay = styled.div`\n  width: 300px;\n  height: 300px;\n  background: var(--primary-50);\n  border-radius: var(--radius-2xl);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: var(--space-6);\n  border: 2px solid var(--primary-200);\n  transition: all 0.3s ease;\n  overflow: hidden;\n\n  img {\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n    border-radius: var(--radius-xl);\n  }\n\n  &:hover {\n    transform: scale(1.02);\n    border-color: var(--primary-300);\n  }\n\n  @media (max-width: 768px) {\n    width: 250px;\n    height: 250px;\n  }\n`;\n_c18 = SignDisplay;\nconst SignName = styled.h3`\n  font-family: var(--font-primary);\n  font-size: 1.5rem;\n  margin-bottom: var(--space-3);\n  color: var(--text-primary);\n  font-weight: 700;\n\n  @media (max-width: 768px) {\n    font-size: 1.25rem;\n  }\n`;\n_c19 = SignName;\nconst SignDescription = styled.p`\n  text-align: center;\n  line-height: 1.6;\n  color: var(--text-secondary);\n  font-size: 0.9rem;\n  font-weight: 400;\n  max-width: 280px;\n`;\n_c20 = SignDescription;\nconst ControlsSection = styled.div`\n  display: flex;\n  justify-content: center;\n  gap: var(--space-4);\n  margin-top: var(--space-8);\n\n  @media (max-width: 768px) {\n    flex-direction: column;\n    align-items: center;\n    gap: var(--space-3);\n  }\n`;\n_c21 = ControlsSection;\nconst ControlButton = styled.button`\n  background: ${props => props.variant === 'primary' ? 'var(--primary-600)' : 'var(--bg-primary)'};\n  border: ${props => props.variant === 'primary' ? 'none' : '1px solid var(--border-medium)'};\n  color: ${props => props.variant === 'primary' ? 'white' : 'var(--text-primary)'};\n  padding: var(--space-3) var(--space-6);\n  border-radius: var(--radius-lg);\n  cursor: pointer;\n  font-size: 0.9rem;\n  font-weight: 600;\n  transition: all 0.2s ease;\n  min-width: 160px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: var(--space-2);\n  box-shadow: ${props => props.variant === 'primary' ? 'var(--shadow-lg)' : 'var(--shadow-sm)'};\n\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: ${props => props.variant === 'primary' ? 'var(--shadow-xl)' : 'var(--shadow-md)'};\n    background: ${props => props.variant === 'primary' ? 'var(--primary-700)' : 'var(--gray-50)'};\n  }\n\n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n    transform: none;\n  }\n\n  @media (max-width: 768px) {\n    width: 100%;\n    max-width: 280px;\n  }\n`;\n_c22 = ControlButton;\nconst StatusMessage = styled.div`\n  text-align: center;\n  margin-top: var(--space-6);\n  padding: var(--space-4) var(--space-6);\n  border-radius: var(--radius-lg);\n  background: ${props => props.type === 'success' ? 'var(--success-500)' : props.type === 'error' ? 'var(--error-500)' : 'var(--primary-600)'};\n  color: white;\n  font-weight: 500;\n  font-size: 0.875rem;\n  max-width: 400px;\n  margin-left: auto;\n  margin-right: auto;\n`;\n_c23 = StatusMessage;\nconst RecordingsSection = styled.div`\n  margin-top: var(--space-16);\n  background: var(--bg-secondary);\n  padding: var(--space-12) var(--space-4);\n  border-radius: var(--radius-2xl);\n  max-width: 1200px;\n  margin-left: auto;\n  margin-right: auto;\n`;\n_c24 = RecordingsSection;\nconst RecordingsTitle = styled.h3`\n  font-family: var(--font-primary);\n  color: var(--text-primary);\n  margin-bottom: var(--space-8);\n  font-size: 1.5rem;\n  font-weight: 600;\n  text-align: center;\n`;\n_c25 = RecordingsTitle;\nconst RecordingsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n  gap: var(--space-6);\n\n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n    gap: var(--space-4);\n  }\n`;\n_c26 = RecordingsGrid;\nconst RecordingCard = styled.div`\n  background: var(--bg-primary);\n  padding: var(--space-6);\n  border-radius: var(--radius-xl);\n  border: 1px solid var(--border-light);\n  box-shadow: var(--shadow-sm);\n  transition: all 0.3s ease;\n\n  &:hover {\n    transform: translateY(-2px);\n    border-color: var(--primary-200);\n    box-shadow: var(--shadow-lg);\n  }\n`;\n_c27 = RecordingCard;\nconst RecordingTitle = styled.p`\n  margin: 0 0 var(--space-2) 0;\n  color: var(--text-primary);\n  font-weight: 600;\n  font-size: 1rem;\n  font-family: var(--font-primary);\n`;\n_c28 = RecordingTitle;\nconst RecordingTime = styled.p`\n  margin: 0 0 var(--space-4) 0;\n  font-size: 0.8rem;\n  color: var(--text-tertiary);\n`;\n_c29 = RecordingTime;\nconst DownloadButton = styled.button`\n  background: var(--primary-600);\n  border: none;\n  border-radius: var(--radius-lg);\n  padding: var(--space-2) var(--space-4);\n  color: white;\n  cursor: pointer;\n  font-size: 0.8rem;\n  font-weight: 500;\n  transition: all 0.2s ease;\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  margin: 0 auto;\n\n  &:hover {\n    background: var(--primary-700);\n    transform: translateY(-1px);\n  }\n`;\n\n// Sign language data with GIFs (matching Streamlit app)\n_c30 = DownloadButton;\nconst signLanguageData = {\n  \"after\": {\n    name: \"After\",\n    gif: \"https://lifeprint.com/asl101/gifs/a/after-over-across.gif\",\n    description: \"Move your dominant hand over and past your non-dominant hand\"\n  },\n  \"airplane\": {\n    name: \"Airplane\",\n    gif: \"https://www.lifeprint.com/asl101/gifs/a/airplane-flying.gif\",\n    description: \"Extend your hand like a plane and move it through the air\"\n  },\n  \"all\": {\n    name: \"All\",\n    gif: \"https://lifeprint.com/asl101/gifs/a/all-whole.gif\",\n    description: \"Circle your dominant hand around your non-dominant hand\"\n  },\n  \"alligator\": {\n    name: \"Alligator\",\n    gif: \"https://lifeprint.com/asl101/gifs/a/alligator.gif\",\n    description: \"Clap your hands together like an alligator's mouth\"\n  },\n  \"animal\": {\n    name: \"Animal\",\n    gif: \"https://www.lifeprint.com/asl101/gifs-animated/animal.gif\",\n    description: \"Place fingertips on chest and move hands back and forth\"\n  },\n  \"any\": {\n    name: \"Any\",\n    gif: \"https://lifeprint.com/asl101/gifs/a/any.gif\",\n    description: \"Point with index finger and twist your wrist\"\n  },\n  \"apple\": {\n    name: \"Apple\",\n    gif: \"https://media.giphy.com/media/l0HlHb4dtZZiMYEA8/giphy.gif\",\n    description: \"Twist your knuckle against your cheek\"\n  },\n  \"arm\": {\n    name: \"Arm\",\n    gif: \"https://th.bing.com/th/id/OIP.KkJLqfbp6XEHiIe-jOUb2AHaEc?w=251&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\n    description: \"Pat your arm with your opposite hand\"\n  },\n  \"aunt\": {\n    name: \"Aunt\",\n    gif: \"https://th.bing.com/th/id/OIP.Yz5UUZdNTrVWXf72we_N6wHaHa?rs=1&pid=ImgDetMain\",\n    description: \"Make an 'A' handshape near your cheek and shake it\"\n  },\n  \"baby\": {\n    name: \"Baby\",\n    gif: \"https://media.giphy.com/media/l0HlHb4dtZZiMYEA8/giphy.gif\",\n    description: \"Rock your arms as if holding a baby\"\n  },\n  \"ball\": {\n    name: \"Ball\",\n    gif: \"https://lifeprint.com/asl101/gifs/b/ball.gif\",\n    description: \"Cup your hands as if holding a ball\"\n  },\n  \"banana\": {\n    name: \"Banana\",\n    gif: \"https://media.giphy.com/media/l0HlHb4dtZZiMYEA8/giphy.gif\",\n    description: \"Peel an imaginary banana with your fingers\"\n  },\n  \"bear\": {\n    name: \"Bear\",\n    gif: \"https://lifeprint.com/asl101/gifs/b/bear.gif\",\n    description: \"Cross your arms and scratch like a bear\"\n  },\n  \"beautiful\": {\n    name: \"Beautiful\",\n    gif: \"https://media.giphy.com/media/l0HlHb4dtZZiMYEA8/giphy.gif\",\n    description: \"Circle your face with your hand and close it into a fist\"\n  },\n  \"bed\": {\n    name: \"Bed\",\n    gif: \"https://lifeprint.com/asl101/gifs/b/bed.gif\",\n    description: \"Rest your head on your hands as if sleeping\"\n  },\n  \"bee\": {\n    name: \"Bee\",\n    gif: \"https://lifeprint.com/asl101/gifs/b/bee.gif\",\n    description: \"Pinch your cheek and brush away as if swatting a bee\"\n  },\n  \"bird\": {\n    name: \"Bird\",\n    gif: \"https://lifeprint.com/asl101/gifs/b/bird.gif\",\n    description: \"Pinch your fingers together near your mouth like a beak\"\n  },\n  \"black\": {\n    name: \"Black\",\n    gif: \"https://lifeprint.com/asl101/gifs/b/black.gif\",\n    description: \"Draw your index finger across your forehead\"\n  },\n  \"blue\": {\n    name: \"Blue\",\n    gif: \"https://lifeprint.com/asl101/gifs/b/blue.gif\",\n    description: \"Shake a 'B' handshape\"\n  },\n  \"book\": {\n    name: \"Book\",\n    gif: \"https://lifeprint.com/asl101/gifs/b/book.gif\",\n    description: \"Open your hands like opening a book\"\n  },\n  \"boy\": {\n    name: \"Boy\",\n    gif: \"https://lifeprint.com/asl101/gifs/b/boy.gif\",\n    description: \"Snap your fingers at your forehead\"\n  },\n  \"brother\": {\n    name: \"Brother\",\n    gif: \"https://lifeprint.com/asl101/gifs/b/brother.gif\",\n    description: \"Make an 'L' shape and point to your forehead, then point forward\"\n  },\n  \"brown\": {\n    name: \"Brown\",\n    gif: \"https://lifeprint.com/asl101/gifs/b/brown.gif\",\n    description: \"Slide your index finger down your cheek\"\n  },\n  \"bug\": {\n    name: \"Bug\",\n    gif: \"https://lifeprint.com/asl101/gifs/b/bug.gif\",\n    description: \"Pinch your nose with your thumb and index finger\"\n  },\n  \"butterfly\": {\n    name: \"Butterfly\",\n    gif: \"https://lifeprint.com/asl101/gifs/b/butterfly.gif\",\n    description: \"Cross your thumbs and flutter your fingers like wings\"\n  },\n  \"car\": {\n    name: \"Car\",\n    gif: \"https://lifeprint.com/asl101/gifs/c/car.gif\",\n    description: \"Pretend to steer a car with both hands\"\n  },\n  \"cat\": {\n    name: \"Cat\",\n    gif: \"https://lifeprint.com/asl101/gifs/c/cat.gif\",\n    description: \"Pinch your cheek and pull out like whiskers\"\n  },\n  \"chair\": {\n    name: \"Chair\",\n    gif: \"https://lifeprint.com/asl101/gifs/c/chair.gif\",\n    description: \"Tap your fingers on your other hand like sitting\"\n  },\n  \"clean\": {\n    name: \"Clean\",\n    gif: \"https://lifeprint.com/asl101/gifs/c/clean.gif\",\n    description: \"Wipe one palm with the other\"\n  },\n  \"cold\": {\n    name: \"Cold\",\n    gif: \"https://media.giphy.com/media/l0HlHb4dtZZiMYEA8/giphy.gif\",\n    description: \"Shiver with both hands in fists\"\n  },\n  \"cow\": {\n    name: \"Cow\",\n    gif: \"https://lifeprint.com/asl101/gifs/c/cow.gif\",\n    description: \"Twist your thumb at your temple like a horn\"\n  },\n  \"cry\": {\n    name: \"Cry\",\n    gif: \"https://lifeprint.com/asl101/gifs/c/cry.gif\",\n    description: \"Draw tears down your cheeks with your index fingers\"\n  },\n  \"cute\": {\n    name: \"Cute\",\n    gif: \"https://lifeprint.com/asl101/gifs/c/cute-sugar.gif\",\n    description: \"Brush your chin with your fingers\"\n  },\n  \"dad\": {\n    name: \"Dad\",\n    gif: \"https://media.giphy.com/media/l0MYQcLKwtl5v6H1S/giphy.gif\",\n    description: \"Tap your forehead with your thumb\"\n  },\n  \"dance\": {\n    name: \"Dance\",\n    gif: \"https://www.bing.com/th/id/OGC.********************************?pid=1.7&rurl=https%3a%2f%2fmedia.giphy.com%2fmedia%2f3o7TKMspYQjQTbOz2U%2fgiphy.gif&ehk=h%2bdBHCxuoOT89ovSy5uTk6MCL9acaBEV6ld9lrVDRF4%3d\",\n    description: \"Swing your fingers over your palm like dancing\"\n  },\n  \"dirty\": {\n    name: \"Dirty\",\n    gif: \"https://th.bing.com/th/id/OIP.wRA7r1OPPUuEoLL4Hds9jAHaHa?rs=1&pid=ImgDetMain\",\n    description: \"Wiggle your fingers under your chin\"\n  },\n  \"dog\": {\n    name: \"Dog\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=uVshGsOHXgldoQ&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fd%2fdog1.gif&ehk=Xnfrvg0xwy1u%2fae9vWdKYMT25%2bF6qoteW2FThCbVrOA%3d&risl=&pid=ImgRaw&r=0\",\n    description: \"Pat your leg and snap your fingers\"\n  },\n  \"eat\": {\n    name: \"Eat\",\n    gif: \"https://lifeprint.com/asl101/gifs/e/eat.gif\",\n    description: \"Bring your fingers to your mouth as if eating\"\n  },\n  \"elephant\": {\n    name: \"Elephant\",\n    gif: \"https://lifeprint.com/asl101/gifs/e/elephant.gif\",\n    description: \"Trace the shape of an elephant's trunk with your hand\"\n  },\n  \"fish\": {\n    name: \"Fish\",\n    gif: \"https://lifeprint.com/asl101/gifs/f/fish.gif\",\n    description: \"Move your hand like a fish swimming\"\n  },\n  \"flower\": {\n    name: \"Flower\",\n    gif: \"https://lifeprint.com/asl101/gifs/f/flower.gif\",\n    description: \"Touch your nose with your fingertips\"\n  },\n  \"friend\": {\n    name: \"Friend\",\n    gif: \"https://lifeprint.com/asl101/gifs/f/friend.gif\",\n    description: \"Hook your index fingers together\"\n  },\n  \"girl\": {\n    name: \"Girl\",\n    gif: \"https://lifeprint.com/asl101/gifs/g/girl.gif\",\n    description: \"Trace your jawline with your thumb\"\n  },\n  \"go\": {\n    name: \"Go\",\n    gif: \"https://lifeprint.com/asl101/gifs/g/go.gif\",\n    description: \"Point both index fingers forward and bend them\"\n  },\n  \"good\": {\n    name: \"Good\",\n    gif: \"https://lifeprint.com/asl101/gifs/g/good.gif\",\n    description: \"Touch your chin and move your hand forward\"\n  },\n  \"green\": {\n    name: \"Green\",\n    gif: \"https://lifeprint.com/asl101/gifs/g/green.gif\",\n    description: \"Shake a 'G' handshape\"\n  },\n  \"hair\": {\n    name: \"Hair\",\n    gif: \"https://www.lifeprint.com/asl101/gifs/h/hair-g-version.gif\",\n    description: \"Pinch a strand of your hair\"\n  },\n  \"happy\": {\n    name: \"Happy\",\n    gif: \"https://media0.giphy.com/media/3o7TKFpahYpUp4g0N2/giphy.gif?cid=790b7611bca188a92e2e759876756ef62ba95b7cdd337c77&rid=giphy.gif&ct=g\",\n    description: \"Brush your chest upward with both hands\"\n  },\n  \"hello\": {\n    name: \"Hello\",\n    gif: \"https://media.giphy.com/media/3o7TKNKOfKlIhbD3gY/giphy.gif\",\n    description: \"Wave your hand from side to side with palm facing forward\"\n  },\n  \"home\": {\n    name: \"Home\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=%2bnBd%2foQjxnoPfg&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fh%2fhome-2.gif&ehk=7yD%2f%2fh6JN1Y4D4BOrUjgKW4Jccy2Y4GVYLf%2fzyk%2b5YY%3d&risl=&pid=ImgRaw&r=0\",\n    description: \"Touch your mouth then your cheek\"\n  },\n  \"horse\": {\n    name: \"Horse\",\n    gif: \"https://media.giphy.com/media/l0HlM5HffraiQaHUk/giphy.gif\",\n    description: \"Extend your thumb and fingers at your temple like ears\"\n  },\n  \"hot\": {\n    name: \"Hot\",\n    gif: \"https://media.giphy.com/media/3o6Zt99k5aDok347bG/giphy.gif\",\n    description: \"Touch your mouth and quickly move your hand away\"\n  },\n  \"hungry\": {\n    name: \"Hungry\",\n    gif: \"https://media.giphy.com/media/l3vR0xkdFEz4tnfTq/giphy.gif\",\n    description: \"Move your hand down your chest like food going down\"\n  },\n  \"jump\": {\n    name: \"Jump\",\n    gif: \"https://lifeprint.com/asl101/gifs-animated/jump.gif\",\n    description: \"Bounce your fingers on your palm\"\n  },\n  \"like\": {\n    name: \"Like\",\n    gif: \"https://lifeprint.com/asl101/gifs/l/like.gif\",\n    description: \"Pull your thumb and middle finger from your chest\"\n  },\n  \"look\": {\n    name: \"Look\",\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=pYhzip7LqNs7qw&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fl%2flook-at-1.gif&ehk=rFJ7dBrMGFDK0nHLzrOPAzROVE7yqyDEcb%2btLqKqYOI%3d&risl=&pid=ImgRaw&r=0\",\n    description: \"Point your fingers from your eyes forward\"\n  },\n  \"love\": {\n    name: \"Love\",\n    gif: \"https://lifeprint.com/asl101/gifs/l/love.gif\",\n    description: \"Cross your arms over your chest\"\n  },\n  \"mom\": {\n    name: \"Mom\",\n    gif: \"https://media.giphy.com/media/l0MYQcLKwtl5v6H1S/giphy.gif\",\n    description: \"Tap your chin with your thumb\"\n  },\n  \"more\": {\n    name: \"More\",\n    gif: \"https://lifeprint.com/asl101/gifs/m/more.gif\",\n    description: \"Tap your fingertips together\"\n  },\n  \"no\": {\n    name: \"No\",\n    gif: \"https://lifeprint.com/asl101/gifs/n/no.gif\",\n    description: \"Snap your fingers together\"\n  },\n  \"orange\": {\n    name: \"Orange\",\n    gif: \"https://lifeprint.com/asl101/gifs/o/orange.gif\",\n    description: \"Squeeze your hand at your mouth like squeezing an orange\"\n  },\n  \"please\": {\n    name: \"Please\",\n    gif: \"https://lifeprint.com/asl101/gifs/p/please.gif\",\n    description: \"Rub your chest in a circular motion\"\n  },\n  \"red\": {\n    name: \"Red\",\n    gif: \"https://lifeprint.com/asl101/gifs/r/red.gif\",\n    description: \"Brush your lips with your index finger\"\n  },\n  \"run\": {\n    name: \"Run\",\n    gif: \"https://lifeprint.com/asl101/gifs/r/run.gif\",\n    description: \"Hook your thumbs and wiggle your fingers\"\n  },\n  \"sad\": {\n    name: \"Sad\",\n    gif: \"https://lifeprint.com/asl101/gifs/s/sad.gif\",\n    description: \"Drop both hands down your face\"\n  },\n  \"see\": {\n    name: \"See\",\n    gif: \"https://lifeprint.com/asl101/gifs/l/look-at-2.gif\",\n    description: \"Point from your eyes forward\"\n  },\n  \"sleep\": {\n    name: \"Sleep\",\n    gif: \"https://media4.giphy.com/media/3o7TKnRuBdakLslcaI/200.gif?cid=790b76110d8f185a9713f36dd65a0df801576e01b403c95c&rid=200.gif&ct=g\",\n    description: \"Rest your head on your hands\"\n  },\n  \"sorry\": {\n    name: \"Sorry\",\n    gif: \"https://lifeprint.com/asl101/gifs/s/sorry.gif\",\n    description: \"Rub your fist in a circle on your chest\"\n  },\n  \"thank\": {\n    name: \"Thank You\",\n    gif: \"https://lifeprint.com/asl101/gifs/t/thank-you.gif\",\n    description: \"Touch your chin and move your hand forward\"\n  },\n  \"water\": {\n    name: \"Water\",\n    gif: \"https://lifeprint.com/asl101/gifs/w/water.gif\",\n    description: \"Tap your chin with a 'W' handshape\"\n  },\n  \"white\": {\n    name: \"White\",\n    gif: \"https://lifeprint.com/asl101/gifs/w/white.gif\",\n    description: \"Touch your chest and pull your hand away\"\n  },\n  \"yellow\": {\n    name: \"Yellow\",\n    gif: \"https://lifeprint.com/asl101/gifs/y/yellow.gif\",\n    description: \"Shake a 'Y' handshape\"\n  },\n  \"yes\": {\n    name: \"Yes\",\n    gif: \"https://media.tenor.com/oYIirlyIih0AAAAC/yes-asl.gif\",\n    description: \"Nod your fist up and down\"\n  }\n};\nconst TrainingPage = ({\n  onBackToHome\n}) => {\n  _s();\n  const [isRecording, setIsRecording] = useState(false);\n  const [currentSign, setCurrentSign] = useState('hello');\n  const [status, setStatus] = useState('');\n  const [recordedVideos, setRecordedVideos] = useState([]);\n  const webcamRef = useRef(null);\n  const mediaRecorderRef = useRef(null);\n  const recordedChunksRef = useRef([]);\n  const handleSignChange = useCallback(event => {\n    setCurrentSign(event.target.value);\n  }, []);\n  const startRecording = useCallback(() => {\n    if (!webcamRef.current) {\n      setStatus('Camera not available');\n      return;\n    }\n    mediaRecorderRef.current = new MediaRecorder(webcamRef.current.stream, {\n      mimeType: 'video/webm'\n    });\n    mediaRecorderRef.current.ondataavailable = event => {\n      if (event.data.size > 0) {\n        recordedChunksRef.current.push(event.data);\n      }\n    };\n    mediaRecorderRef.current.onstop = () => {\n      const blob = new Blob(recordedChunksRef.current, {\n        type: 'video/webm'\n      });\n      const url = URL.createObjectURL(blob);\n      const timestamp = new Date().toISOString();\n      setRecordedVideos(prev => [...prev, {\n        id: timestamp,\n        url,\n        sign: signLanguageData[currentSign].name,\n        timestamp\n      }]);\n      recordedChunksRef.current = [];\n      setStatus('Recording saved successfully!');\n    };\n    mediaRecorderRef.current.start();\n    setIsRecording(true);\n    setStatus('Recording started...');\n  }, [currentSign]);\n  const stopRecording = useCallback(() => {\n    if (mediaRecorderRef.current && isRecording) {\n      mediaRecorderRef.current.stop();\n      setIsRecording(false);\n      setStatus('Processing recording...');\n    }\n  }, [isRecording]);\n  const downloadRecording = video => {\n    const a = document.createElement('a');\n    a.href = video.url;\n    a.download = `sign_${video.sign}_${video.timestamp}.webm`;\n    a.click();\n  };\n  return /*#__PURE__*/_jsxDEV(TrainingContainer, {\n    children: [/*#__PURE__*/_jsxDEV(Navigation, {\n      children: /*#__PURE__*/_jsxDEV(NavContainer, {\n        children: [/*#__PURE__*/_jsxDEV(Logo, {\n          children: [/*#__PURE__*/_jsxDEV(LogoIcon, {\n            children: /*#__PURE__*/_jsxDEV(Brain, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1025,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1024,\n            columnNumber: 13\n          }, this), \"ASL Neural\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1023,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(BackButton, {\n          onClick: onBackToHome,\n          children: [/*#__PURE__*/_jsxDEV(ArrowLeft, {\n            size: 18\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1030,\n            columnNumber: 13\n          }, this), \"Back to Home\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1029,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1022,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1021,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(MainContent, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          marginBottom: 'var(--space-12)'\n        },\n        children: /*#__PURE__*/_jsxDEV(StatusBadge, {\n          children: [/*#__PURE__*/_jsxDEV(Eye, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1039,\n            columnNumber: 13\n          }, this), \"Neural Vision Active\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1038,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1037,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(PageTitle, {\n        children: \"AI Training Session\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1044,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(PageSubtitle, {\n        children: \"Experience real-time neural network analysis as our AI learns from your sign language practice\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1045,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TrainingGrid, {\n        children: [/*#__PURE__*/_jsxDEV(CameraSection, {\n          children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n            children: [/*#__PURE__*/_jsxDEV(SectionIcon, {\n              children: /*#__PURE__*/_jsxDEV(Camera, {\n                size: 24\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1053,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1052,\n              columnNumber: 15\n            }, this), \"Neural Vision Feed\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1051,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(WebcamContainer, {\n            children: [/*#__PURE__*/_jsxDEV(StyledWebcam, {\n              ref: webcamRef,\n              audio: false,\n              screenshotFormat: \"image/jpeg\",\n              videoConstraints: {\n                width: 640,\n                height: 480,\n                facingMode: \"user\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1058,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(RecordingOverlay, {\n              isRecording: isRecording,\n              children: isRecording ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    width: '8px',\n                    height: '8px',\n                    borderRadius: '50%',\n                    backgroundColor: 'white',\n                    marginRight: '4px'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1071,\n                  columnNumber: 21\n                }, this), \"Recording\"]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Eye, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1082,\n                  columnNumber: 21\n                }, this), \"Ready\"]\n              }, void 0, true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1068,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1057,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1050,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SignSection, {\n          children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n            children: [/*#__PURE__*/_jsxDEV(SectionIcon, {\n              children: /*#__PURE__*/_jsxDEV(Target, {\n                size: 24\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1093,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1092,\n              columnNumber: 15\n            }, this), \"Select a Sign\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1091,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SignSelector, {\n            value: currentSign,\n            onChange: handleSignChange,\n            disabled: isRecording,\n            children: Object.keys(signLanguageData).map(signKey => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: signKey,\n              children: signLanguageData[signKey].name\n            }, signKey, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1103,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1097,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SignDisplay, {\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: signLanguageData[currentSign].gif,\n              alt: signLanguageData[currentSign].name,\n              onError: e => {\n                e.target.style.display = 'none';\n                e.target.nextSibling.style.display = 'flex';\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1109,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'none',\n                fontSize: '3rem'\n              },\n              children: \"\\uD83D\\uDCF7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1117,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1108,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SignName, {\n            children: signLanguageData[currentSign].name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1121,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SignDescription, {\n            children: signLanguageData[currentSign].description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1122,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1090,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1049,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ControlsSection, {\n        children: /*#__PURE__*/_jsxDEV(ControlButton, {\n          variant: \"primary\",\n          onClick: isRecording ? stopRecording : startRecording,\n          children: isRecording ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Square, {\n              size: 18\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1135,\n              columnNumber: 17\n            }, this), \"Stop Neural Recording\"]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Play, {\n              size: 18\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1140,\n              columnNumber: 17\n            }, this), \"Start Neural Recording\"]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1129,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1128,\n        columnNumber: 9\n      }, this), status && /*#__PURE__*/_jsxDEV(StatusMessage, {\n        type: status.includes('error') ? 'error' : status.includes('success') ? 'success' : 'info',\n        children: status\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1148,\n        columnNumber: 11\n      }, this), recordedVideos.length > 0 && /*#__PURE__*/_jsxDEV(RecordingsSection, {\n        children: [/*#__PURE__*/_jsxDEV(RecordingsTitle, {\n          children: \"Your Practice Recordings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1155,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(RecordingsGrid, {\n          children: recordedVideos.map(video => /*#__PURE__*/_jsxDEV(RecordingCard, {\n            children: [/*#__PURE__*/_jsxDEV(RecordingTitle, {\n              children: video.sign\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1159,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(RecordingTime, {\n              children: new Date(video.timestamp).toLocaleString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1160,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(DownloadButton, {\n              onClick: () => downloadRecording(video),\n              children: [/*#__PURE__*/_jsxDEV(Download, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1164,\n                columnNumber: 21\n              }, this), \"Download\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1163,\n              columnNumber: 19\n            }, this)]\n          }, video.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1158,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1156,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1154,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1036,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1020,\n    columnNumber: 5\n  }, this);\n};\n_s(TrainingPage, \"fPZSFOXPGgAcIoV542qnBMhSzxk=\");\n_c31 = TrainingPage;\nexport default TrainingPage;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22, _c23, _c24, _c25, _c26, _c27, _c28, _c29, _c30, _c31;\n$RefreshReg$(_c, \"TrainingContainer\");\n$RefreshReg$(_c2, \"Navigation\");\n$RefreshReg$(_c3, \"NavContainer\");\n$RefreshReg$(_c4, \"Logo\");\n$RefreshReg$(_c5, \"LogoIcon\");\n$RefreshReg$(_c6, \"BackButton\");\n$RefreshReg$(_c7, \"PageTitle\");\n$RefreshReg$(_c8, \"PageSubtitle\");\n$RefreshReg$(_c9, \"StatusBadge\");\n$RefreshReg$(_c0, \"MainContent\");\n$RefreshReg$(_c1, \"TrainingGrid\");\n$RefreshReg$(_c10, \"CameraSection\");\n$RefreshReg$(_c11, \"SectionTitle\");\n$RefreshReg$(_c12, \"SectionIcon\");\n$RefreshReg$(_c13, \"WebcamContainer\");\n$RefreshReg$(_c14, \"StyledWebcam\");\n$RefreshReg$(_c15, \"RecordingOverlay\");\n$RefreshReg$(_c16, \"SignSection\");\n$RefreshReg$(_c17, \"SignSelector\");\n$RefreshReg$(_c18, \"SignDisplay\");\n$RefreshReg$(_c19, \"SignName\");\n$RefreshReg$(_c20, \"SignDescription\");\n$RefreshReg$(_c21, \"ControlsSection\");\n$RefreshReg$(_c22, \"ControlButton\");\n$RefreshReg$(_c23, \"StatusMessage\");\n$RefreshReg$(_c24, \"RecordingsSection\");\n$RefreshReg$(_c25, \"RecordingsTitle\");\n$RefreshReg$(_c26, \"RecordingsGrid\");\n$RefreshReg$(_c27, \"RecordingCard\");\n$RefreshReg$(_c28, \"RecordingTitle\");\n$RefreshReg$(_c29, \"RecordingTime\");\n$RefreshReg$(_c30, \"DownloadButton\");\n$RefreshReg$(_c31, \"TrainingPage\");", "map": {"version": 3, "names": ["useState", "useRef", "useCallback", "styled", "Webcam", "Brain", "Camera", "ArrowLeft", "Play", "Square", "Download", "Zap", "Eye", "Target", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "TrainingContainer", "div", "_c", "Navigation", "nav", "_c2", "NavContainer", "_c3", "Logo", "_c4", "LogoIcon", "_c5", "BackButton", "button", "_c6", "Page<PERSON><PERSON>le", "h1", "_c7", "PageSubtitle", "p", "_c8", "StatusBadge", "_c9", "MainContent", "main", "_c0", "TrainingGrid", "_c1", "CameraSection", "_c10", "SectionTitle", "h2", "_c11", "SectionIcon", "_c12", "WebcamContainer", "_c13", "StyledWebcam", "_c14", "RecordingOverlay", "props", "isRecording", "_c15", "SignSection", "_c16", "SignSelector", "select", "_c17", "SignDisplay", "_c18", "SignName", "h3", "_c19", "SignDescription", "_c20", "ControlsSection", "_c21", "ControlButton", "variant", "_c22", "StatusMessage", "type", "_c23", "RecordingsSection", "_c24", "RecordingsTitle", "_c25", "RecordingsGrid", "_c26", "RecordingCard", "_c27", "RecordingTitle", "_c28", "RecordingTime", "_c29", "DownloadButton", "_c30", "signLanguageData", "name", "gif", "description", "TrainingPage", "onBackToHome", "_s", "setIsRecording", "currentSign", "setCurrentSign", "status", "setStatus", "recordedVideos", "setRecordedVideos", "webcamRef", "mediaRecorderRef", "recordedChunksRef", "handleSignChange", "event", "target", "value", "startRecording", "current", "MediaRecorder", "stream", "mimeType", "ondataavailable", "data", "size", "push", "onstop", "blob", "Blob", "url", "URL", "createObjectURL", "timestamp", "Date", "toISOString", "prev", "id", "sign", "start", "stopRecording", "stop", "downloadRecording", "video", "a", "document", "createElement", "href", "download", "click", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "style", "textAlign", "marginBottom", "ref", "audio", "screenshotFormat", "videoConstraints", "width", "height", "facingMode", "borderRadius", "backgroundColor", "marginRight", "onChange", "disabled", "Object", "keys", "map", "sign<PERSON><PERSON>", "src", "alt", "onError", "e", "display", "nextS<PERSON>ling", "fontSize", "includes", "length", "toLocaleString", "_c31", "$RefreshReg$"], "sources": ["D:/ASL/training-frontend/src/components/TrainingPage.js"], "sourcesContent": ["import { useState, useRef, useCallback } from 'react';\r\nimport styled from 'styled-components';\r\nimport Webcam from 'react-webcam';\r\nimport {\r\n  Brain,\r\n  Camera,\r\n  ArrowLeft,\r\n  Play,\r\n  Square,\r\n  Download,\r\n  Zap,\r\n  Eye,\r\n  Target\r\n} from 'lucide-react';\r\n\r\nconst TrainingContainer = styled.div`\r\n  min-height: 100vh;\r\n  background: var(--bg-primary);\r\n  position: relative;\r\n  overflow-x: hidden;\r\n\r\n  &::before {\r\n    content: '';\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background:\r\n      radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),\r\n      radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.03) 0%, transparent 50%);\r\n    pointer-events: none;\r\n    z-index: 0;\r\n  }\r\n`;\r\n\r\nconst Navigation = styled.nav`\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  z-index: 50;\r\n  background: var(--bg-glass);\r\n  backdrop-filter: blur(20px);\r\n  border-bottom: 1px solid var(--border-neural);\r\n  padding: var(--space-4) 0;\r\n  transition: var(--transition-normal);\r\n`;\r\n\r\nconst NavContainer = styled.div`\r\n  max-width: 1400px;\r\n  margin: 0 auto;\r\n  padding: 0 var(--space-6);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n\r\n  @media (max-width: 768px) {\r\n    padding: 0 var(--space-4);\r\n  }\r\n`;\r\n\r\nconst Logo = styled.div`\r\n  font-family: var(--font-primary);\r\n  font-size: 1.5rem;\r\n  font-weight: 700;\r\n  background: var(--text-gradient);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--space-3);\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 1.25rem;\r\n    gap: var(--space-2);\r\n  }\r\n`;\r\n\r\nconst LogoIcon = styled.div`\r\n  width: 40px;\r\n  height: 40px;\r\n  background: var(--bg-neural);\r\n  border-radius: var(--radius-lg);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  box-shadow: var(--shadow-neural);\r\n\r\n  @media (max-width: 768px) {\r\n    width: 36px;\r\n    height: 36px;\r\n  }\r\n`;\r\n\r\nconst BackButton = styled.button`\r\n  background: var(--bg-glass);\r\n  color: var(--text-secondary);\r\n  border: 1px solid var(--border-neural);\r\n  padding: var(--space-3) var(--space-5);\r\n  border-radius: var(--radius-xl);\r\n  font-size: 0.9rem;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  transition: var(--transition-normal);\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--space-2);\r\n  backdrop-filter: blur(10px);\r\n\r\n  &:hover {\r\n    background: var(--primary-50);\r\n    color: var(--primary-600);\r\n    border-color: var(--primary-300);\r\n    transform: translateY(-1px);\r\n    box-shadow: var(--shadow-lg);\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    padding: var(--space-2) var(--space-4);\r\n    font-size: 0.85rem;\r\n  }\r\n`;\r\n\r\nconst PageTitle = styled.h1`\r\n  font-family: var(--font-primary);\r\n  font-size: 2.5rem;\r\n  font-weight: 700;\r\n  background: var(--text-gradient);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n  text-align: center;\r\n  margin-bottom: var(--space-4);\r\n  letter-spacing: -0.02em;\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 2rem;\r\n  }\r\n`;\r\n\r\nconst PageSubtitle = styled.p`\r\n  font-size: 1.125rem;\r\n  color: var(--text-secondary);\r\n  text-align: center;\r\n  margin-bottom: var(--space-16);\r\n  max-width: 700px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n  line-height: 1.6;\r\n\r\n  @media (max-width: 768px) {\r\n    margin-bottom: var(--space-12);\r\n    font-size: 1rem;\r\n  }\r\n`;\r\n\r\nconst StatusBadge = styled.div`\r\n  display: inline-flex;\r\n  align-items: center;\r\n  gap: var(--space-2);\r\n  background: var(--bg-glass);\r\n  border: 1px solid var(--border-neural);\r\n  border-radius: var(--radius-full);\r\n  padding: var(--space-2) var(--space-4);\r\n  margin-bottom: var(--space-8);\r\n  font-size: 0.875rem;\r\n  font-weight: 500;\r\n  color: var(--text-accent);\r\n  backdrop-filter: blur(10px);\r\n  box-shadow: var(--shadow-glow);\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 0.8rem;\r\n    padding: var(--space-2) var(--space-3);\r\n  }\r\n`;\r\n\r\nconst MainContent = styled.main`\r\n  padding: var(--space-20) var(--space-4) var(--space-16);\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n\r\n  @media (max-width: 768px) {\r\n    padding: var(--space-16) var(--space-4) var(--space-12);\r\n  }\r\n`;\r\n\r\nconst TrainingGrid = styled.div`\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: var(--space-8);\r\n  max-width: 1200px;\r\n  margin: 0 auto var(--space-12);\r\n\r\n  @media (max-width: 1024px) {\r\n    grid-template-columns: 1fr;\r\n    gap: var(--space-6);\r\n  }\r\n`;\r\n\r\nconst CameraSection = styled.div`\r\n  background: var(--bg-glass);\r\n  border-radius: var(--radius-2xl);\r\n  padding: var(--space-10);\r\n  border: 1px solid var(--border-neural);\r\n  backdrop-filter: blur(20px);\r\n  box-shadow: var(--shadow-lg);\r\n  transition: var(--transition-normal);\r\n  position: relative;\r\n  overflow: hidden;\r\n\r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    height: 4px;\r\n    background: var(--bg-neural);\r\n    transform: scaleX(0);\r\n    transition: var(--transition-normal);\r\n  }\r\n\r\n  &:hover {\r\n    box-shadow: var(--shadow-xl), var(--shadow-glow);\r\n    border-color: var(--primary-300);\r\n\r\n    &::before {\r\n      transform: scaleX(1);\r\n    }\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    padding: var(--space-8);\r\n  }\r\n`;\r\n\r\nconst SectionTitle = styled.h2`\r\n  font-family: var(--font-primary);\r\n  font-size: 1.25rem;\r\n  margin-bottom: var(--space-6);\r\n  color: var(--text-primary);\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--space-3);\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 1.125rem;\r\n    margin-bottom: var(--space-4);\r\n  }\r\n`;\r\n\r\nconst SectionIcon = styled.div`\r\n  width: 48px;\r\n  height: 48px;\r\n  background: var(--bg-neural);\r\n  border-radius: var(--radius-lg);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  box-shadow: var(--shadow-neural);\r\n\r\n  @media (max-width: 768px) {\r\n    width: 40px;\r\n    height: 40px;\r\n  }\r\n`;\r\n\r\nconst WebcamContainer = styled.div`\r\n  position: relative;\r\n  border-radius: var(--radius-2xl);\r\n  overflow: hidden;\r\n  background: var(--neural-100);\r\n  aspect-ratio: 4/3;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border: 3px solid var(--border-neural);\r\n  margin-bottom: var(--space-6);\r\n  box-shadow: var(--shadow-lg);\r\n`;\r\n\r\nconst StyledWebcam = styled(Webcam)`\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n`;\r\n\r\nconst RecordingOverlay = styled.div`\r\n  position: absolute;\r\n  top: var(--space-4);\r\n  right: var(--space-4);\r\n  background: ${props => props.isRecording ?\r\n    'var(--error-500)' :\r\n    'var(--neural-600)'\r\n  };\r\n  color: white;\r\n  padding: var(--space-3) var(--space-5);\r\n  border-radius: var(--radius-full);\r\n  font-size: 0.9rem;\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--space-2);\r\n  box-shadow: var(--shadow-lg);\r\n  animation: ${props => props.isRecording ? 'pulse 1.5s infinite' : 'none'};\r\n\r\n  @keyframes pulse {\r\n    0%, 100% { opacity: 1; transform: scale(1); }\r\n    50% { opacity: 0.8; transform: scale(1.05); }\r\n  }\r\n`;\r\n\r\nconst SignSection = styled.div`\r\n  background: var(--bg-primary);\r\n  border-radius: var(--radius-2xl);\r\n  padding: var(--space-8);\r\n  border: 1px solid var(--border-light);\r\n  box-shadow: var(--shadow-lg);\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  text-align: center;\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    box-shadow: var(--shadow-xl);\r\n    border-color: var(--primary-200);\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    padding: var(--space-6);\r\n  }\r\n`;\r\n\r\nconst SignSelector = styled.select`\r\n  width: 100%;\r\n  padding: var(--space-3) var(--space-4);\r\n  border: 2px solid var(--border-light);\r\n  border-radius: var(--radius-lg);\r\n  background: var(--bg-primary);\r\n  color: var(--text-primary);\r\n  font-size: 1rem;\r\n  font-weight: 500;\r\n  margin-bottom: var(--space-4);\r\n  cursor: pointer;\r\n  transition: var(--transition-normal);\r\n\r\n  &:focus {\r\n    outline: none;\r\n    border-color: var(--primary-500);\r\n    box-shadow: 0 0 0 3px var(--primary-100);\r\n  }\r\n\r\n  &:hover {\r\n    border-color: var(--primary-300);\r\n  }\r\n\r\n  option {\r\n    padding: var(--space-2);\r\n    background: var(--bg-primary);\r\n    color: var(--text-primary);\r\n  }\r\n`;\r\n\r\nconst SignDisplay = styled.div`\r\n  width: 300px;\r\n  height: 300px;\r\n  background: var(--primary-50);\r\n  border-radius: var(--radius-2xl);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-bottom: var(--space-6);\r\n  border: 2px solid var(--primary-200);\r\n  transition: all 0.3s ease;\r\n  overflow: hidden;\r\n\r\n  img {\r\n    width: 100%;\r\n    height: 100%;\r\n    object-fit: cover;\r\n    border-radius: var(--radius-xl);\r\n  }\r\n\r\n  &:hover {\r\n    transform: scale(1.02);\r\n    border-color: var(--primary-300);\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    width: 250px;\r\n    height: 250px;\r\n  }\r\n`;\r\n\r\nconst SignName = styled.h3`\r\n  font-family: var(--font-primary);\r\n  font-size: 1.5rem;\r\n  margin-bottom: var(--space-3);\r\n  color: var(--text-primary);\r\n  font-weight: 700;\r\n\r\n  @media (max-width: 768px) {\r\n    font-size: 1.25rem;\r\n  }\r\n`;\r\n\r\nconst SignDescription = styled.p`\r\n  text-align: center;\r\n  line-height: 1.6;\r\n  color: var(--text-secondary);\r\n  font-size: 0.9rem;\r\n  font-weight: 400;\r\n  max-width: 280px;\r\n`;\r\n\r\nconst ControlsSection = styled.div`\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: var(--space-4);\r\n  margin-top: var(--space-8);\r\n\r\n  @media (max-width: 768px) {\r\n    flex-direction: column;\r\n    align-items: center;\r\n    gap: var(--space-3);\r\n  }\r\n`;\r\n\r\nconst ControlButton = styled.button`\r\n  background: ${props => props.variant === 'primary'\r\n    ? 'var(--primary-600)'\r\n    : 'var(--bg-primary)'};\r\n  border: ${props => props.variant === 'primary'\r\n    ? 'none'\r\n    : '1px solid var(--border-medium)'};\r\n  color: ${props => props.variant === 'primary'\r\n    ? 'white'\r\n    : 'var(--text-primary)'};\r\n  padding: var(--space-3) var(--space-6);\r\n  border-radius: var(--radius-lg);\r\n  cursor: pointer;\r\n  font-size: 0.9rem;\r\n  font-weight: 600;\r\n  transition: all 0.2s ease;\r\n  min-width: 160px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: var(--space-2);\r\n  box-shadow: ${props => props.variant === 'primary'\r\n    ? 'var(--shadow-lg)'\r\n    : 'var(--shadow-sm)'};\r\n\r\n  &:hover {\r\n    transform: translateY(-2px);\r\n    box-shadow: ${props => props.variant === 'primary'\r\n      ? 'var(--shadow-xl)'\r\n      : 'var(--shadow-md)'};\r\n    background: ${props => props.variant === 'primary'\r\n      ? 'var(--primary-700)'\r\n      : 'var(--gray-50)'};\r\n  }\r\n\r\n  &:disabled {\r\n    opacity: 0.5;\r\n    cursor: not-allowed;\r\n    transform: none;\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    width: 100%;\r\n    max-width: 280px;\r\n  }\r\n`;\r\n\r\nconst StatusMessage = styled.div`\r\n  text-align: center;\r\n  margin-top: var(--space-6);\r\n  padding: var(--space-4) var(--space-6);\r\n  border-radius: var(--radius-lg);\r\n  background: ${props =>\r\n    props.type === 'success' ? 'var(--success-500)' :\r\n    props.type === 'error' ? 'var(--error-500)' :\r\n    'var(--primary-600)'\r\n  };\r\n  color: white;\r\n  font-weight: 500;\r\n  font-size: 0.875rem;\r\n  max-width: 400px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n`;\r\n\r\nconst RecordingsSection = styled.div`\r\n  margin-top: var(--space-16);\r\n  background: var(--bg-secondary);\r\n  padding: var(--space-12) var(--space-4);\r\n  border-radius: var(--radius-2xl);\r\n  max-width: 1200px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n`;\r\n\r\nconst RecordingsTitle = styled.h3`\r\n  font-family: var(--font-primary);\r\n  color: var(--text-primary);\r\n  margin-bottom: var(--space-8);\r\n  font-size: 1.5rem;\r\n  font-weight: 600;\r\n  text-align: center;\r\n`;\r\n\r\nconst RecordingsGrid = styled.div`\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\r\n  gap: var(--space-6);\r\n\r\n  @media (max-width: 768px) {\r\n    grid-template-columns: 1fr;\r\n    gap: var(--space-4);\r\n  }\r\n`;\r\n\r\nconst RecordingCard = styled.div`\r\n  background: var(--bg-primary);\r\n  padding: var(--space-6);\r\n  border-radius: var(--radius-xl);\r\n  border: 1px solid var(--border-light);\r\n  box-shadow: var(--shadow-sm);\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    transform: translateY(-2px);\r\n    border-color: var(--primary-200);\r\n    box-shadow: var(--shadow-lg);\r\n  }\r\n`;\r\n\r\nconst RecordingTitle = styled.p`\r\n  margin: 0 0 var(--space-2) 0;\r\n  color: var(--text-primary);\r\n  font-weight: 600;\r\n  font-size: 1rem;\r\n  font-family: var(--font-primary);\r\n`;\r\n\r\nconst RecordingTime = styled.p`\r\n  margin: 0 0 var(--space-4) 0;\r\n  font-size: 0.8rem;\r\n  color: var(--text-tertiary);\r\n`;\r\n\r\nconst DownloadButton = styled.button`\r\n  background: var(--primary-600);\r\n  border: none;\r\n  border-radius: var(--radius-lg);\r\n  padding: var(--space-2) var(--space-4);\r\n  color: white;\r\n  cursor: pointer;\r\n  font-size: 0.8rem;\r\n  font-weight: 500;\r\n  transition: all 0.2s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--space-2);\r\n  margin: 0 auto;\r\n\r\n  &:hover {\r\n    background: var(--primary-700);\r\n    transform: translateY(-1px);\r\n  }\r\n`;\r\n\r\n// Sign language data with GIFs (matching Streamlit app)\r\nconst signLanguageData = {\r\n  \"after\": {\r\n    name: \"After\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/a/after-over-across.gif\",\r\n    description: \"Move your dominant hand over and past your non-dominant hand\"\r\n  },\r\n  \"airplane\": {\r\n    name: \"Airplane\",\r\n    gif: \"https://www.lifeprint.com/asl101/gifs/a/airplane-flying.gif\",\r\n    description: \"Extend your hand like a plane and move it through the air\"\r\n  },\r\n  \"all\": {\r\n    name: \"All\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/a/all-whole.gif\",\r\n    description: \"Circle your dominant hand around your non-dominant hand\"\r\n  },\r\n  \"alligator\": {\r\n    name: \"Alligator\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/a/alligator.gif\",\r\n    description: \"Clap your hands together like an alligator's mouth\"\r\n  },\r\n  \"animal\": {\r\n    name: \"Animal\",\r\n    gif: \"https://www.lifeprint.com/asl101/gifs-animated/animal.gif\",\r\n    description: \"Place fingertips on chest and move hands back and forth\"\r\n  },\r\n  \"any\": {\r\n    name: \"Any\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/a/any.gif\",\r\n    description: \"Point with index finger and twist your wrist\"\r\n  },\r\n  \"apple\": {\r\n    name: \"Apple\",\r\n    gif: \"https://media.giphy.com/media/l0HlHb4dtZZiMYEA8/giphy.gif\",\r\n    description: \"Twist your knuckle against your cheek\"\r\n  },\r\n  \"arm\": {\r\n    name: \"Arm\",\r\n    gif: \"https://th.bing.com/th/id/OIP.KkJLqfbp6XEHiIe-jOUb2AHaEc?w=251&h=180&c=7&r=0&o=5&dpr=1.3&pid=1.7\",\r\n    description: \"Pat your arm with your opposite hand\"\r\n  },\r\n  \"aunt\": {\r\n    name: \"Aunt\",\r\n    gif: \"https://th.bing.com/th/id/OIP.Yz5UUZdNTrVWXf72we_N6wHaHa?rs=1&pid=ImgDetMain\",\r\n    description: \"Make an 'A' handshape near your cheek and shake it\"\r\n  },\r\n  \"baby\": {\r\n    name: \"Baby\",\r\n    gif: \"https://media.giphy.com/media/l0HlHb4dtZZiMYEA8/giphy.gif\",\r\n    description: \"Rock your arms as if holding a baby\"\r\n  },\r\n  \"ball\": {\r\n    name: \"Ball\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/b/ball.gif\",\r\n    description: \"Cup your hands as if holding a ball\"\r\n  },\r\n  \"banana\": {\r\n    name: \"Banana\",\r\n    gif: \"https://media.giphy.com/media/l0HlHb4dtZZiMYEA8/giphy.gif\",\r\n    description: \"Peel an imaginary banana with your fingers\"\r\n  },\r\n  \"bear\": {\r\n    name: \"Bear\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/b/bear.gif\",\r\n    description: \"Cross your arms and scratch like a bear\"\r\n  },\r\n  \"beautiful\": {\r\n    name: \"Beautiful\",\r\n    gif: \"https://media.giphy.com/media/l0HlHb4dtZZiMYEA8/giphy.gif\",\r\n    description: \"Circle your face with your hand and close it into a fist\"\r\n  },\r\n  \"bed\": {\r\n    name: \"Bed\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/b/bed.gif\",\r\n    description: \"Rest your head on your hands as if sleeping\"\r\n  },\r\n  \"bee\": {\r\n    name: \"Bee\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/b/bee.gif\",\r\n    description: \"Pinch your cheek and brush away as if swatting a bee\"\r\n  },\r\n  \"bird\": {\r\n    name: \"Bird\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/b/bird.gif\",\r\n    description: \"Pinch your fingers together near your mouth like a beak\"\r\n  },\r\n  \"black\": {\r\n    name: \"Black\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/b/black.gif\",\r\n    description: \"Draw your index finger across your forehead\"\r\n  },\r\n  \"blue\": {\r\n    name: \"Blue\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/b/blue.gif\",\r\n    description: \"Shake a 'B' handshape\"\r\n  },\r\n  \"book\": {\r\n    name: \"Book\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/b/book.gif\",\r\n    description: \"Open your hands like opening a book\"\r\n  },\r\n  \"boy\": {\r\n    name: \"Boy\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/b/boy.gif\",\r\n    description: \"Snap your fingers at your forehead\"\r\n  },\r\n  \"brother\": {\r\n    name: \"Brother\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/b/brother.gif\",\r\n    description: \"Make an 'L' shape and point to your forehead, then point forward\"\r\n  },\r\n  \"brown\": {\r\n    name: \"Brown\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/b/brown.gif\",\r\n    description: \"Slide your index finger down your cheek\"\r\n  },\r\n  \"bug\": {\r\n    name: \"Bug\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/b/bug.gif\",\r\n    description: \"Pinch your nose with your thumb and index finger\"\r\n  },\r\n  \"butterfly\": {\r\n    name: \"Butterfly\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/b/butterfly.gif\",\r\n    description: \"Cross your thumbs and flutter your fingers like wings\"\r\n  },\r\n  \"car\": {\r\n    name: \"Car\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/c/car.gif\",\r\n    description: \"Pretend to steer a car with both hands\"\r\n  },\r\n  \"cat\": {\r\n    name: \"Cat\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/c/cat.gif\",\r\n    description: \"Pinch your cheek and pull out like whiskers\"\r\n  },\r\n  \"chair\": {\r\n    name: \"Chair\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/c/chair.gif\",\r\n    description: \"Tap your fingers on your other hand like sitting\"\r\n  },\r\n  \"clean\": {\r\n    name: \"Clean\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/c/clean.gif\",\r\n    description: \"Wipe one palm with the other\"\r\n  },\r\n  \"cold\": {\r\n    name: \"Cold\",\r\n    gif: \"https://media.giphy.com/media/l0HlHb4dtZZiMYEA8/giphy.gif\",\r\n    description: \"Shiver with both hands in fists\"\r\n  },\r\n  \"cow\": {\r\n    name: \"Cow\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/c/cow.gif\",\r\n    description: \"Twist your thumb at your temple like a horn\"\r\n  },\r\n  \"cry\": {\r\n    name: \"Cry\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/c/cry.gif\",\r\n    description: \"Draw tears down your cheeks with your index fingers\"\r\n  },\r\n  \"cute\": {\r\n    name: \"Cute\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/c/cute-sugar.gif\",\r\n    description: \"Brush your chin with your fingers\"\r\n  },\r\n  \"dad\": {\r\n    name: \"Dad\",\r\n    gif: \"https://media.giphy.com/media/l0MYQcLKwtl5v6H1S/giphy.gif\",\r\n    description: \"Tap your forehead with your thumb\"\r\n  },\r\n  \"dance\": {\r\n    name: \"Dance\",\r\n    gif: \"https://www.bing.com/th/id/OGC.********************************?pid=1.7&rurl=https%3a%2f%2fmedia.giphy.com%2fmedia%2f3o7TKMspYQjQTbOz2U%2fgiphy.gif&ehk=h%2bdBHCxuoOT89ovSy5uTk6MCL9acaBEV6ld9lrVDRF4%3d\",\r\n    description: \"Swing your fingers over your palm like dancing\"\r\n  },\r\n  \"dirty\": {\r\n    name: \"Dirty\",\r\n    gif: \"https://th.bing.com/th/id/OIP.wRA7r1OPPUuEoLL4Hds9jAHaHa?rs=1&pid=ImgDetMain\",\r\n    description: \"Wiggle your fingers under your chin\"\r\n  },\r\n  \"dog\": {\r\n    name: \"Dog\",\r\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=uVshGsOHXgldoQ&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fd%2fdog1.gif&ehk=Xnfrvg0xwy1u%2fae9vWdKYMT25%2bF6qoteW2FThCbVrOA%3d&risl=&pid=ImgRaw&r=0\",\r\n    description: \"Pat your leg and snap your fingers\"\r\n  },\r\n  \"eat\": {\r\n    name: \"Eat\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/e/eat.gif\",\r\n    description: \"Bring your fingers to your mouth as if eating\"\r\n  },\r\n  \"elephant\": {\r\n    name: \"Elephant\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/e/elephant.gif\",\r\n    description: \"Trace the shape of an elephant's trunk with your hand\"\r\n  },\r\n  \"fish\": {\r\n    name: \"Fish\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/f/fish.gif\",\r\n    description: \"Move your hand like a fish swimming\"\r\n  },\r\n  \"flower\": {\r\n    name: \"Flower\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/f/flower.gif\",\r\n    description: \"Touch your nose with your fingertips\"\r\n  },\r\n  \"friend\": {\r\n    name: \"Friend\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/f/friend.gif\",\r\n    description: \"Hook your index fingers together\"\r\n  },\r\n  \"girl\": {\r\n    name: \"Girl\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/g/girl.gif\",\r\n    description: \"Trace your jawline with your thumb\"\r\n  },\r\n  \"go\": {\r\n    name: \"Go\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/g/go.gif\",\r\n    description: \"Point both index fingers forward and bend them\"\r\n  },\r\n  \"good\": {\r\n    name: \"Good\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/g/good.gif\",\r\n    description: \"Touch your chin and move your hand forward\"\r\n  },\r\n  \"green\": {\r\n    name: \"Green\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/g/green.gif\",\r\n    description: \"Shake a 'G' handshape\"\r\n  },\r\n  \"hair\": {\r\n    name: \"Hair\",\r\n    gif: \"https://www.lifeprint.com/asl101/gifs/h/hair-g-version.gif\",\r\n    description: \"Pinch a strand of your hair\"\r\n  },\r\n  \"happy\": {\r\n    name: \"Happy\",\r\n    gif: \"https://media0.giphy.com/media/3o7TKFpahYpUp4g0N2/giphy.gif?cid=790b7611bca188a92e2e759876756ef62ba95b7cdd337c77&rid=giphy.gif&ct=g\",\r\n    description: \"Brush your chest upward with both hands\"\r\n  },\r\n  \"hello\": {\r\n    name: \"Hello\",\r\n    gif: \"https://media.giphy.com/media/3o7TKNKOfKlIhbD3gY/giphy.gif\",\r\n    description: \"Wave your hand from side to side with palm facing forward\"\r\n  },\r\n  \"home\": {\r\n    name: \"Home\",\r\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=%2bnBd%2foQjxnoPfg&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fh%2fhome-2.gif&ehk=7yD%2f%2fh6JN1Y4D4BOrUjgKW4Jccy2Y4GVYLf%2fzyk%2b5YY%3d&risl=&pid=ImgRaw&r=0\",\r\n    description: \"Touch your mouth then your cheek\"\r\n  },\r\n  \"horse\": {\r\n    name: \"Horse\",\r\n    gif: \"https://media.giphy.com/media/l0HlM5HffraiQaHUk/giphy.gif\",\r\n    description: \"Extend your thumb and fingers at your temple like ears\"\r\n  },\r\n  \"hot\": {\r\n    name: \"Hot\",\r\n    gif: \"https://media.giphy.com/media/3o6Zt99k5aDok347bG/giphy.gif\",\r\n    description: \"Touch your mouth and quickly move your hand away\"\r\n  },\r\n  \"hungry\": {\r\n    name: \"Hungry\",\r\n    gif: \"https://media.giphy.com/media/l3vR0xkdFEz4tnfTq/giphy.gif\",\r\n    description: \"Move your hand down your chest like food going down\"\r\n  },\r\n  \"jump\": {\r\n    name: \"Jump\",\r\n    gif: \"https://lifeprint.com/asl101/gifs-animated/jump.gif\",\r\n    description: \"Bounce your fingers on your palm\"\r\n  },\r\n  \"like\": {\r\n    name: \"Like\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/l/like.gif\",\r\n    description: \"Pull your thumb and middle finger from your chest\"\r\n  },\r\n  \"look\": {\r\n    name: \"Look\",\r\n    gif: \"https://th.bing.com/th/id/R.********************************?rik=pYhzip7LqNs7qw&riu=http%3a%2f%2flifeprint.com%2fasl101%2fgifs%2fl%2flook-at-1.gif&ehk=rFJ7dBrMGFDK0nHLzrOPAzROVE7yqyDEcb%2btLqKqYOI%3d&risl=&pid=ImgRaw&r=0\",\r\n    description: \"Point your fingers from your eyes forward\"\r\n  },\r\n  \"love\": {\r\n    name: \"Love\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/l/love.gif\",\r\n    description: \"Cross your arms over your chest\"\r\n  },\r\n  \"mom\": {\r\n    name: \"Mom\",\r\n    gif: \"https://media.giphy.com/media/l0MYQcLKwtl5v6H1S/giphy.gif\",\r\n    description: \"Tap your chin with your thumb\"\r\n  },\r\n  \"more\": {\r\n    name: \"More\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/m/more.gif\",\r\n    description: \"Tap your fingertips together\"\r\n  },\r\n  \"no\": {\r\n    name: \"No\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/n/no.gif\",\r\n    description: \"Snap your fingers together\"\r\n  },\r\n  \"orange\": {\r\n    name: \"Orange\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/o/orange.gif\",\r\n    description: \"Squeeze your hand at your mouth like squeezing an orange\"\r\n  },\r\n  \"please\": {\r\n    name: \"Please\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/p/please.gif\",\r\n    description: \"Rub your chest in a circular motion\"\r\n  },\r\n  \"red\": {\r\n    name: \"Red\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/r/red.gif\",\r\n    description: \"Brush your lips with your index finger\"\r\n  },\r\n  \"run\": {\r\n    name: \"Run\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/r/run.gif\",\r\n    description: \"Hook your thumbs and wiggle your fingers\"\r\n  },\r\n  \"sad\": {\r\n    name: \"Sad\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/s/sad.gif\",\r\n    description: \"Drop both hands down your face\"\r\n  },\r\n  \"see\": {\r\n    name: \"See\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/l/look-at-2.gif\",\r\n    description: \"Point from your eyes forward\"\r\n  },\r\n  \"sleep\": {\r\n    name: \"Sleep\",\r\n    gif: \"https://media4.giphy.com/media/3o7TKnRuBdakLslcaI/200.gif?cid=790b76110d8f185a9713f36dd65a0df801576e01b403c95c&rid=200.gif&ct=g\",\r\n    description: \"Rest your head on your hands\"\r\n  },\r\n  \"sorry\": {\r\n    name: \"Sorry\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/s/sorry.gif\",\r\n    description: \"Rub your fist in a circle on your chest\"\r\n  },\r\n  \"thank\": {\r\n    name: \"Thank You\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/t/thank-you.gif\",\r\n    description: \"Touch your chin and move your hand forward\"\r\n  },\r\n  \"water\": {\r\n    name: \"Water\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/w/water.gif\",\r\n    description: \"Tap your chin with a 'W' handshape\"\r\n  },\r\n  \"white\": {\r\n    name: \"White\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/w/white.gif\",\r\n    description: \"Touch your chest and pull your hand away\"\r\n  },\r\n  \"yellow\": {\r\n    name: \"Yellow\",\r\n    gif: \"https://lifeprint.com/asl101/gifs/y/yellow.gif\",\r\n    description: \"Shake a 'Y' handshape\"\r\n  },\r\n  \"yes\": {\r\n    name: \"Yes\",\r\n    gif: \"https://media.tenor.com/oYIirlyIih0AAAAC/yes-asl.gif\",\r\n    description: \"Nod your fist up and down\"\r\n  }\r\n};\r\n\r\nconst TrainingPage = ({ onBackToHome }) => {\r\n  const [isRecording, setIsRecording] = useState(false);\r\n  const [currentSign, setCurrentSign] = useState('hello');\r\n  const [status, setStatus] = useState('');\r\n  const [recordedVideos, setRecordedVideos] = useState([]);\r\n  const webcamRef = useRef(null);\r\n  const mediaRecorderRef = useRef(null);\r\n  const recordedChunksRef = useRef([]);\r\n\r\n  const handleSignChange = useCallback((event) => {\r\n    setCurrentSign(event.target.value);\r\n  }, []);\r\n\r\n  const startRecording = useCallback(() => {\r\n    if (!webcamRef.current) {\r\n      setStatus('Camera not available');\r\n      return;\r\n    }\r\n\r\n    mediaRecorderRef.current = new MediaRecorder(webcamRef.current.stream, {\r\n      mimeType: 'video/webm'\r\n    });\r\n\r\n    mediaRecorderRef.current.ondataavailable = (event) => {\r\n      if (event.data.size > 0) {\r\n        recordedChunksRef.current.push(event.data);\r\n      }\r\n    };\r\n\r\n    mediaRecorderRef.current.onstop = () => {\r\n      const blob = new Blob(recordedChunksRef.current, {\r\n        type: 'video/webm'\r\n      });\r\n      const url = URL.createObjectURL(blob);\r\n      const timestamp = new Date().toISOString();\r\n      \r\n      setRecordedVideos(prev => [...prev, {\r\n        id: timestamp,\r\n        url,\r\n        sign: signLanguageData[currentSign].name,\r\n        timestamp\r\n      }]);\r\n      \r\n      recordedChunksRef.current = [];\r\n      setStatus('Recording saved successfully!');\r\n    };\r\n\r\n    mediaRecorderRef.current.start();\r\n    setIsRecording(true);\r\n    setStatus('Recording started...');\r\n  }, [currentSign]);\r\n\r\n  const stopRecording = useCallback(() => {\r\n    if (mediaRecorderRef.current && isRecording) {\r\n      mediaRecorderRef.current.stop();\r\n      setIsRecording(false);\r\n      setStatus('Processing recording...');\r\n    }\r\n  }, [isRecording]);\r\n\r\n  const downloadRecording = (video) => {\r\n    const a = document.createElement('a');\r\n    a.href = video.url;\r\n    a.download = `sign_${video.sign}_${video.timestamp}.webm`;\r\n    a.click();\r\n  };\r\n\r\n\r\n\r\n  return (\r\n    <TrainingContainer>\r\n      <Navigation>\r\n        <NavContainer>\r\n          <Logo>\r\n            <LogoIcon>\r\n              <Brain size={24} />\r\n            </LogoIcon>\r\n            ASL Neural\r\n          </Logo>\r\n          <BackButton onClick={onBackToHome}>\r\n            <ArrowLeft size={18} />\r\n            Back to Home\r\n          </BackButton>\r\n        </NavContainer>\r\n      </Navigation>\r\n\r\n      <MainContent>\r\n        <div style={{ textAlign: 'center', marginBottom: 'var(--space-12)' }}>\r\n          <StatusBadge>\r\n            <Eye size={16} />\r\n            Neural Vision Active\r\n          </StatusBadge>\r\n        </div>\r\n\r\n        <PageTitle>AI Training Session</PageTitle>\r\n        <PageSubtitle>\r\n          Experience real-time neural network analysis as our AI learns from your sign language practice\r\n        </PageSubtitle>\r\n\r\n        <TrainingGrid>\r\n          <CameraSection>\r\n            <SectionTitle>\r\n              <SectionIcon>\r\n                <Camera size={24} />\r\n              </SectionIcon>\r\n              Neural Vision Feed\r\n            </SectionTitle>\r\n            <WebcamContainer>\r\n              <StyledWebcam\r\n                ref={webcamRef}\r\n                audio={false}\r\n                screenshotFormat=\"image/jpeg\"\r\n                videoConstraints={{\r\n                  width: 640,\r\n                  height: 480,\r\n                  facingMode: \"user\"\r\n                }}\r\n              />\r\n              <RecordingOverlay isRecording={isRecording}>\r\n                {isRecording ? (\r\n                  <>\r\n                    <div style={{\r\n                      width: '8px',\r\n                      height: '8px',\r\n                      borderRadius: '50%',\r\n                      backgroundColor: 'white',\r\n                      marginRight: '4px'\r\n                    }} />\r\n                    Recording\r\n                  </>\r\n                ) : (\r\n                  <>\r\n                    <Eye size={16} />\r\n                    Ready\r\n                  </>\r\n                )}\r\n              </RecordingOverlay>\r\n            </WebcamContainer>\r\n          </CameraSection>\r\n\r\n          <SignSection>\r\n            <SectionTitle>\r\n              <SectionIcon>\r\n                <Target size={24} />\r\n              </SectionIcon>\r\n              Select a Sign\r\n            </SectionTitle>\r\n            <SignSelector\r\n              value={currentSign}\r\n              onChange={handleSignChange}\r\n              disabled={isRecording}\r\n            >\r\n              {Object.keys(signLanguageData).map(signKey => (\r\n                <option key={signKey} value={signKey}>\r\n                  {signLanguageData[signKey].name}\r\n                </option>\r\n              ))}\r\n            </SignSelector>\r\n            <SignDisplay>\r\n              <img\r\n                src={signLanguageData[currentSign].gif}\r\n                alt={signLanguageData[currentSign].name}\r\n                onError={(e) => {\r\n                  e.target.style.display = 'none';\r\n                  e.target.nextSibling.style.display = 'flex';\r\n                }}\r\n              />\r\n              <div style={{display: 'none', fontSize: '3rem'}}>\r\n                📷\r\n              </div>\r\n            </SignDisplay>\r\n            <SignName>{signLanguageData[currentSign].name}</SignName>\r\n            <SignDescription>\r\n              {signLanguageData[currentSign].description}\r\n            </SignDescription>\r\n          </SignSection>\r\n        </TrainingGrid>\r\n\r\n        <ControlsSection>\r\n          <ControlButton\r\n            variant=\"primary\"\r\n            onClick={isRecording ? stopRecording : startRecording}\r\n          >\r\n            {isRecording ? (\r\n              <>\r\n                <Square size={18} />\r\n                Stop Neural Recording\r\n              </>\r\n            ) : (\r\n              <>\r\n                <Play size={18} />\r\n                Start Neural Recording\r\n              </>\r\n            )}\r\n          </ControlButton>\r\n        </ControlsSection>\r\n\r\n        {status && (\r\n          <StatusMessage type={status.includes('error') ? 'error' : status.includes('success') ? 'success' : 'info'}>\r\n            {status}\r\n          </StatusMessage>\r\n        )}\r\n\r\n        {recordedVideos.length > 0 && (\r\n          <RecordingsSection>\r\n            <RecordingsTitle>Your Practice Recordings</RecordingsTitle>\r\n            <RecordingsGrid>\r\n              {recordedVideos.map((video) => (\r\n                <RecordingCard key={video.id}>\r\n                  <RecordingTitle>{video.sign}</RecordingTitle>\r\n                  <RecordingTime>\r\n                    {new Date(video.timestamp).toLocaleString()}\r\n                  </RecordingTime>\r\n                  <DownloadButton onClick={() => downloadRecording(video)}>\r\n                    <Download size={16} />\r\n                    Download\r\n                  </DownloadButton>\r\n                </RecordingCard>\r\n              ))}\r\n            </RecordingsGrid>\r\n          </RecordingsSection>\r\n        )}\r\n      </MainContent>\r\n    </TrainingContainer>\r\n  );\r\n};\r\n\r\nexport default TrainingPage; "], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AACrD,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,MAAM,MAAM,cAAc;AACjC,SACEC,KAAK,EACLC,MAAM,EACNC,SAAS,EACTC,IAAI,EACJC,MAAM,EACNC,QAAQ,EACRC,GAAG,EACHC,GAAG,EACHC,MAAM,QACD,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtB,MAAMC,iBAAiB,GAAGf,MAAM,CAACgB,GAAG;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAnBIF,iBAAiB;AAqBvB,MAAMG,UAAU,GAAGlB,MAAM,CAACmB,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAXIF,UAAU;AAahB,MAAMG,YAAY,GAAGrB,MAAM,CAACgB,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACM,GAAA,GAXID,YAAY;AAalB,MAAME,IAAI,GAAGvB,MAAM,CAACgB,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACQ,GAAA,GAhBID,IAAI;AAkBV,MAAME,QAAQ,GAAGzB,MAAM,CAACgB,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACU,GAAA,GAfID,QAAQ;AAiBd,MAAME,UAAU,GAAG3B,MAAM,CAAC4B,MAAM;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GA3BIF,UAAU;AA6BhB,MAAMG,SAAS,GAAG9B,MAAM,CAAC+B,EAAE;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAfIF,SAAS;AAiBf,MAAMG,YAAY,GAAGjC,MAAM,CAACkC,CAAC;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAdIF,YAAY;AAgBlB,MAAMG,WAAW,GAAGpC,MAAM,CAACgB,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACqB,GAAA,GAnBID,WAAW;AAqBjB,MAAME,WAAW,GAAGtC,MAAM,CAACuC,IAAI;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GARIF,WAAW;AAUjB,MAAMG,YAAY,GAAGzC,MAAM,CAACgB,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC0B,GAAA,GAXID,YAAY;AAalB,MAAME,aAAa,GAAG3C,MAAM,CAACgB,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC4B,IAAA,GAnCID,aAAa;AAqCnB,MAAME,YAAY,GAAG7C,MAAM,CAAC8C,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAdIF,YAAY;AAgBlB,MAAMG,WAAW,GAAGhD,MAAM,CAACgB,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACiC,IAAA,GAfID,WAAW;AAiBjB,MAAME,eAAe,GAAGlD,MAAM,CAACgB,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACmC,IAAA,GAZID,eAAe;AAcrB,MAAME,YAAY,GAAGpD,MAAM,CAACC,MAAM,CAAC;AACnC;AACA;AACA;AACA,CAAC;AAACoD,IAAA,GAJID,YAAY;AAMlB,MAAME,gBAAgB,GAAGtD,MAAM,CAACgB,GAAG;AACnC;AACA;AACA;AACA,gBAAgBuC,KAAK,IAAIA,KAAK,CAACC,WAAW,GACtC,kBAAkB,GAClB,mBAAmB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eACeD,KAAK,IAAIA,KAAK,CAACC,WAAW,GAAG,qBAAqB,GAAG,MAAM;AAC1E;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAvBIH,gBAAgB;AAyBtB,MAAMI,WAAW,GAAG1D,MAAM,CAACgB,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC2C,IAAA,GApBID,WAAW;AAsBjB,MAAME,YAAY,GAAG5D,MAAM,CAAC6D,MAAM;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GA5BIF,YAAY;AA8BlB,MAAMG,WAAW,GAAG/D,MAAM,CAACgB,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACgD,IAAA,GA7BID,WAAW;AA+BjB,MAAME,QAAQ,GAAGjE,MAAM,CAACkE,EAAE;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAVIF,QAAQ;AAYd,MAAMG,eAAe,GAAGpE,MAAM,CAACkC,CAAC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACmC,IAAA,GAPID,eAAe;AASrB,MAAME,eAAe,GAAGtE,MAAM,CAACgB,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACuD,IAAA,GAXID,eAAe;AAarB,MAAME,aAAa,GAAGxE,MAAM,CAAC4B,MAAM;AACnC,gBAAgB2B,KAAK,IAAIA,KAAK,CAACkB,OAAO,KAAK,SAAS,GAC9C,oBAAoB,GACpB,mBAAmB;AACzB,YAAYlB,KAAK,IAAIA,KAAK,CAACkB,OAAO,KAAK,SAAS,GAC1C,MAAM,GACN,gCAAgC;AACtC,WAAWlB,KAAK,IAAIA,KAAK,CAACkB,OAAO,KAAK,SAAS,GACzC,OAAO,GACP,qBAAqB;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgBlB,KAAK,IAAIA,KAAK,CAACkB,OAAO,KAAK,SAAS,GAC9C,kBAAkB,GAClB,kBAAkB;AACxB;AACA;AACA;AACA,kBAAkBlB,KAAK,IAAIA,KAAK,CAACkB,OAAO,KAAK,SAAS,GAC9C,kBAAkB,GAClB,kBAAkB;AAC1B,kBAAkBlB,KAAK,IAAIA,KAAK,CAACkB,OAAO,KAAK,SAAS,GAC9C,oBAAoB,GACpB,gBAAgB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GA7CIF,aAAa;AA+CnB,MAAMG,aAAa,GAAG3E,MAAM,CAACgB,GAAG;AAChC;AACA;AACA;AACA;AACA,gBAAgBuC,KAAK,IACjBA,KAAK,CAACqB,IAAI,KAAK,SAAS,GAAG,oBAAoB,GAC/CrB,KAAK,CAACqB,IAAI,KAAK,OAAO,GAAG,kBAAkB,GAC3C,oBAAoB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA,CACC;AAACC,IAAA,GAhBIF,aAAa;AAkBnB,MAAMG,iBAAiB,GAAG9E,MAAM,CAACgB,GAAG;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC+D,IAAA,GARID,iBAAiB;AAUvB,MAAME,eAAe,GAAGhF,MAAM,CAACkE,EAAE;AACjC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACe,IAAA,GAPID,eAAe;AASrB,MAAME,cAAc,GAAGlF,MAAM,CAACgB,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACmE,IAAA,GATID,cAAc;AAWpB,MAAME,aAAa,GAAGpF,MAAM,CAACgB,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACqE,IAAA,GAbID,aAAa;AAenB,MAAME,cAAc,GAAGtF,MAAM,CAACkC,CAAC;AAC/B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACqD,IAAA,GANID,cAAc;AAQpB,MAAME,aAAa,GAAGxF,MAAM,CAACkC,CAAC;AAC9B;AACA;AACA;AACA,CAAC;AAACuD,IAAA,GAJID,aAAa;AAMnB,MAAME,cAAc,GAAG1F,MAAM,CAAC4B,MAAM;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAA+D,IAAA,GArBMD,cAAc;AAsBpB,MAAME,gBAAgB,GAAG;EACvB,OAAO,EAAE;IACPC,IAAI,EAAE,OAAO;IACbC,GAAG,EAAE,2DAA2D;IAChEC,WAAW,EAAE;EACf,CAAC;EACD,UAAU,EAAE;IACVF,IAAI,EAAE,UAAU;IAChBC,GAAG,EAAE,6DAA6D;IAClEC,WAAW,EAAE;EACf,CAAC;EACD,KAAK,EAAE;IACLF,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,mDAAmD;IACxDC,WAAW,EAAE;EACf,CAAC;EACD,WAAW,EAAE;IACXF,IAAI,EAAE,WAAW;IACjBC,GAAG,EAAE,mDAAmD;IACxDC,WAAW,EAAE;EACf,CAAC;EACD,QAAQ,EAAE;IACRF,IAAI,EAAE,QAAQ;IACdC,GAAG,EAAE,2DAA2D;IAChEC,WAAW,EAAE;EACf,CAAC;EACD,KAAK,EAAE;IACLF,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,6CAA6C;IAClDC,WAAW,EAAE;EACf,CAAC;EACD,OAAO,EAAE;IACPF,IAAI,EAAE,OAAO;IACbC,GAAG,EAAE,2DAA2D;IAChEC,WAAW,EAAE;EACf,CAAC;EACD,KAAK,EAAE;IACLF,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,kGAAkG;IACvGC,WAAW,EAAE;EACf,CAAC;EACD,MAAM,EAAE;IACNF,IAAI,EAAE,MAAM;IACZC,GAAG,EAAE,8EAA8E;IACnFC,WAAW,EAAE;EACf,CAAC;EACD,MAAM,EAAE;IACNF,IAAI,EAAE,MAAM;IACZC,GAAG,EAAE,2DAA2D;IAChEC,WAAW,EAAE;EACf,CAAC;EACD,MAAM,EAAE;IACNF,IAAI,EAAE,MAAM;IACZC,GAAG,EAAE,8CAA8C;IACnDC,WAAW,EAAE;EACf,CAAC;EACD,QAAQ,EAAE;IACRF,IAAI,EAAE,QAAQ;IACdC,GAAG,EAAE,2DAA2D;IAChEC,WAAW,EAAE;EACf,CAAC;EACD,MAAM,EAAE;IACNF,IAAI,EAAE,MAAM;IACZC,GAAG,EAAE,8CAA8C;IACnDC,WAAW,EAAE;EACf,CAAC;EACD,WAAW,EAAE;IACXF,IAAI,EAAE,WAAW;IACjBC,GAAG,EAAE,2DAA2D;IAChEC,WAAW,EAAE;EACf,CAAC;EACD,KAAK,EAAE;IACLF,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,6CAA6C;IAClDC,WAAW,EAAE;EACf,CAAC;EACD,KAAK,EAAE;IACLF,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,6CAA6C;IAClDC,WAAW,EAAE;EACf,CAAC;EACD,MAAM,EAAE;IACNF,IAAI,EAAE,MAAM;IACZC,GAAG,EAAE,8CAA8C;IACnDC,WAAW,EAAE;EACf,CAAC;EACD,OAAO,EAAE;IACPF,IAAI,EAAE,OAAO;IACbC,GAAG,EAAE,+CAA+C;IACpDC,WAAW,EAAE;EACf,CAAC;EACD,MAAM,EAAE;IACNF,IAAI,EAAE,MAAM;IACZC,GAAG,EAAE,8CAA8C;IACnDC,WAAW,EAAE;EACf,CAAC;EACD,MAAM,EAAE;IACNF,IAAI,EAAE,MAAM;IACZC,GAAG,EAAE,8CAA8C;IACnDC,WAAW,EAAE;EACf,CAAC;EACD,KAAK,EAAE;IACLF,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,6CAA6C;IAClDC,WAAW,EAAE;EACf,CAAC;EACD,SAAS,EAAE;IACTF,IAAI,EAAE,SAAS;IACfC,GAAG,EAAE,iDAAiD;IACtDC,WAAW,EAAE;EACf,CAAC;EACD,OAAO,EAAE;IACPF,IAAI,EAAE,OAAO;IACbC,GAAG,EAAE,+CAA+C;IACpDC,WAAW,EAAE;EACf,CAAC;EACD,KAAK,EAAE;IACLF,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,6CAA6C;IAClDC,WAAW,EAAE;EACf,CAAC;EACD,WAAW,EAAE;IACXF,IAAI,EAAE,WAAW;IACjBC,GAAG,EAAE,mDAAmD;IACxDC,WAAW,EAAE;EACf,CAAC;EACD,KAAK,EAAE;IACLF,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,6CAA6C;IAClDC,WAAW,EAAE;EACf,CAAC;EACD,KAAK,EAAE;IACLF,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,6CAA6C;IAClDC,WAAW,EAAE;EACf,CAAC;EACD,OAAO,EAAE;IACPF,IAAI,EAAE,OAAO;IACbC,GAAG,EAAE,+CAA+C;IACpDC,WAAW,EAAE;EACf,CAAC;EACD,OAAO,EAAE;IACPF,IAAI,EAAE,OAAO;IACbC,GAAG,EAAE,+CAA+C;IACpDC,WAAW,EAAE;EACf,CAAC;EACD,MAAM,EAAE;IACNF,IAAI,EAAE,MAAM;IACZC,GAAG,EAAE,2DAA2D;IAChEC,WAAW,EAAE;EACf,CAAC;EACD,KAAK,EAAE;IACLF,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,6CAA6C;IAClDC,WAAW,EAAE;EACf,CAAC;EACD,KAAK,EAAE;IACLF,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,6CAA6C;IAClDC,WAAW,EAAE;EACf,CAAC;EACD,MAAM,EAAE;IACNF,IAAI,EAAE,MAAM;IACZC,GAAG,EAAE,oDAAoD;IACzDC,WAAW,EAAE;EACf,CAAC;EACD,KAAK,EAAE;IACLF,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,2DAA2D;IAChEC,WAAW,EAAE;EACf,CAAC;EACD,OAAO,EAAE;IACPF,IAAI,EAAE,OAAO;IACbC,GAAG,EAAE,0MAA0M;IAC/MC,WAAW,EAAE;EACf,CAAC;EACD,OAAO,EAAE;IACPF,IAAI,EAAE,OAAO;IACbC,GAAG,EAAE,8EAA8E;IACnFC,WAAW,EAAE;EACf,CAAC;EACD,KAAK,EAAE;IACLF,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,2NAA2N;IAChOC,WAAW,EAAE;EACf,CAAC;EACD,KAAK,EAAE;IACLF,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,6CAA6C;IAClDC,WAAW,EAAE;EACf,CAAC;EACD,UAAU,EAAE;IACVF,IAAI,EAAE,UAAU;IAChBC,GAAG,EAAE,kDAAkD;IACvDC,WAAW,EAAE;EACf,CAAC;EACD,MAAM,EAAE;IACNF,IAAI,EAAE,MAAM;IACZC,GAAG,EAAE,8CAA8C;IACnDC,WAAW,EAAE;EACf,CAAC;EACD,QAAQ,EAAE;IACRF,IAAI,EAAE,QAAQ;IACdC,GAAG,EAAE,gDAAgD;IACrDC,WAAW,EAAE;EACf,CAAC;EACD,QAAQ,EAAE;IACRF,IAAI,EAAE,QAAQ;IACdC,GAAG,EAAE,gDAAgD;IACrDC,WAAW,EAAE;EACf,CAAC;EACD,MAAM,EAAE;IACNF,IAAI,EAAE,MAAM;IACZC,GAAG,EAAE,8CAA8C;IACnDC,WAAW,EAAE;EACf,CAAC;EACD,IAAI,EAAE;IACJF,IAAI,EAAE,IAAI;IACVC,GAAG,EAAE,4CAA4C;IACjDC,WAAW,EAAE;EACf,CAAC;EACD,MAAM,EAAE;IACNF,IAAI,EAAE,MAAM;IACZC,GAAG,EAAE,8CAA8C;IACnDC,WAAW,EAAE;EACf,CAAC;EACD,OAAO,EAAE;IACPF,IAAI,EAAE,OAAO;IACbC,GAAG,EAAE,+CAA+C;IACpDC,WAAW,EAAE;EACf,CAAC;EACD,MAAM,EAAE;IACNF,IAAI,EAAE,MAAM;IACZC,GAAG,EAAE,4DAA4D;IACjEC,WAAW,EAAE;EACf,CAAC;EACD,OAAO,EAAE;IACPF,IAAI,EAAE,OAAO;IACbC,GAAG,EAAE,qIAAqI;IAC1IC,WAAW,EAAE;EACf,CAAC;EACD,OAAO,EAAE;IACPF,IAAI,EAAE,OAAO;IACbC,GAAG,EAAE,4DAA4D;IACjEC,WAAW,EAAE;EACf,CAAC;EACD,MAAM,EAAE;IACNF,IAAI,EAAE,MAAM;IACZC,GAAG,EAAE,qOAAqO;IAC1OC,WAAW,EAAE;EACf,CAAC;EACD,OAAO,EAAE;IACPF,IAAI,EAAE,OAAO;IACbC,GAAG,EAAE,2DAA2D;IAChEC,WAAW,EAAE;EACf,CAAC;EACD,KAAK,EAAE;IACLF,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,4DAA4D;IACjEC,WAAW,EAAE;EACf,CAAC;EACD,QAAQ,EAAE;IACRF,IAAI,EAAE,QAAQ;IACdC,GAAG,EAAE,2DAA2D;IAChEC,WAAW,EAAE;EACf,CAAC;EACD,MAAM,EAAE;IACNF,IAAI,EAAE,MAAM;IACZC,GAAG,EAAE,qDAAqD;IAC1DC,WAAW,EAAE;EACf,CAAC;EACD,MAAM,EAAE;IACNF,IAAI,EAAE,MAAM;IACZC,GAAG,EAAE,8CAA8C;IACnDC,WAAW,EAAE;EACf,CAAC;EACD,MAAM,EAAE;IACNF,IAAI,EAAE,MAAM;IACZC,GAAG,EAAE,8NAA8N;IACnOC,WAAW,EAAE;EACf,CAAC;EACD,MAAM,EAAE;IACNF,IAAI,EAAE,MAAM;IACZC,GAAG,EAAE,8CAA8C;IACnDC,WAAW,EAAE;EACf,CAAC;EACD,KAAK,EAAE;IACLF,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,2DAA2D;IAChEC,WAAW,EAAE;EACf,CAAC;EACD,MAAM,EAAE;IACNF,IAAI,EAAE,MAAM;IACZC,GAAG,EAAE,8CAA8C;IACnDC,WAAW,EAAE;EACf,CAAC;EACD,IAAI,EAAE;IACJF,IAAI,EAAE,IAAI;IACVC,GAAG,EAAE,4CAA4C;IACjDC,WAAW,EAAE;EACf,CAAC;EACD,QAAQ,EAAE;IACRF,IAAI,EAAE,QAAQ;IACdC,GAAG,EAAE,gDAAgD;IACrDC,WAAW,EAAE;EACf,CAAC;EACD,QAAQ,EAAE;IACRF,IAAI,EAAE,QAAQ;IACdC,GAAG,EAAE,gDAAgD;IACrDC,WAAW,EAAE;EACf,CAAC;EACD,KAAK,EAAE;IACLF,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,6CAA6C;IAClDC,WAAW,EAAE;EACf,CAAC;EACD,KAAK,EAAE;IACLF,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,6CAA6C;IAClDC,WAAW,EAAE;EACf,CAAC;EACD,KAAK,EAAE;IACLF,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,6CAA6C;IAClDC,WAAW,EAAE;EACf,CAAC;EACD,KAAK,EAAE;IACLF,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,mDAAmD;IACxDC,WAAW,EAAE;EACf,CAAC;EACD,OAAO,EAAE;IACPF,IAAI,EAAE,OAAO;IACbC,GAAG,EAAE,iIAAiI;IACtIC,WAAW,EAAE;EACf,CAAC;EACD,OAAO,EAAE;IACPF,IAAI,EAAE,OAAO;IACbC,GAAG,EAAE,+CAA+C;IACpDC,WAAW,EAAE;EACf,CAAC;EACD,OAAO,EAAE;IACPF,IAAI,EAAE,WAAW;IACjBC,GAAG,EAAE,mDAAmD;IACxDC,WAAW,EAAE;EACf,CAAC;EACD,OAAO,EAAE;IACPF,IAAI,EAAE,OAAO;IACbC,GAAG,EAAE,+CAA+C;IACpDC,WAAW,EAAE;EACf,CAAC;EACD,OAAO,EAAE;IACPF,IAAI,EAAE,OAAO;IACbC,GAAG,EAAE,+CAA+C;IACpDC,WAAW,EAAE;EACf,CAAC;EACD,QAAQ,EAAE;IACRF,IAAI,EAAE,QAAQ;IACdC,GAAG,EAAE,gDAAgD;IACrDC,WAAW,EAAE;EACf,CAAC;EACD,KAAK,EAAE;IACLF,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,sDAAsD;IAC3DC,WAAW,EAAE;EACf;AACF,CAAC;AAED,MAAMC,YAAY,GAAGA,CAAC;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACzC,MAAM,CAAC1C,WAAW,EAAE2C,cAAc,CAAC,GAAGtG,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACuG,WAAW,EAAEC,cAAc,CAAC,GAAGxG,QAAQ,CAAC,OAAO,CAAC;EACvD,MAAM,CAACyG,MAAM,EAAEC,SAAS,CAAC,GAAG1G,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC2G,cAAc,EAAEC,iBAAiB,CAAC,GAAG5G,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM6G,SAAS,GAAG5G,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM6G,gBAAgB,GAAG7G,MAAM,CAAC,IAAI,CAAC;EACrC,MAAM8G,iBAAiB,GAAG9G,MAAM,CAAC,EAAE,CAAC;EAEpC,MAAM+G,gBAAgB,GAAG9G,WAAW,CAAE+G,KAAK,IAAK;IAC9CT,cAAc,CAACS,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC;EACpC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,cAAc,GAAGlH,WAAW,CAAC,MAAM;IACvC,IAAI,CAAC2G,SAAS,CAACQ,OAAO,EAAE;MACtBX,SAAS,CAAC,sBAAsB,CAAC;MACjC;IACF;IAEAI,gBAAgB,CAACO,OAAO,GAAG,IAAIC,aAAa,CAACT,SAAS,CAACQ,OAAO,CAACE,MAAM,EAAE;MACrEC,QAAQ,EAAE;IACZ,CAAC,CAAC;IAEFV,gBAAgB,CAACO,OAAO,CAACI,eAAe,GAAIR,KAAK,IAAK;MACpD,IAAIA,KAAK,CAACS,IAAI,CAACC,IAAI,GAAG,CAAC,EAAE;QACvBZ,iBAAiB,CAACM,OAAO,CAACO,IAAI,CAACX,KAAK,CAACS,IAAI,CAAC;MAC5C;IACF,CAAC;IAEDZ,gBAAgB,CAACO,OAAO,CAACQ,MAAM,GAAG,MAAM;MACtC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAChB,iBAAiB,CAACM,OAAO,EAAE;QAC/CtC,IAAI,EAAE;MACR,CAAC,CAAC;MACF,MAAMiD,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;MACrC,MAAMK,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAE1CzB,iBAAiB,CAAC0B,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;QAClCC,EAAE,EAAEJ,SAAS;QACbH,GAAG;QACHQ,IAAI,EAAEzC,gBAAgB,CAACQ,WAAW,CAAC,CAACP,IAAI;QACxCmC;MACF,CAAC,CAAC,CAAC;MAEHpB,iBAAiB,CAACM,OAAO,GAAG,EAAE;MAC9BX,SAAS,CAAC,+BAA+B,CAAC;IAC5C,CAAC;IAEDI,gBAAgB,CAACO,OAAO,CAACoB,KAAK,CAAC,CAAC;IAChCnC,cAAc,CAAC,IAAI,CAAC;IACpBI,SAAS,CAAC,sBAAsB,CAAC;EACnC,CAAC,EAAE,CAACH,WAAW,CAAC,CAAC;EAEjB,MAAMmC,aAAa,GAAGxI,WAAW,CAAC,MAAM;IACtC,IAAI4G,gBAAgB,CAACO,OAAO,IAAI1D,WAAW,EAAE;MAC3CmD,gBAAgB,CAACO,OAAO,CAACsB,IAAI,CAAC,CAAC;MAC/BrC,cAAc,CAAC,KAAK,CAAC;MACrBI,SAAS,CAAC,yBAAyB,CAAC;IACtC;EACF,CAAC,EAAE,CAAC/C,WAAW,CAAC,CAAC;EAEjB,MAAMiF,iBAAiB,GAAIC,KAAK,IAAK;IACnC,MAAMC,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACrCF,CAAC,CAACG,IAAI,GAAGJ,KAAK,CAACb,GAAG;IAClBc,CAAC,CAACI,QAAQ,GAAG,QAAQL,KAAK,CAACL,IAAI,IAAIK,KAAK,CAACV,SAAS,OAAO;IACzDW,CAAC,CAACK,KAAK,CAAC,CAAC;EACX,CAAC;EAID,oBACEpI,OAAA,CAACG,iBAAiB;IAAAkI,QAAA,gBAChBrI,OAAA,CAACM,UAAU;MAAA+H,QAAA,eACTrI,OAAA,CAACS,YAAY;QAAA4H,QAAA,gBACXrI,OAAA,CAACW,IAAI;UAAA0H,QAAA,gBACHrI,OAAA,CAACa,QAAQ;YAAAwH,QAAA,eACPrI,OAAA,CAACV,KAAK;cAACsH,IAAI,EAAE;YAAG;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,cAEb;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPzI,OAAA,CAACe,UAAU;UAAC2H,OAAO,EAAErD,YAAa;UAAAgD,QAAA,gBAChCrI,OAAA,CAACR,SAAS;YAACoH,IAAI,EAAE;UAAG;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEzB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAEbzI,OAAA,CAAC0B,WAAW;MAAA2G,QAAA,gBACVrI,OAAA;QAAK2I,KAAK,EAAE;UAAEC,SAAS,EAAE,QAAQ;UAAEC,YAAY,EAAE;QAAkB,CAAE;QAAAR,QAAA,eACnErI,OAAA,CAACwB,WAAW;UAAA6G,QAAA,gBACVrI,OAAA,CAACH,GAAG;YAAC+G,IAAI,EAAE;UAAG;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,wBAEnB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC,eAENzI,OAAA,CAACkB,SAAS;QAAAmH,QAAA,EAAC;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC,eAC1CzI,OAAA,CAACqB,YAAY;QAAAgH,QAAA,EAAC;MAEd;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC,eAEfzI,OAAA,CAAC6B,YAAY;QAAAwG,QAAA,gBACXrI,OAAA,CAAC+B,aAAa;UAAAsG,QAAA,gBACZrI,OAAA,CAACiC,YAAY;YAAAoG,QAAA,gBACXrI,OAAA,CAACoC,WAAW;cAAAiG,QAAA,eACVrI,OAAA,CAACT,MAAM;gBAACqH,IAAI,EAAE;cAAG;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,sBAEhB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,eACfzI,OAAA,CAACsC,eAAe;YAAA+F,QAAA,gBACdrI,OAAA,CAACwC,YAAY;cACXsG,GAAG,EAAEhD,SAAU;cACfiD,KAAK,EAAE,KAAM;cACbC,gBAAgB,EAAC,YAAY;cAC7BC,gBAAgB,EAAE;gBAChBC,KAAK,EAAE,GAAG;gBACVC,MAAM,EAAE,GAAG;gBACXC,UAAU,EAAE;cACd;YAAE;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFzI,OAAA,CAAC0C,gBAAgB;cAACE,WAAW,EAAEA,WAAY;cAAAyF,QAAA,EACxCzF,WAAW,gBACV5C,OAAA,CAAAE,SAAA;gBAAAmI,QAAA,gBACErI,OAAA;kBAAK2I,KAAK,EAAE;oBACVO,KAAK,EAAE,KAAK;oBACZC,MAAM,EAAE,KAAK;oBACbE,YAAY,EAAE,KAAK;oBACnBC,eAAe,EAAE,OAAO;oBACxBC,WAAW,EAAE;kBACf;gBAAE;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,aAEP;cAAA,eAAE,CAAC,gBAEHzI,OAAA,CAAAE,SAAA;gBAAAmI,QAAA,gBACErI,OAAA,CAACH,GAAG;kBAAC+G,IAAI,EAAE;gBAAG;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,SAEnB;cAAA,eAAE;YACH;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEhBzI,OAAA,CAAC8C,WAAW;UAAAuF,QAAA,gBACVrI,OAAA,CAACiC,YAAY;YAAAoG,QAAA,gBACXrI,OAAA,CAACoC,WAAW;cAAAiG,QAAA,eACVrI,OAAA,CAACF,MAAM;gBAAC8G,IAAI,EAAE;cAAG;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,iBAEhB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,eACfzI,OAAA,CAACgD,YAAY;YACXoD,KAAK,EAAEZ,WAAY;YACnBgE,QAAQ,EAAEvD,gBAAiB;YAC3BwD,QAAQ,EAAE7G,WAAY;YAAAyF,QAAA,EAErBqB,MAAM,CAACC,IAAI,CAAC3E,gBAAgB,CAAC,CAAC4E,GAAG,CAACC,OAAO,iBACxC7J,OAAA;cAAsBoG,KAAK,EAAEyD,OAAQ;cAAAxB,QAAA,EAClCrD,gBAAgB,CAAC6E,OAAO,CAAC,CAAC5E;YAAI,GADpB4E,OAAO;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEZ,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU,CAAC,eACfzI,OAAA,CAACmD,WAAW;YAAAkF,QAAA,gBACVrI,OAAA;cACE8J,GAAG,EAAE9E,gBAAgB,CAACQ,WAAW,CAAC,CAACN,GAAI;cACvC6E,GAAG,EAAE/E,gBAAgB,CAACQ,WAAW,CAAC,CAACP,IAAK;cACxC+E,OAAO,EAAGC,CAAC,IAAK;gBACdA,CAAC,CAAC9D,MAAM,CAACwC,KAAK,CAACuB,OAAO,GAAG,MAAM;gBAC/BD,CAAC,CAAC9D,MAAM,CAACgE,WAAW,CAACxB,KAAK,CAACuB,OAAO,GAAG,MAAM;cAC7C;YAAE;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFzI,OAAA;cAAK2I,KAAK,EAAE;gBAACuB,OAAO,EAAE,MAAM;gBAAEE,QAAQ,EAAE;cAAM,CAAE;cAAA/B,QAAA,EAAC;YAEjD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC,eACdzI,OAAA,CAACqD,QAAQ;YAAAgF,QAAA,EAAErD,gBAAgB,CAACQ,WAAW,CAAC,CAACP;UAAI;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eACzDzI,OAAA,CAACwD,eAAe;YAAA6E,QAAA,EACbrD,gBAAgB,CAACQ,WAAW,CAAC,CAACL;UAAW;YAAAmD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAEfzI,OAAA,CAAC0D,eAAe;QAAA2E,QAAA,eACdrI,OAAA,CAAC4D,aAAa;UACZC,OAAO,EAAC,SAAS;UACjB6E,OAAO,EAAE9F,WAAW,GAAG+E,aAAa,GAAGtB,cAAe;UAAAgC,QAAA,EAErDzF,WAAW,gBACV5C,OAAA,CAAAE,SAAA;YAAAmI,QAAA,gBACErI,OAAA,CAACN,MAAM;cAACkH,IAAI,EAAE;YAAG;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,yBAEtB;UAAA,eAAE,CAAC,gBAEHzI,OAAA,CAAAE,SAAA;YAAAmI,QAAA,gBACErI,OAAA,CAACP,IAAI;cAACmH,IAAI,EAAE;YAAG;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,0BAEpB;UAAA,eAAE;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,EAEjB/C,MAAM,iBACL1F,OAAA,CAAC+D,aAAa;QAACC,IAAI,EAAE0B,MAAM,CAAC2E,QAAQ,CAAC,OAAO,CAAC,GAAG,OAAO,GAAG3E,MAAM,CAAC2E,QAAQ,CAAC,SAAS,CAAC,GAAG,SAAS,GAAG,MAAO;QAAAhC,QAAA,EACvG3C;MAAM;QAAA4C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAChB,EAEA7C,cAAc,CAAC0E,MAAM,GAAG,CAAC,iBACxBtK,OAAA,CAACkE,iBAAiB;QAAAmE,QAAA,gBAChBrI,OAAA,CAACoE,eAAe;UAAAiE,QAAA,EAAC;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAiB,CAAC,eAC3DzI,OAAA,CAACsE,cAAc;UAAA+D,QAAA,EACZzC,cAAc,CAACgE,GAAG,CAAE9B,KAAK,iBACxB9H,OAAA,CAACwE,aAAa;YAAA6D,QAAA,gBACZrI,OAAA,CAAC0E,cAAc;cAAA2D,QAAA,EAAEP,KAAK,CAACL;YAAI;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAiB,CAAC,eAC7CzI,OAAA,CAAC4E,aAAa;cAAAyD,QAAA,EACX,IAAIhB,IAAI,CAACS,KAAK,CAACV,SAAS,CAAC,CAACmD,cAAc,CAAC;YAAC;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eAChBzI,OAAA,CAAC8E,cAAc;cAAC4D,OAAO,EAAEA,CAAA,KAAMb,iBAAiB,CAACC,KAAK,CAAE;cAAAO,QAAA,gBACtDrI,OAAA,CAACL,QAAQ;gBAACiH,IAAI,EAAE;cAAG;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,YAExB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAgB,CAAC;UAAA,GARCX,KAAK,CAACN,EAAE;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OASb,CAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CACpB;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACU,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAExB,CAAC;AAACnD,EAAA,CAjOIF,YAAY;AAAAoF,IAAA,GAAZpF,YAAY;AAmOlB,eAAeA,YAAY;AAAC,IAAA/E,EAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAI,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAyF,IAAA;AAAAC,YAAA,CAAApK,EAAA;AAAAoK,YAAA,CAAAjK,GAAA;AAAAiK,YAAA,CAAA/J,GAAA;AAAA+J,YAAA,CAAA7J,GAAA;AAAA6J,YAAA,CAAA3J,GAAA;AAAA2J,YAAA,CAAAxJ,GAAA;AAAAwJ,YAAA,CAAArJ,GAAA;AAAAqJ,YAAA,CAAAlJ,GAAA;AAAAkJ,YAAA,CAAAhJ,GAAA;AAAAgJ,YAAA,CAAA7I,GAAA;AAAA6I,YAAA,CAAA3I,GAAA;AAAA2I,YAAA,CAAAzI,IAAA;AAAAyI,YAAA,CAAAtI,IAAA;AAAAsI,YAAA,CAAApI,IAAA;AAAAoI,YAAA,CAAAlI,IAAA;AAAAkI,YAAA,CAAAhI,IAAA;AAAAgI,YAAA,CAAA5H,IAAA;AAAA4H,YAAA,CAAA1H,IAAA;AAAA0H,YAAA,CAAAvH,IAAA;AAAAuH,YAAA,CAAArH,IAAA;AAAAqH,YAAA,CAAAlH,IAAA;AAAAkH,YAAA,CAAAhH,IAAA;AAAAgH,YAAA,CAAA9G,IAAA;AAAA8G,YAAA,CAAA3G,IAAA;AAAA2G,YAAA,CAAAxG,IAAA;AAAAwG,YAAA,CAAAtG,IAAA;AAAAsG,YAAA,CAAApG,IAAA;AAAAoG,YAAA,CAAAlG,IAAA;AAAAkG,YAAA,CAAAhG,IAAA;AAAAgG,YAAA,CAAA9F,IAAA;AAAA8F,YAAA,CAAA5F,IAAA;AAAA4F,YAAA,CAAA1F,IAAA;AAAA0F,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}