<!DOCTYPE html>
<html>
<head>
    <title>Recording Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        .recording { background-color: #fff3cd; color: #856404; }
        button { padding: 10px 20px; margin: 5px; border: none; border-radius: 5px; cursor: pointer; }
        .start { background-color: #28a745; color: white; }
        .stop { background-color: #dc3545; color: white; }
        .log { background-color: #f8f9fa; padding: 10px; border-radius: 5px; max-height: 300px; overflow-y: auto; }
    </style>
</head>
<body>
    <h1>Recording Test</h1>
    
    <div id="status" class="status disconnected">Disconnected</div>
    
    <div>
        <label for="targetSign">Target Sign:</label>
        <input type="text" id="targetSign" value="hello" style="margin: 0 10px;">
        <button class="start" onclick="startRecording()">Start Recording</button>
        <button class="stop" onclick="stopRecording()">Stop Recording</button>
    </div>
    
    <div>
        <h3>Log:</h3>
        <div id="log" class="log"></div>
    </div>
    
    <script>
        const ws = new WebSocket('ws://localhost:8001/ws/detect');
        const status = document.getElementById('status');
        const log = document.getElementById('log');
        
        function addLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            log.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            log.scrollTop = log.scrollHeight;
        }
        
        ws.onopen = function(event) {
            status.textContent = 'Connected';
            status.className = 'status connected';
            addLog('Connected to backend');
        };
        
        ws.onmessage = function(event) {
            const data = JSON.parse(event.data);
            addLog(`Received: ${data.type}`);
            
            if (data.type === 'recording_started') {
                addLog(`Recording started for: ${data.target_sign}`);
            } else if (data.type === 'recording_stopped') {
                if (data.result) {
                    addLog(`Recording stopped: ${data.result.frame_count} frames saved`);
                } else {
                    addLog('Recording stopped: No frames recorded');
                }
            } else if (data.type === 'frame_processed') {
                if (data.prediction) {
                    addLog(`Predicted: ${data.prediction.sign} (${Math.round(data.prediction.confidence * 100)}%)`);
                    if (data.sign_matched) {
                        addLog('✅ Frame recorded!');
                    }
                }
            }
        };
        
        ws.onerror = function(error) {
            status.textContent = 'Error';
            status.className = 'status disconnected';
            addLog('WebSocket error');
        };
        
        ws.onclose = function(event) {
            status.textContent = 'Disconnected';
            status.className = 'status disconnected';
            addLog('Disconnected from backend');
        };
        
        function startRecording() {
            const targetSign = document.getElementById('targetSign').value;
            const message = {
                type: 'start_recording',
                target_sign: targetSign
            };
            ws.send(JSON.stringify(message));
            addLog(`Starting recording for: ${targetSign}`);
        }
        
        function stopRecording() {
            const message = {
                type: 'stop_recording'
            };
            ws.send(JSON.stringify(message));
            addLog('Stopping recording');
        }
    </script>
</body>
</html> 