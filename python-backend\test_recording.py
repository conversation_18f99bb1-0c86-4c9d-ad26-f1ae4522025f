#!/usr/bin/env python3
"""
Test script to verify recording functionality
"""
import asyncio
import json
import websockets
import base64
import numpy as np
import cv2

async def test_recording():
    """Test the recording functionality"""
    uri = "ws://localhost:8001/ws/detect"
    
    try:
        async with websockets.connect(uri) as websocket:
            print("✅ Connected to backend")
            
            # Start recording for a test sign
            start_msg = {
                "type": "start_recording",
                "target_sign": "hello"
            }
            await websocket.send(json.dumps(start_msg))
            print("📤 Sent start recording message")
            
            # Wait for response
            response = await websocket.receive()
            data = json.loads(response)
            print(f"📥 Received: {data['type']}")
            
            # Send a test frame with high confidence prediction
            # Create a dummy image
            test_image = np.zeros((480, 640, 3), dtype=np.uint8)
            test_image[:] = (128, 128, 128)  # Gray background
            
            # Encode to base64
            _, buffer = cv2.imencode('.jpg', test_image)
            image_b64 = base64.b64encode(buffer).decode('utf-8')
            
            frame_msg = {
                "type": "frame",
                "frame": f"data:image/jpeg;base64,{image_b64}"
            }
            
            # Send multiple frames to simulate detection
            for i in range(5):
                await websocket.send(json.dumps(frame_msg))
                print(f"📤 Sent frame {i+1}")
                
                # Wait for response
                response = await websocket.receive()
                data = json.loads(response)
                print(f"📥 Frame {i+1} response: {data['type']}")
                
                if data.get('sign_matched'):
                    print(f"✅ Frame {i+1} matched and recorded!")
                
                await asyncio.sleep(0.5)
            
            # Stop recording
            stop_msg = {
                "type": "stop_recording"
            }
            await websocket.send(json.dumps(stop_msg))
            print("📤 Sent stop recording message")
            
            # Wait for response
            response = await websocket.receive()
            data = json.loads(response)
            print(f"📥 Stop response: {data}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    asyncio.run(test_recording()) 