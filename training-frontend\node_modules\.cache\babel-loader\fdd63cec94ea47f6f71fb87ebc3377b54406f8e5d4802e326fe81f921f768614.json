{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState, useRef, useCallback, useEffect } from 'react';\nconst BACKEND_URL = 'ws://localhost:8001/ws/detect';\nexport const useSignDetection = () => {\n  _s();\n  const [isConnected, setIsConnected] = useState(false);\n  const [prediction, setPrediction] = useState(null);\n  const [lastPrediction, setLastPrediction] = useState(null);\n  const [isRecording, setIsRecording] = useState(false);\n  const [recordingStatus, setRecordingStatus] = useState('');\n  const [processedFrame, setProcessedFrame] = useState(null);\n  const [signMatched, setSignMatched] = useState(false);\n  const [targetSign, setTargetSign] = useState('');\n  const [predictionHistory, setPredictionHistory] = useState([]);\n  const wsRef = useRef(null);\n  const reconnectTimeoutRef = useRef(null);\n  const frameIntervalRef = useRef(null);\n  const connect = useCallback(() => {\n    try {\n      wsRef.current = new WebSocket(BACKEND_URL);\n      wsRef.current.onopen = () => {\n        console.log('Connected to sign detection backend');\n        setIsConnected(true);\n        setRecordingStatus('Connected to AI backend');\n      };\n      wsRef.current.onmessage = event => {\n        try {\n          const data = JSON.parse(event.data);\n          console.log('Received from backend:', data.type, data.prediction);\n          switch (data.type) {\n            case 'frame_processed':\n              // Always update processed frame\n              setProcessedFrame(data.processed_frame);\n              setSignMatched(data.sign_matched || false);\n              setTargetSign(data.target_sign || '');\n\n              // Update prediction - keep last prediction if new one is null\n              if (data.prediction) {\n                setPrediction(data.prediction);\n                setLastPrediction(data.prediction);\n\n                // Add to prediction history (keep last 5)\n                setPredictionHistory(prev => {\n                  const newHistory = [data.prediction, ...prev.slice(0, 4)];\n                  return newHistory;\n                });\n                console.log(`Detected: ${data.prediction.sign} (${Math.round(data.prediction.confidence * 100)}%)`);\n              } else if (lastPrediction) {\n                // Keep showing last prediction with reduced opacity\n                setPrediction({\n                  ...lastPrediction,\n                  confidence: lastPrediction.confidence * 0.7,\n                  isStale: true\n                });\n              }\n              break;\n            case 'recording_started':\n              setIsRecording(true);\n              setRecordingStatus(`Recording started for: ${data.target_sign}`);\n              break;\n            case 'recording_stopped':\n              setIsRecording(false);\n              if (data.result) {\n                setRecordingStatus(`Recording saved: ${data.result.frame_count} frames`);\n              } else {\n                setRecordingStatus('Recording stopped');\n              }\n              break;\n            default:\n              console.log('Unknown message type:', data.type);\n          }\n        } catch (error) {\n          console.error('Error parsing WebSocket message:', error);\n        }\n      };\n      wsRef.current.onclose = () => {\n        console.log('Disconnected from sign detection backend');\n        setIsConnected(false);\n        setRecordingStatus('Disconnected from backend');\n\n        // Attempt to reconnect after 3 seconds\n        reconnectTimeoutRef.current = setTimeout(() => {\n          console.log('Attempting to reconnect...');\n          connect();\n        }, 3000);\n      };\n      wsRef.current.onerror = error => {\n        console.error('WebSocket error:', error);\n        setRecordingStatus('Connection error');\n      };\n    } catch (error) {\n      console.error('Error connecting to WebSocket:', error);\n      setRecordingStatus('Failed to connect to backend');\n    }\n  }, []);\n  const disconnect = useCallback(() => {\n    if (reconnectTimeoutRef.current) {\n      clearTimeout(reconnectTimeoutRef.current);\n    }\n    if (frameIntervalRef.current) {\n      clearInterval(frameIntervalRef.current);\n    }\n    if (wsRef.current) {\n      wsRef.current.close();\n      wsRef.current = null;\n    }\n    setIsConnected(false);\n  }, []);\n  const sendFrame = useCallback(frameData => {\n    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {\n      const message = {\n        type: 'frame',\n        frame: frameData\n      };\n      wsRef.current.send(JSON.stringify(message));\n    }\n  }, []);\n  const startRecording = useCallback(targetSignName => {\n    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {\n      const message = {\n        type: 'start_recording',\n        target_sign: targetSignName\n      };\n      wsRef.current.send(JSON.stringify(message));\n      setTargetSign(targetSignName);\n    }\n  }, []);\n  const stopRecording = useCallback(() => {\n    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {\n      const message = {\n        type: 'stop_recording'\n      };\n      wsRef.current.send(JSON.stringify(message));\n    }\n  }, []);\n  const startFrameCapture = useCallback((webcamRef, interval = 200) => {\n    if (frameIntervalRef.current) {\n      clearInterval(frameIntervalRef.current);\n    }\n    frameIntervalRef.current = setInterval(() => {\n      if (webcamRef.current && isConnected) {\n        const imageSrc = webcamRef.current.getScreenshot();\n        if (imageSrc) {\n          console.log('Sending frame to backend');\n          sendFrame(imageSrc);\n        }\n      }\n    }, interval);\n  }, [isConnected, sendFrame]);\n  const stopFrameCapture = useCallback(() => {\n    if (frameIntervalRef.current) {\n      clearInterval(frameIntervalRef.current);\n      frameIntervalRef.current = null;\n    }\n  }, []);\n\n  // Auto-connect on mount\n  useEffect(() => {\n    connect();\n    return () => {\n      disconnect();\n    };\n  }, [connect, disconnect]);\n  return {\n    isConnected,\n    prediction,\n    lastPrediction,\n    predictionHistory,\n    isRecording,\n    recordingStatus,\n    processedFrame,\n    signMatched,\n    targetSign,\n    connect,\n    disconnect,\n    sendFrame,\n    startRecording,\n    stopRecording,\n    startFrameCapture,\n    stopFrameCapture\n  };\n};\n_s(useSignDetection, \"qBrQ5r1uS5TkWGTMxAZudI9E/s8=\");", "map": {"version": 3, "names": ["useState", "useRef", "useCallback", "useEffect", "BACKEND_URL", "useSignDetection", "_s", "isConnected", "setIsConnected", "prediction", "setPrediction", "lastPrediction", "setLastPrediction", "isRecording", "setIsRecording", "recordingStatus", "setRecordingStatus", "processedFrame", "setProcessedFrame", "signMatched", "setSignMatched", "targetSign", "setTargetSign", "predictionHistory", "setPredictionHistory", "wsRef", "reconnectTimeoutRef", "frameIntervalRef", "connect", "current", "WebSocket", "onopen", "console", "log", "onmessage", "event", "data", "JSON", "parse", "type", "processed_frame", "sign_matched", "target_sign", "prev", "newHistory", "slice", "sign", "Math", "round", "confidence", "isStale", "result", "frame_count", "error", "onclose", "setTimeout", "onerror", "disconnect", "clearTimeout", "clearInterval", "close", "sendFrame", "frameData", "readyState", "OPEN", "message", "frame", "send", "stringify", "startRecording", "targetSignName", "stopRecording", "startFrameCapture", "webcamRef", "interval", "setInterval", "imageSrc", "getScreenshot", "stopFrameCapture"], "sources": ["D:/ASL/training-frontend/src/hooks/useSignDetection.js"], "sourcesContent": ["import { useState, useRef, useCallback, useEffect } from 'react';\n\nconst BACKEND_URL = 'ws://localhost:8001/ws/detect';\n\nexport const useSignDetection = () => {\n  const [isConnected, setIsConnected] = useState(false);\n  const [prediction, setPrediction] = useState(null);\n  const [lastPrediction, setLastPrediction] = useState(null);\n  const [isRecording, setIsRecording] = useState(false);\n  const [recordingStatus, setRecordingStatus] = useState('');\n  const [processedFrame, setProcessedFrame] = useState(null);\n  const [signMatched, setSignMatched] = useState(false);\n  const [targetSign, setTargetSign] = useState('');\n  const [predictionHistory, setPredictionHistory] = useState([]);\n  \n  const wsRef = useRef(null);\n  const reconnectTimeoutRef = useRef(null);\n  const frameIntervalRef = useRef(null);\n  \n  const connect = useCallback(() => {\n    try {\n      wsRef.current = new WebSocket(BACKEND_URL);\n      \n      wsRef.current.onopen = () => {\n        console.log('Connected to sign detection backend');\n        setIsConnected(true);\n        setRecordingStatus('Connected to AI backend');\n      };\n      \n      wsRef.current.onmessage = (event) => {\n        try {\n          const data = JSON.parse(event.data);\n          console.log('Received from backend:', data.type, data.prediction);\n\n          switch (data.type) {\n            case 'frame_processed':\n              // Always update processed frame\n              setProcessedFrame(data.processed_frame);\n              setSignMatched(data.sign_matched || false);\n              setTargetSign(data.target_sign || '');\n\n              // Update prediction - keep last prediction if new one is null\n              if (data.prediction) {\n                setPrediction(data.prediction);\n                setLastPrediction(data.prediction);\n\n                // Add to prediction history (keep last 5)\n                setPredictionHistory(prev => {\n                  const newHistory = [data.prediction, ...prev.slice(0, 4)];\n                  return newHistory;\n                });\n\n                console.log(`Detected: ${data.prediction.sign} (${Math.round(data.prediction.confidence * 100)}%)`);\n              } else if (lastPrediction) {\n                // Keep showing last prediction with reduced opacity\n                setPrediction({\n                  ...lastPrediction,\n                  confidence: lastPrediction.confidence * 0.7,\n                  isStale: true\n                });\n              }\n              break;\n              \n            case 'recording_started':\n              setIsRecording(true);\n              setRecordingStatus(`Recording started for: ${data.target_sign}`);\n              break;\n              \n            case 'recording_stopped':\n              setIsRecording(false);\n              if (data.result) {\n                setRecordingStatus(`Recording saved: ${data.result.frame_count} frames`);\n              } else {\n                setRecordingStatus('Recording stopped');\n              }\n              break;\n              \n            default:\n              console.log('Unknown message type:', data.type);\n          }\n        } catch (error) {\n          console.error('Error parsing WebSocket message:', error);\n        }\n      };\n      \n      wsRef.current.onclose = () => {\n        console.log('Disconnected from sign detection backend');\n        setIsConnected(false);\n        setRecordingStatus('Disconnected from backend');\n        \n        // Attempt to reconnect after 3 seconds\n        reconnectTimeoutRef.current = setTimeout(() => {\n          console.log('Attempting to reconnect...');\n          connect();\n        }, 3000);\n      };\n      \n      wsRef.current.onerror = (error) => {\n        console.error('WebSocket error:', error);\n        setRecordingStatus('Connection error');\n      };\n      \n    } catch (error) {\n      console.error('Error connecting to WebSocket:', error);\n      setRecordingStatus('Failed to connect to backend');\n    }\n  }, []);\n  \n  const disconnect = useCallback(() => {\n    if (reconnectTimeoutRef.current) {\n      clearTimeout(reconnectTimeoutRef.current);\n    }\n    \n    if (frameIntervalRef.current) {\n      clearInterval(frameIntervalRef.current);\n    }\n    \n    if (wsRef.current) {\n      wsRef.current.close();\n      wsRef.current = null;\n    }\n    \n    setIsConnected(false);\n  }, []);\n  \n  const sendFrame = useCallback((frameData) => {\n    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {\n      const message = {\n        type: 'frame',\n        frame: frameData\n      };\n      wsRef.current.send(JSON.stringify(message));\n    }\n  }, []);\n  \n  const startRecording = useCallback((targetSignName) => {\n    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {\n      const message = {\n        type: 'start_recording',\n        target_sign: targetSignName\n      };\n      wsRef.current.send(JSON.stringify(message));\n      setTargetSign(targetSignName);\n    }\n  }, []);\n  \n  const stopRecording = useCallback(() => {\n    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {\n      const message = {\n        type: 'stop_recording'\n      };\n      wsRef.current.send(JSON.stringify(message));\n    }\n  }, []);\n  \n  const startFrameCapture = useCallback((webcamRef, interval = 200) => {\n    if (frameIntervalRef.current) {\n      clearInterval(frameIntervalRef.current);\n    }\n\n    frameIntervalRef.current = setInterval(() => {\n      if (webcamRef.current && isConnected) {\n        const imageSrc = webcamRef.current.getScreenshot();\n        if (imageSrc) {\n          console.log('Sending frame to backend');\n          sendFrame(imageSrc);\n        }\n      }\n    }, interval);\n  }, [isConnected, sendFrame]);\n  \n  const stopFrameCapture = useCallback(() => {\n    if (frameIntervalRef.current) {\n      clearInterval(frameIntervalRef.current);\n      frameIntervalRef.current = null;\n    }\n  }, []);\n  \n  // Auto-connect on mount\n  useEffect(() => {\n    connect();\n    \n    return () => {\n      disconnect();\n    };\n  }, [connect, disconnect]);\n  \n  return {\n    isConnected,\n    prediction,\n    lastPrediction,\n    predictionHistory,\n    isRecording,\n    recordingStatus,\n    processedFrame,\n    signMatched,\n    targetSign,\n    connect,\n    disconnect,\n    sendFrame,\n    startRecording,\n    stopRecording,\n    startFrameCapture,\n    stopFrameCapture\n  };\n};\n"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,MAAM,EAAEC,WAAW,EAAEC,SAAS,QAAQ,OAAO;AAEhE,MAAMC,WAAW,GAAG,+BAA+B;AAEnD,OAAO,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGR,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACS,UAAU,EAAEC,aAAa,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACW,cAAc,EAAEC,iBAAiB,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACa,WAAW,EAAEC,cAAc,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACe,eAAe,EAAEC,kBAAkB,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACiB,cAAc,EAAEC,iBAAiB,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACmB,WAAW,EAAEC,cAAc,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACqB,UAAU,EAAEC,aAAa,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACuB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAE9D,MAAMyB,KAAK,GAAGxB,MAAM,CAAC,IAAI,CAAC;EAC1B,MAAMyB,mBAAmB,GAAGzB,MAAM,CAAC,IAAI,CAAC;EACxC,MAAM0B,gBAAgB,GAAG1B,MAAM,CAAC,IAAI,CAAC;EAErC,MAAM2B,OAAO,GAAG1B,WAAW,CAAC,MAAM;IAChC,IAAI;MACFuB,KAAK,CAACI,OAAO,GAAG,IAAIC,SAAS,CAAC1B,WAAW,CAAC;MAE1CqB,KAAK,CAACI,OAAO,CAACE,MAAM,GAAG,MAAM;QAC3BC,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;QAClDzB,cAAc,CAAC,IAAI,CAAC;QACpBQ,kBAAkB,CAAC,yBAAyB,CAAC;MAC/C,CAAC;MAEDS,KAAK,CAACI,OAAO,CAACK,SAAS,GAAIC,KAAK,IAAK;QACnC,IAAI;UACF,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,KAAK,CAACC,IAAI,CAAC;UACnCJ,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEG,IAAI,CAACG,IAAI,EAAEH,IAAI,CAAC3B,UAAU,CAAC;UAEjE,QAAQ2B,IAAI,CAACG,IAAI;YACf,KAAK,iBAAiB;cACpB;cACArB,iBAAiB,CAACkB,IAAI,CAACI,eAAe,CAAC;cACvCpB,cAAc,CAACgB,IAAI,CAACK,YAAY,IAAI,KAAK,CAAC;cAC1CnB,aAAa,CAACc,IAAI,CAACM,WAAW,IAAI,EAAE,CAAC;;cAErC;cACA,IAAIN,IAAI,CAAC3B,UAAU,EAAE;gBACnBC,aAAa,CAAC0B,IAAI,CAAC3B,UAAU,CAAC;gBAC9BG,iBAAiB,CAACwB,IAAI,CAAC3B,UAAU,CAAC;;gBAElC;gBACAe,oBAAoB,CAACmB,IAAI,IAAI;kBAC3B,MAAMC,UAAU,GAAG,CAACR,IAAI,CAAC3B,UAAU,EAAE,GAAGkC,IAAI,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;kBACzD,OAAOD,UAAU;gBACnB,CAAC,CAAC;gBAEFZ,OAAO,CAACC,GAAG,CAAC,aAAaG,IAAI,CAAC3B,UAAU,CAACqC,IAAI,KAAKC,IAAI,CAACC,KAAK,CAACZ,IAAI,CAAC3B,UAAU,CAACwC,UAAU,GAAG,GAAG,CAAC,IAAI,CAAC;cACrG,CAAC,MAAM,IAAItC,cAAc,EAAE;gBACzB;gBACAD,aAAa,CAAC;kBACZ,GAAGC,cAAc;kBACjBsC,UAAU,EAAEtC,cAAc,CAACsC,UAAU,GAAG,GAAG;kBAC3CC,OAAO,EAAE;gBACX,CAAC,CAAC;cACJ;cACA;YAEF,KAAK,mBAAmB;cACtBpC,cAAc,CAAC,IAAI,CAAC;cACpBE,kBAAkB,CAAC,0BAA0BoB,IAAI,CAACM,WAAW,EAAE,CAAC;cAChE;YAEF,KAAK,mBAAmB;cACtB5B,cAAc,CAAC,KAAK,CAAC;cACrB,IAAIsB,IAAI,CAACe,MAAM,EAAE;gBACfnC,kBAAkB,CAAC,oBAAoBoB,IAAI,CAACe,MAAM,CAACC,WAAW,SAAS,CAAC;cAC1E,CAAC,MAAM;gBACLpC,kBAAkB,CAAC,mBAAmB,CAAC;cACzC;cACA;YAEF;cACEgB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEG,IAAI,CAACG,IAAI,CAAC;UACnD;QACF,CAAC,CAAC,OAAOc,KAAK,EAAE;UACdrB,OAAO,CAACqB,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QAC1D;MACF,CAAC;MAED5B,KAAK,CAACI,OAAO,CAACyB,OAAO,GAAG,MAAM;QAC5BtB,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;QACvDzB,cAAc,CAAC,KAAK,CAAC;QACrBQ,kBAAkB,CAAC,2BAA2B,CAAC;;QAE/C;QACAU,mBAAmB,CAACG,OAAO,GAAG0B,UAAU,CAAC,MAAM;UAC7CvB,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;UACzCL,OAAO,CAAC,CAAC;QACX,CAAC,EAAE,IAAI,CAAC;MACV,CAAC;MAEDH,KAAK,CAACI,OAAO,CAAC2B,OAAO,GAAIH,KAAK,IAAK;QACjCrB,OAAO,CAACqB,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;QACxCrC,kBAAkB,CAAC,kBAAkB,CAAC;MACxC,CAAC;IAEH,CAAC,CAAC,OAAOqC,KAAK,EAAE;MACdrB,OAAO,CAACqB,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtDrC,kBAAkB,CAAC,8BAA8B,CAAC;IACpD;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMyC,UAAU,GAAGvD,WAAW,CAAC,MAAM;IACnC,IAAIwB,mBAAmB,CAACG,OAAO,EAAE;MAC/B6B,YAAY,CAAChC,mBAAmB,CAACG,OAAO,CAAC;IAC3C;IAEA,IAAIF,gBAAgB,CAACE,OAAO,EAAE;MAC5B8B,aAAa,CAAChC,gBAAgB,CAACE,OAAO,CAAC;IACzC;IAEA,IAAIJ,KAAK,CAACI,OAAO,EAAE;MACjBJ,KAAK,CAACI,OAAO,CAAC+B,KAAK,CAAC,CAAC;MACrBnC,KAAK,CAACI,OAAO,GAAG,IAAI;IACtB;IAEArB,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMqD,SAAS,GAAG3D,WAAW,CAAE4D,SAAS,IAAK;IAC3C,IAAIrC,KAAK,CAACI,OAAO,IAAIJ,KAAK,CAACI,OAAO,CAACkC,UAAU,KAAKjC,SAAS,CAACkC,IAAI,EAAE;MAChE,MAAMC,OAAO,GAAG;QACd1B,IAAI,EAAE,OAAO;QACb2B,KAAK,EAAEJ;MACT,CAAC;MACDrC,KAAK,CAACI,OAAO,CAACsC,IAAI,CAAC9B,IAAI,CAAC+B,SAAS,CAACH,OAAO,CAAC,CAAC;IAC7C;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,cAAc,GAAGnE,WAAW,CAAEoE,cAAc,IAAK;IACrD,IAAI7C,KAAK,CAACI,OAAO,IAAIJ,KAAK,CAACI,OAAO,CAACkC,UAAU,KAAKjC,SAAS,CAACkC,IAAI,EAAE;MAChE,MAAMC,OAAO,GAAG;QACd1B,IAAI,EAAE,iBAAiB;QACvBG,WAAW,EAAE4B;MACf,CAAC;MACD7C,KAAK,CAACI,OAAO,CAACsC,IAAI,CAAC9B,IAAI,CAAC+B,SAAS,CAACH,OAAO,CAAC,CAAC;MAC3C3C,aAAa,CAACgD,cAAc,CAAC;IAC/B;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,aAAa,GAAGrE,WAAW,CAAC,MAAM;IACtC,IAAIuB,KAAK,CAACI,OAAO,IAAIJ,KAAK,CAACI,OAAO,CAACkC,UAAU,KAAKjC,SAAS,CAACkC,IAAI,EAAE;MAChE,MAAMC,OAAO,GAAG;QACd1B,IAAI,EAAE;MACR,CAAC;MACDd,KAAK,CAACI,OAAO,CAACsC,IAAI,CAAC9B,IAAI,CAAC+B,SAAS,CAACH,OAAO,CAAC,CAAC;IAC7C;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMO,iBAAiB,GAAGtE,WAAW,CAAC,CAACuE,SAAS,EAAEC,QAAQ,GAAG,GAAG,KAAK;IACnE,IAAI/C,gBAAgB,CAACE,OAAO,EAAE;MAC5B8B,aAAa,CAAChC,gBAAgB,CAACE,OAAO,CAAC;IACzC;IAEAF,gBAAgB,CAACE,OAAO,GAAG8C,WAAW,CAAC,MAAM;MAC3C,IAAIF,SAAS,CAAC5C,OAAO,IAAItB,WAAW,EAAE;QACpC,MAAMqE,QAAQ,GAAGH,SAAS,CAAC5C,OAAO,CAACgD,aAAa,CAAC,CAAC;QAClD,IAAID,QAAQ,EAAE;UACZ5C,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;UACvC4B,SAAS,CAACe,QAAQ,CAAC;QACrB;MACF;IACF,CAAC,EAAEF,QAAQ,CAAC;EACd,CAAC,EAAE,CAACnE,WAAW,EAAEsD,SAAS,CAAC,CAAC;EAE5B,MAAMiB,gBAAgB,GAAG5E,WAAW,CAAC,MAAM;IACzC,IAAIyB,gBAAgB,CAACE,OAAO,EAAE;MAC5B8B,aAAa,CAAChC,gBAAgB,CAACE,OAAO,CAAC;MACvCF,gBAAgB,CAACE,OAAO,GAAG,IAAI;IACjC;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA1B,SAAS,CAAC,MAAM;IACdyB,OAAO,CAAC,CAAC;IAET,OAAO,MAAM;MACX6B,UAAU,CAAC,CAAC;IACd,CAAC;EACH,CAAC,EAAE,CAAC7B,OAAO,EAAE6B,UAAU,CAAC,CAAC;EAEzB,OAAO;IACLlD,WAAW;IACXE,UAAU;IACVE,cAAc;IACdY,iBAAiB;IACjBV,WAAW;IACXE,eAAe;IACfE,cAAc;IACdE,WAAW;IACXE,UAAU;IACVO,OAAO;IACP6B,UAAU;IACVI,SAAS;IACTQ,cAAc;IACdE,aAAa;IACbC,iBAAiB;IACjBM;EACF,CAAC;AACH,CAAC;AAACxE,EAAA,CAzMWD,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}