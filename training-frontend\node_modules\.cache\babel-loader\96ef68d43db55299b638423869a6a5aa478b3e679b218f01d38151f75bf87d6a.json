{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState, useRef, useCallback, useEffect } from 'react';\nconst BACKEND_URL = 'ws://localhost:8001/ws/detect';\nexport const useSignDetection = () => {\n  _s();\n  const [isConnected, setIsConnected] = useState(false);\n  const [prediction, setPrediction] = useState(null);\n  const [lastPrediction, setLastPrediction] = useState(null);\n  const [isRecording, setIsRecording] = useState(false);\n  const [recordingStatus, setRecordingStatus] = useState('');\n  const [processedFrame, setProcessedFrame] = useState(null);\n  const [signMatched, setSignMatched] = useState(false);\n  const [targetSign, setTargetSign] = useState('');\n  const [predictionHistory, setPredictionHistory] = useState([]);\n  const wsRef = useRef(null);\n  const reconnectTimeoutRef = useRef(null);\n  const frameIntervalRef = useRef(null);\n  const connect = useCallback(() => {\n    try {\n      wsRef.current = new WebSocket(BACKEND_URL);\n      wsRef.current.onopen = () => {\n        console.log('Connected to sign detection backend');\n        setIsConnected(true);\n        setRecordingStatus('');\n      };\n      wsRef.current.onmessage = event => {\n        try {\n          const data = JSON.parse(event.data);\n          console.log('Received from backend:', data.type, data.prediction);\n          switch (data.type) {\n            case 'frame_processed':\n              // Always update processed frame\n              setProcessedFrame(data.processed_frame);\n              setSignMatched(data.sign_matched || false);\n              setTargetSign(data.target_sign || '');\n\n              // Update prediction - keep last prediction if new one is null\n              if (data.prediction) {\n                setPrediction(data.prediction);\n                setLastPrediction(data.prediction);\n\n                // Add to prediction history (keep last 5)\n                setPredictionHistory(prev => {\n                  const newHistory = [data.prediction, ...prev.slice(0, 4)];\n                  return newHistory;\n                });\n                console.log(`Detected: ${data.prediction.sign} (${Math.round(data.prediction.confidence * 100)}%)`);\n\n                // Show recording session status\n                if (data.should_start_session) {\n                  setRecordingStatus(`🎬 Started recording session for ${data.target_sign} (3 seconds)`);\n                } else if (data.is_in_session && data.sign_matched) {\n                  setRecordingStatus(`📹 Recording session active for ${data.target_sign}...`);\n                }\n              } else if (lastPrediction) {\n                // Keep showing last prediction with reduced opacity\n                setPrediction({\n                  ...lastPrediction,\n                  confidence: lastPrediction.confidence * 0.7,\n                  isStale: true\n                });\n              }\n              break;\n            case 'recording_started':\n              setIsRecording(true);\n              setRecordingStatus(`Ready to record: ${data.target_sign} (will auto-record for 3s when detected)`);\n              break;\n            case 'recording_stopped':\n              setIsRecording(false);\n              if (data.result) {\n                const reason = data.reason === 'auto_stop_session_complete' ? 'Session completed' : 'Manual stop';\n                setRecordingStatus(`✅ Recording saved: ${data.result.frame_count} frames (${reason})`);\n              } else {\n                setRecordingStatus('Recording stopped');\n              }\n              break;\n            default:\n              console.log('Unknown message type:', data.type);\n          }\n        } catch (error) {\n          console.error('Error parsing WebSocket message:', error);\n        }\n      };\n      wsRef.current.onclose = () => {\n        console.log('Disconnected from sign detection backend');\n        setIsConnected(false);\n        setRecordingStatus('Connection lost - Click retry to reconnect');\n\n        // Attempt to reconnect after 3 seconds\n        reconnectTimeoutRef.current = setTimeout(() => {\n          console.log('Attempting to reconnect...');\n          connect();\n        }, 3000);\n      };\n      wsRef.current.onerror = error => {\n        console.error('WebSocket error:', error);\n        setRecordingStatus('Connection error');\n      };\n    } catch (error) {\n      console.error('Error connecting to WebSocket:', error);\n      setRecordingStatus('Failed to connect to backend');\n    }\n  }, []);\n  const disconnect = useCallback(() => {\n    if (reconnectTimeoutRef.current) {\n      clearTimeout(reconnectTimeoutRef.current);\n    }\n    if (frameIntervalRef.current) {\n      clearInterval(frameIntervalRef.current);\n    }\n    if (wsRef.current) {\n      wsRef.current.close();\n      wsRef.current = null;\n    }\n    setIsConnected(false);\n  }, []);\n  const sendFrame = useCallback(frameData => {\n    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {\n      const message = {\n        type: 'frame',\n        frame: frameData\n      };\n      wsRef.current.send(JSON.stringify(message));\n    }\n  }, []);\n  const startRecording = useCallback(targetSignName => {\n    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {\n      const message = {\n        type: 'start_recording',\n        target_sign: targetSignName\n      };\n      wsRef.current.send(JSON.stringify(message));\n      setTargetSign(targetSignName);\n    }\n  }, []);\n  const stopRecording = useCallback(() => {\n    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {\n      const message = {\n        type: 'stop_recording'\n      };\n      wsRef.current.send(JSON.stringify(message));\n    }\n  }, []);\n  const startFrameCapture = useCallback((webcamRef, interval = 200) => {\n    if (frameIntervalRef.current) {\n      clearInterval(frameIntervalRef.current);\n    }\n    frameIntervalRef.current = setInterval(() => {\n      if (webcamRef.current && isConnected) {\n        const imageSrc = webcamRef.current.getScreenshot();\n        if (imageSrc) {\n          console.log('Sending frame to backend');\n          sendFrame(imageSrc);\n        }\n      }\n    }, interval);\n  }, [isConnected, sendFrame]);\n  const stopFrameCapture = useCallback(() => {\n    if (frameIntervalRef.current) {\n      clearInterval(frameIntervalRef.current);\n      frameIntervalRef.current = null;\n    }\n  }, []);\n\n  // Auto-connect on mount\n  useEffect(() => {\n    connect();\n    return () => {\n      disconnect();\n    };\n  }, [connect, disconnect]);\n  const retryConnection = useCallback(() => {\n    setRecordingStatus('Attempting to reconnect...');\n    disconnect();\n    setTimeout(() => {\n      connect();\n    }, 1000);\n  }, [connect, disconnect]);\n  return {\n    isConnected,\n    prediction,\n    lastPrediction,\n    predictionHistory,\n    isRecording,\n    recordingStatus,\n    processedFrame,\n    signMatched,\n    targetSign,\n    connect,\n    disconnect,\n    retryConnection,\n    sendFrame,\n    startRecording,\n    stopRecording,\n    startFrameCapture,\n    stopFrameCapture\n  };\n};\n_s(useSignDetection, \"/TkO+UrqGWJUTNxbPg6JMRQWC00=\");", "map": {"version": 3, "names": ["useState", "useRef", "useCallback", "useEffect", "BACKEND_URL", "useSignDetection", "_s", "isConnected", "setIsConnected", "prediction", "setPrediction", "lastPrediction", "setLastPrediction", "isRecording", "setIsRecording", "recordingStatus", "setRecordingStatus", "processedFrame", "setProcessedFrame", "signMatched", "setSignMatched", "targetSign", "setTargetSign", "predictionHistory", "setPredictionHistory", "wsRef", "reconnectTimeoutRef", "frameIntervalRef", "connect", "current", "WebSocket", "onopen", "console", "log", "onmessage", "event", "data", "JSON", "parse", "type", "processed_frame", "sign_matched", "target_sign", "prev", "newHistory", "slice", "sign", "Math", "round", "confidence", "should_start_session", "is_in_session", "isStale", "result", "reason", "frame_count", "error", "onclose", "setTimeout", "onerror", "disconnect", "clearTimeout", "clearInterval", "close", "sendFrame", "frameData", "readyState", "OPEN", "message", "frame", "send", "stringify", "startRecording", "targetSignName", "stopRecording", "startFrameCapture", "webcamRef", "interval", "setInterval", "imageSrc", "getScreenshot", "stopFrameCapture", "retryConnection"], "sources": ["D:/ASL/training-frontend/src/hooks/useSignDetection.js"], "sourcesContent": ["import { useState, useRef, useCallback, useEffect } from 'react';\n\nconst BACKEND_URL = 'ws://localhost:8001/ws/detect';\n\nexport const useSignDetection = () => {\n  const [isConnected, setIsConnected] = useState(false);\n  const [prediction, setPrediction] = useState(null);\n  const [lastPrediction, setLastPrediction] = useState(null);\n  const [isRecording, setIsRecording] = useState(false);\n  const [recordingStatus, setRecordingStatus] = useState('');\n  const [processedFrame, setProcessedFrame] = useState(null);\n  const [signMatched, setSignMatched] = useState(false);\n  const [targetSign, setTargetSign] = useState('');\n  const [predictionHistory, setPredictionHistory] = useState([]);\n  \n  const wsRef = useRef(null);\n  const reconnectTimeoutRef = useRef(null);\n  const frameIntervalRef = useRef(null);\n  \n  const connect = useCallback(() => {\n    try {\n      wsRef.current = new WebSocket(BACKEND_URL);\n      \n      wsRef.current.onopen = () => {\n        console.log('Connected to sign detection backend');\n        setIsConnected(true);\n        setRecordingStatus('');\n      };\n      \n      wsRef.current.onmessage = (event) => {\n        try {\n          const data = JSON.parse(event.data);\n          console.log('Received from backend:', data.type, data.prediction);\n\n          switch (data.type) {\n            case 'frame_processed':\n              // Always update processed frame\n              setProcessedFrame(data.processed_frame);\n              setSignMatched(data.sign_matched || false);\n              setTargetSign(data.target_sign || '');\n\n              // Update prediction - keep last prediction if new one is null\n              if (data.prediction) {\n                setPrediction(data.prediction);\n                setLastPrediction(data.prediction);\n\n                // Add to prediction history (keep last 5)\n                setPredictionHistory(prev => {\n                  const newHistory = [data.prediction, ...prev.slice(0, 4)];\n                  return newHistory;\n                });\n\n                console.log(`Detected: ${data.prediction.sign} (${Math.round(data.prediction.confidence * 100)}%)`);\n                \n                // Show recording session status\n                if (data.should_start_session) {\n                  setRecordingStatus(`🎬 Started recording session for ${data.target_sign} (3 seconds)`);\n                } else if (data.is_in_session && data.sign_matched) {\n                  setRecordingStatus(`📹 Recording session active for ${data.target_sign}...`);\n                }\n              } else if (lastPrediction) {\n                // Keep showing last prediction with reduced opacity\n                setPrediction({\n                  ...lastPrediction,\n                  confidence: lastPrediction.confidence * 0.7,\n                  isStale: true\n                });\n              }\n              break;\n              \n            case 'recording_started':\n              setIsRecording(true);\n              setRecordingStatus(`Ready to record: ${data.target_sign} (will auto-record for 3s when detected)`);\n              break;\n              \n            case 'recording_stopped':\n              setIsRecording(false);\n              if (data.result) {\n                const reason = data.reason === 'auto_stop_session_complete' ? 'Session completed' : 'Manual stop';\n                setRecordingStatus(`✅ Recording saved: ${data.result.frame_count} frames (${reason})`);\n              } else {\n                setRecordingStatus('Recording stopped');\n              }\n              break;\n              \n            default:\n              console.log('Unknown message type:', data.type);\n          }\n        } catch (error) {\n          console.error('Error parsing WebSocket message:', error);\n        }\n      };\n      \n      wsRef.current.onclose = () => {\n        console.log('Disconnected from sign detection backend');\n        setIsConnected(false);\n        setRecordingStatus('Connection lost - Click retry to reconnect');\n\n        // Attempt to reconnect after 3 seconds\n        reconnectTimeoutRef.current = setTimeout(() => {\n          console.log('Attempting to reconnect...');\n          connect();\n        }, 3000);\n      };\n      \n      wsRef.current.onerror = (error) => {\n        console.error('WebSocket error:', error);\n        setRecordingStatus('Connection error');\n      };\n      \n    } catch (error) {\n      console.error('Error connecting to WebSocket:', error);\n      setRecordingStatus('Failed to connect to backend');\n    }\n  }, []);\n  \n  const disconnect = useCallback(() => {\n    if (reconnectTimeoutRef.current) {\n      clearTimeout(reconnectTimeoutRef.current);\n    }\n    \n    if (frameIntervalRef.current) {\n      clearInterval(frameIntervalRef.current);\n    }\n    \n    if (wsRef.current) {\n      wsRef.current.close();\n      wsRef.current = null;\n    }\n    \n    setIsConnected(false);\n  }, []);\n  \n  const sendFrame = useCallback((frameData) => {\n    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {\n      const message = {\n        type: 'frame',\n        frame: frameData\n      };\n      wsRef.current.send(JSON.stringify(message));\n    }\n  }, []);\n  \n  const startRecording = useCallback((targetSignName) => {\n    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {\n      const message = {\n        type: 'start_recording',\n        target_sign: targetSignName\n      };\n      wsRef.current.send(JSON.stringify(message));\n      setTargetSign(targetSignName);\n    }\n  }, []);\n  \n  const stopRecording = useCallback(() => {\n    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {\n      const message = {\n        type: 'stop_recording'\n      };\n      wsRef.current.send(JSON.stringify(message));\n    }\n  }, []);\n  \n  const startFrameCapture = useCallback((webcamRef, interval = 200) => {\n    if (frameIntervalRef.current) {\n      clearInterval(frameIntervalRef.current);\n    }\n\n    frameIntervalRef.current = setInterval(() => {\n      if (webcamRef.current && isConnected) {\n        const imageSrc = webcamRef.current.getScreenshot();\n        if (imageSrc) {\n          console.log('Sending frame to backend');\n          sendFrame(imageSrc);\n        }\n      }\n    }, interval);\n  }, [isConnected, sendFrame]);\n  \n  const stopFrameCapture = useCallback(() => {\n    if (frameIntervalRef.current) {\n      clearInterval(frameIntervalRef.current);\n      frameIntervalRef.current = null;\n    }\n  }, []);\n  \n  // Auto-connect on mount\n  useEffect(() => {\n    connect();\n    \n    return () => {\n      disconnect();\n    };\n  }, [connect, disconnect]);\n  \n  const retryConnection = useCallback(() => {\n    setRecordingStatus('Attempting to reconnect...');\n    disconnect();\n    setTimeout(() => {\n      connect();\n    }, 1000);\n  }, [connect, disconnect]);\n\n  return {\n    isConnected,\n    prediction,\n    lastPrediction,\n    predictionHistory,\n    isRecording,\n    recordingStatus,\n    processedFrame,\n    signMatched,\n    targetSign,\n    connect,\n    disconnect,\n    retryConnection,\n    sendFrame,\n    startRecording,\n    stopRecording,\n    startFrameCapture,\n    stopFrameCapture\n  };\n};\n"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,MAAM,EAAEC,WAAW,EAAEC,SAAS,QAAQ,OAAO;AAEhE,MAAMC,WAAW,GAAG,+BAA+B;AAEnD,OAAO,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGR,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACS,UAAU,EAAEC,aAAa,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACW,cAAc,EAAEC,iBAAiB,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACa,WAAW,EAAEC,cAAc,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACe,eAAe,EAAEC,kBAAkB,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACiB,cAAc,EAAEC,iBAAiB,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACmB,WAAW,EAAEC,cAAc,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACqB,UAAU,EAAEC,aAAa,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACuB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAE9D,MAAMyB,KAAK,GAAGxB,MAAM,CAAC,IAAI,CAAC;EAC1B,MAAMyB,mBAAmB,GAAGzB,MAAM,CAAC,IAAI,CAAC;EACxC,MAAM0B,gBAAgB,GAAG1B,MAAM,CAAC,IAAI,CAAC;EAErC,MAAM2B,OAAO,GAAG1B,WAAW,CAAC,MAAM;IAChC,IAAI;MACFuB,KAAK,CAACI,OAAO,GAAG,IAAIC,SAAS,CAAC1B,WAAW,CAAC;MAE1CqB,KAAK,CAACI,OAAO,CAACE,MAAM,GAAG,MAAM;QAC3BC,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;QAClDzB,cAAc,CAAC,IAAI,CAAC;QACpBQ,kBAAkB,CAAC,EAAE,CAAC;MACxB,CAAC;MAEDS,KAAK,CAACI,OAAO,CAACK,SAAS,GAAIC,KAAK,IAAK;QACnC,IAAI;UACF,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,KAAK,CAACC,IAAI,CAAC;UACnCJ,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEG,IAAI,CAACG,IAAI,EAAEH,IAAI,CAAC3B,UAAU,CAAC;UAEjE,QAAQ2B,IAAI,CAACG,IAAI;YACf,KAAK,iBAAiB;cACpB;cACArB,iBAAiB,CAACkB,IAAI,CAACI,eAAe,CAAC;cACvCpB,cAAc,CAACgB,IAAI,CAACK,YAAY,IAAI,KAAK,CAAC;cAC1CnB,aAAa,CAACc,IAAI,CAACM,WAAW,IAAI,EAAE,CAAC;;cAErC;cACA,IAAIN,IAAI,CAAC3B,UAAU,EAAE;gBACnBC,aAAa,CAAC0B,IAAI,CAAC3B,UAAU,CAAC;gBAC9BG,iBAAiB,CAACwB,IAAI,CAAC3B,UAAU,CAAC;;gBAElC;gBACAe,oBAAoB,CAACmB,IAAI,IAAI;kBAC3B,MAAMC,UAAU,GAAG,CAACR,IAAI,CAAC3B,UAAU,EAAE,GAAGkC,IAAI,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;kBACzD,OAAOD,UAAU;gBACnB,CAAC,CAAC;gBAEFZ,OAAO,CAACC,GAAG,CAAC,aAAaG,IAAI,CAAC3B,UAAU,CAACqC,IAAI,KAAKC,IAAI,CAACC,KAAK,CAACZ,IAAI,CAAC3B,UAAU,CAACwC,UAAU,GAAG,GAAG,CAAC,IAAI,CAAC;;gBAEnG;gBACA,IAAIb,IAAI,CAACc,oBAAoB,EAAE;kBAC7BlC,kBAAkB,CAAC,oCAAoCoB,IAAI,CAACM,WAAW,cAAc,CAAC;gBACxF,CAAC,MAAM,IAAIN,IAAI,CAACe,aAAa,IAAIf,IAAI,CAACK,YAAY,EAAE;kBAClDzB,kBAAkB,CAAC,mCAAmCoB,IAAI,CAACM,WAAW,KAAK,CAAC;gBAC9E;cACF,CAAC,MAAM,IAAI/B,cAAc,EAAE;gBACzB;gBACAD,aAAa,CAAC;kBACZ,GAAGC,cAAc;kBACjBsC,UAAU,EAAEtC,cAAc,CAACsC,UAAU,GAAG,GAAG;kBAC3CG,OAAO,EAAE;gBACX,CAAC,CAAC;cACJ;cACA;YAEF,KAAK,mBAAmB;cACtBtC,cAAc,CAAC,IAAI,CAAC;cACpBE,kBAAkB,CAAC,oBAAoBoB,IAAI,CAACM,WAAW,0CAA0C,CAAC;cAClG;YAEF,KAAK,mBAAmB;cACtB5B,cAAc,CAAC,KAAK,CAAC;cACrB,IAAIsB,IAAI,CAACiB,MAAM,EAAE;gBACf,MAAMC,MAAM,GAAGlB,IAAI,CAACkB,MAAM,KAAK,4BAA4B,GAAG,mBAAmB,GAAG,aAAa;gBACjGtC,kBAAkB,CAAC,sBAAsBoB,IAAI,CAACiB,MAAM,CAACE,WAAW,YAAYD,MAAM,GAAG,CAAC;cACxF,CAAC,MAAM;gBACLtC,kBAAkB,CAAC,mBAAmB,CAAC;cACzC;cACA;YAEF;cACEgB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEG,IAAI,CAACG,IAAI,CAAC;UACnD;QACF,CAAC,CAAC,OAAOiB,KAAK,EAAE;UACdxB,OAAO,CAACwB,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QAC1D;MACF,CAAC;MAED/B,KAAK,CAACI,OAAO,CAAC4B,OAAO,GAAG,MAAM;QAC5BzB,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;QACvDzB,cAAc,CAAC,KAAK,CAAC;QACrBQ,kBAAkB,CAAC,4CAA4C,CAAC;;QAEhE;QACAU,mBAAmB,CAACG,OAAO,GAAG6B,UAAU,CAAC,MAAM;UAC7C1B,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;UACzCL,OAAO,CAAC,CAAC;QACX,CAAC,EAAE,IAAI,CAAC;MACV,CAAC;MAEDH,KAAK,CAACI,OAAO,CAAC8B,OAAO,GAAIH,KAAK,IAAK;QACjCxB,OAAO,CAACwB,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;QACxCxC,kBAAkB,CAAC,kBAAkB,CAAC;MACxC,CAAC;IAEH,CAAC,CAAC,OAAOwC,KAAK,EAAE;MACdxB,OAAO,CAACwB,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtDxC,kBAAkB,CAAC,8BAA8B,CAAC;IACpD;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM4C,UAAU,GAAG1D,WAAW,CAAC,MAAM;IACnC,IAAIwB,mBAAmB,CAACG,OAAO,EAAE;MAC/BgC,YAAY,CAACnC,mBAAmB,CAACG,OAAO,CAAC;IAC3C;IAEA,IAAIF,gBAAgB,CAACE,OAAO,EAAE;MAC5BiC,aAAa,CAACnC,gBAAgB,CAACE,OAAO,CAAC;IACzC;IAEA,IAAIJ,KAAK,CAACI,OAAO,EAAE;MACjBJ,KAAK,CAACI,OAAO,CAACkC,KAAK,CAAC,CAAC;MACrBtC,KAAK,CAACI,OAAO,GAAG,IAAI;IACtB;IAEArB,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMwD,SAAS,GAAG9D,WAAW,CAAE+D,SAAS,IAAK;IAC3C,IAAIxC,KAAK,CAACI,OAAO,IAAIJ,KAAK,CAACI,OAAO,CAACqC,UAAU,KAAKpC,SAAS,CAACqC,IAAI,EAAE;MAChE,MAAMC,OAAO,GAAG;QACd7B,IAAI,EAAE,OAAO;QACb8B,KAAK,EAAEJ;MACT,CAAC;MACDxC,KAAK,CAACI,OAAO,CAACyC,IAAI,CAACjC,IAAI,CAACkC,SAAS,CAACH,OAAO,CAAC,CAAC;IAC7C;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,cAAc,GAAGtE,WAAW,CAAEuE,cAAc,IAAK;IACrD,IAAIhD,KAAK,CAACI,OAAO,IAAIJ,KAAK,CAACI,OAAO,CAACqC,UAAU,KAAKpC,SAAS,CAACqC,IAAI,EAAE;MAChE,MAAMC,OAAO,GAAG;QACd7B,IAAI,EAAE,iBAAiB;QACvBG,WAAW,EAAE+B;MACf,CAAC;MACDhD,KAAK,CAACI,OAAO,CAACyC,IAAI,CAACjC,IAAI,CAACkC,SAAS,CAACH,OAAO,CAAC,CAAC;MAC3C9C,aAAa,CAACmD,cAAc,CAAC;IAC/B;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,aAAa,GAAGxE,WAAW,CAAC,MAAM;IACtC,IAAIuB,KAAK,CAACI,OAAO,IAAIJ,KAAK,CAACI,OAAO,CAACqC,UAAU,KAAKpC,SAAS,CAACqC,IAAI,EAAE;MAChE,MAAMC,OAAO,GAAG;QACd7B,IAAI,EAAE;MACR,CAAC;MACDd,KAAK,CAACI,OAAO,CAACyC,IAAI,CAACjC,IAAI,CAACkC,SAAS,CAACH,OAAO,CAAC,CAAC;IAC7C;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMO,iBAAiB,GAAGzE,WAAW,CAAC,CAAC0E,SAAS,EAAEC,QAAQ,GAAG,GAAG,KAAK;IACnE,IAAIlD,gBAAgB,CAACE,OAAO,EAAE;MAC5BiC,aAAa,CAACnC,gBAAgB,CAACE,OAAO,CAAC;IACzC;IAEAF,gBAAgB,CAACE,OAAO,GAAGiD,WAAW,CAAC,MAAM;MAC3C,IAAIF,SAAS,CAAC/C,OAAO,IAAItB,WAAW,EAAE;QACpC,MAAMwE,QAAQ,GAAGH,SAAS,CAAC/C,OAAO,CAACmD,aAAa,CAAC,CAAC;QAClD,IAAID,QAAQ,EAAE;UACZ/C,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;UACvC+B,SAAS,CAACe,QAAQ,CAAC;QACrB;MACF;IACF,CAAC,EAAEF,QAAQ,CAAC;EACd,CAAC,EAAE,CAACtE,WAAW,EAAEyD,SAAS,CAAC,CAAC;EAE5B,MAAMiB,gBAAgB,GAAG/E,WAAW,CAAC,MAAM;IACzC,IAAIyB,gBAAgB,CAACE,OAAO,EAAE;MAC5BiC,aAAa,CAACnC,gBAAgB,CAACE,OAAO,CAAC;MACvCF,gBAAgB,CAACE,OAAO,GAAG,IAAI;IACjC;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA1B,SAAS,CAAC,MAAM;IACdyB,OAAO,CAAC,CAAC;IAET,OAAO,MAAM;MACXgC,UAAU,CAAC,CAAC;IACd,CAAC;EACH,CAAC,EAAE,CAAChC,OAAO,EAAEgC,UAAU,CAAC,CAAC;EAEzB,MAAMsB,eAAe,GAAGhF,WAAW,CAAC,MAAM;IACxCc,kBAAkB,CAAC,4BAA4B,CAAC;IAChD4C,UAAU,CAAC,CAAC;IACZF,UAAU,CAAC,MAAM;MACf9B,OAAO,CAAC,CAAC;IACX,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,EAAE,CAACA,OAAO,EAAEgC,UAAU,CAAC,CAAC;EAEzB,OAAO;IACLrD,WAAW;IACXE,UAAU;IACVE,cAAc;IACdY,iBAAiB;IACjBV,WAAW;IACXE,eAAe;IACfE,cAAc;IACdE,WAAW;IACXE,UAAU;IACVO,OAAO;IACPgC,UAAU;IACVsB,eAAe;IACflB,SAAS;IACTQ,cAAc;IACdE,aAAa;IACbC,iBAAiB;IACjBM;EACF,CAAC;AACH,CAAC;AAAC3E,EAAA,CA1NWD,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}